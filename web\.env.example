# NeuroCare Web Application Environment Variables
# Copy this file to .env and fill in your Firebase configuration

# Firebase Configuration
REACT_APP_FIREBASE_API_KEY=your_firebase_api_key_here
REACT_APP_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your_project_id
REACT_APP_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
REACT_APP_FIREBASE_APP_ID=your_app_id
REACT_APP_FIREBASE_MEASUREMENT_ID=your_measurement_id

# Application Configuration
REACT_APP_APP_NAME=NeuroCare
REACT_APP_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development

# API Configuration (if needed for future features)
REACT_APP_API_BASE_URL=https://api.neurocare.com
REACT_APP_API_VERSION=v1

# Feature Flags (for enabling/disabling features)
REACT_APP_ENABLE_VIDEO_CALLS=false
REACT_APP_ENABLE_CHAT=false
REACT_APP_ENABLE_GPS_TRACKING=false
REACT_APP_ENABLE_VOICE_COMMANDS=false

# Debug and Development
REACT_APP_DEBUG_MODE=true
REACT_APP_SHOW_CONSOLE_LOGS=true
