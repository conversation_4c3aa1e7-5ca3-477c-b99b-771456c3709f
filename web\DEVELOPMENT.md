# Development Guide - NeuroCare Web Application

Guide de développement pour contribuer à l'application web NeuroCare.

## 🏗️ Architecture du projet

### Structure des dossiers
```
web/
├── public/                 # Fichiers statiques
├── src/
│   ├── components/        # Composants React
│   │   ├── auth/         # Authentification
│   │   ├── common/       # Composants réutilisables
│   │   ├── dashboards/   # Dashboards par rôle
│   │   ├── navigation/   # Navigation et routing
│   │   ├── notifications/# Notifications
│   │   └── profile/      # Gestion profil
│   ├── contexts/         # Contextes React
│   ├── hooks/            # Hooks personnalisés
│   ├── services/         # Services API
│   ├── utils/            # Utilitaires
│   ├── config/           # Configuration
│   └── styles/           # Styles globaux
├── .env.example          # Variables d'environnement
├── package.json          # Dépendances
└── README.md            # Documentation
```

### Technologies utilisées

#### Core
- **React 18** : Framework principal
- **React Router v6** : Navigation
- **Styled Components** : CSS-in-JS
- **Firebase v9** : Backend et authentification

#### Utilitaires
- **React Context** : Gestion d'état
- **React Toastify** : Notifications
- **QRCode.js** : Génération QR codes
- **Date-fns** : Manipulation des dates

## 🎨 Système de design

### Thème et couleurs

#### Couleurs par rôle
```javascript
const roleColors = {
  patient: { primary: '#4CAF50', secondary: '#81C784' },
  doctor: { primary: '#2196F3', secondary: '#64B5F6' },
  admin: { primary: '#9C27B0', secondary: '#BA68C8' },
  caregiver: { primary: '#FF9800', secondary: '#FFB74D' },
  supervisor: { primary: '#E91E63', secondary: '#F06292' }
};
```

#### Palette générale
```javascript
const colors = {
  primary: '#4CAF50',
  primaryLight: '#81C784',
  primaryDark: '#388E3C',
  text: '#212121',
  textSecondary: '#757575',
  background: '#FAFAFA',
  lightGray: '#F5F5F5',
  border: '#E0E0E0',
  borderLight: '#F0F0F0'
};
```

### Composants de base

#### Button
```jsx
import { Button, PrimaryButton, SecondaryButton } from '../components/common';

<Button variant="primary" size="large" loading={isLoading}>
  Submit
</Button>
```

#### Input
```jsx
import { Input, EmailInput, PasswordInput } from '../components/common';

<Input
  label="Full Name"
  placeholder="Enter your name"
  error={errors.name}
  required
/>
```

#### Card
```jsx
import { Card, StatCard } from '../components/common';

<Card title="Patient Info" elevation="medium">
  <p>Card content</p>
</Card>
```

## 🔧 Développement

### Conventions de code

#### Nommage
- **Composants** : PascalCase (`UserProfile.js`)
- **Hooks** : camelCase avec préfixe `use` (`useAuth.js`)
- **Utilitaires** : camelCase (`dateUtils.js`)
- **Constants** : UPPER_SNAKE_CASE (`USER_ROLES`)

#### Structure des composants
```jsx
import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../contexts/ThemeContext';

// Styled components
const Container = styled.div`
  /* styles */
`;

// Component
const MyComponent = ({ prop1, prop2, ...props }) => {
  const { theme } = useTheme();
  const [state, setState] = useState(null);

  useEffect(() => {
    // effects
  }, []);

  const handleAction = () => {
    // handlers
  };

  return (
    <Container theme={theme} {...props}>
      {/* JSX */}
    </Container>
  );
};

export default MyComponent;
```

### Gestion d'état

#### Contextes React
```jsx
// Création d'un contexte
const MyContext = createContext();

export const MyProvider = ({ children }) => {
  const [state, setState] = useState(initialState);
  
  const value = {
    state,
    setState,
    // actions
  };

  return (
    <MyContext.Provider value={value}>
      {children}
    </MyContext.Provider>
  );
};

// Hook personnalisé
export const useMyContext = () => {
  const context = useContext(MyContext);
  if (!context) {
    throw new Error('useMyContext must be used within MyProvider');
  }
  return context;
};
```

### Services et API

#### Service Firebase
```jsx
import { FirebaseService } from '../services';

// Créer un service pour une collection
const myService = new FirebaseService('myCollection');

// Utiliser le service
const data = await myService.getAll();
const item = await myService.getById(id);
const newId = await myService.create(data);
await myService.update(id, updates);
await myService.delete(id);
```

#### Hook API
```jsx
import { useApi } from '../hooks';

const MyComponent = () => {
  const { data, loading, error, execute } = useApi(
    () => myService.getAll(),
    [], // initial data
    true // immediate execution
  );

  return (
    <div>
      {loading && <p>Loading...</p>}
      {error && <p>Error: {error.message}</p>}
      {data && <p>Data loaded: {data.length} items</p>}
    </div>
  );
};
```

### Formulaires

#### Hook useForm
```jsx
import { useForm } from '../hooks';
import { validateEmail, validateRequired } from '../utils/validation';

const MyForm = () => {
  const {
    values,
    errors,
    handleChange,
    handleSubmit,
    getFieldProps
  } = useForm(
    { email: '', name: '' }, // initial values
    {
      email: validateEmail,
      name: (value) => validateRequired(value, 'Name')
    }, // validators
    async (data) => {
      // submit handler
      await submitData(data);
    }
  );

  return (
    <form onSubmit={handleSubmit}>
      <Input {...getFieldProps('name')} label="Name" />
      <Input {...getFieldProps('email')} label="Email" type="email" />
      <Button type="submit">Submit</Button>
    </form>
  );
};
```

## 🧪 Tests

### Structure des tests
```
src/
├── components/
│   └── __tests__/
│       ├── Button.test.js
│       └── Input.test.js
├── hooks/
│   └── __tests__/
│       └── useForm.test.js
└── utils/
    └── __tests__/
        └── validation.test.js
```

### Exemple de test
```jsx
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '../contexts/ThemeContext';
import Button from '../components/common/Button';

const renderWithTheme = (component) => {
  return render(
    <ThemeProvider>
      {component}
    </ThemeProvider>
  );
};

describe('Button', () => {
  test('renders button with text', () => {
    renderWithTheme(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  test('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    renderWithTheme(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

## 🚀 Déploiement

### Environnements

#### Développement
```bash
npm start
# http://localhost:3000
```

#### Staging
```bash
npm run build:staging
firebase deploy --project staging
```

#### Production
```bash
npm run build
firebase deploy --project production
```

### Variables d'environnement

#### .env.development
```env
REACT_APP_ENVIRONMENT=development
REACT_APP_DEBUG_MODE=true
REACT_APP_API_BASE_URL=http://localhost:3000
```

#### .env.production
```env
REACT_APP_ENVIRONMENT=production
REACT_APP_DEBUG_MODE=false
REACT_APP_API_BASE_URL=https://api.neurocare.com
```

## 📋 Workflow de développement

### Git Flow

#### Branches
- `main` : Production
- `develop` : Développement
- `feature/*` : Nouvelles fonctionnalités
- `bugfix/*` : Corrections de bugs
- `hotfix/*` : Corrections urgentes

#### Processus
1. Créer une branche feature depuis develop
2. Développer la fonctionnalité
3. Créer une Pull Request vers develop
4. Code review et tests
5. Merge vers develop
6. Deploy sur staging pour tests
7. Merge vers main pour production

### Commits

#### Convention
```
type(scope): description

feat(auth): add password reset functionality
fix(dashboard): resolve loading state issue
docs(readme): update installation guide
style(button): improve hover animations
refactor(api): simplify error handling
test(form): add validation tests
```

### Code Review

#### Checklist
- [ ] Code suit les conventions du projet
- [ ] Tests ajoutés/mis à jour
- [ ] Documentation mise à jour
- [ ] Pas de console.log oubliés
- [ ] Performance optimisée
- [ ] Accessibilité respectée
- [ ] Responsive design testé

## 🔍 Debugging

### Outils de développement

#### React DevTools
- Installer l'extension navigateur
- Inspecter les composants et props
- Profiler les performances

#### Firebase Emulator
```bash
firebase emulators:start
```
- Tester localement sans affecter la production
- Débugger les règles Firestore

#### Console logs
```jsx
// Utiliser le service de debug
import { debugLog } from '../utils/helpers';

debugLog('User data:', userData);
```

### Erreurs communes

#### "Cannot read property of undefined"
```jsx
// Mauvais
const name = user.profile.name;

// Bon
const name = user?.profile?.name || 'Unknown';
```

#### "Memory leak" warnings
```jsx
// Nettoyer les subscriptions
useEffect(() => {
  const unsubscribe = subscribe();
  return () => unsubscribe();
}, []);
```

## 📚 Ressources

### Documentation
- [React Docs](https://reactjs.org/docs/)
- [Firebase Docs](https://firebase.google.com/docs/)
- [Styled Components](https://styled-components.com/docs/)

### Outils
- [VS Code Extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next)
- [React DevTools](https://chrome.google.com/webstore/detail/react-developer-tools/)
- [Firebase Emulator](https://firebase.google.com/docs/emulator-suite)

### Standards
- [Airbnb React Style Guide](https://github.com/airbnb/javascript/tree/master/react)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Conventional Commits](https://www.conventionalcommits.org/)
