# Installation Guide - NeuroCare Web Application

Ce guide vous accompagne dans l'installation et la configuration de l'application web NeuroCare.

## 📋 Prérequis

### Logiciels requis
- **Node.js** (version 16 ou supérieure)
  - Télécharger depuis [nodejs.org](https://nodejs.org/)
  - Vérifier l'installation : `node --version`
- **npm** (inclus avec Node.js)
  - Vérifier l'installation : `npm --version`
- **Git** (optionnel, pour le développement)
  - Télécharger depuis [git-scm.com](https://git-scm.com/)

### Compte Firebase
- Créer un compte sur [Firebase Console](https://console.firebase.google.com/)
- Créer un nouveau projet Firebase

## 🚀 Installation rapide

### Option 1 : Script automatique (Recommandé)

#### Windows
```bash
# Double-cliquer sur le fichier start.bat
# OU exécuter dans le terminal :
start.bat
```

#### Linux/Mac
```bash
# Rendre le script exécutable
chmod +x start.sh

# Exécuter le script
./start.sh
```

### Option 2 : Installation manuelle

1. **Naviguer vers le dossier web**
   ```bash
   cd web
   ```

2. **Installer les dépendances**
   ```bash
   npm install
   ```

3. **Configurer Firebase** (voir section Configuration)

4. **Démarrer l'application**
   ```bash
   npm start
   ```

## ⚙️ Configuration Firebase

### 1. Créer un projet Firebase

1. Aller sur [Firebase Console](https://console.firebase.google.com/)
2. Cliquer sur "Ajouter un projet"
3. Suivre les étapes de création
4. Activer Google Analytics (optionnel)

### 2. Configurer Authentication

1. Dans la console Firebase, aller dans "Authentication"
2. Cliquer sur "Commencer"
3. Dans l'onglet "Sign-in method", activer :
   - **Email/Password**
   - **Google** (optionnel)
4. Dans l'onglet "Settings", configurer :
   - Nom du projet
   - Email de support

### 3. Configurer Firestore Database

1. Aller dans "Firestore Database"
2. Cliquer sur "Créer une base de données"
3. Choisir "Commencer en mode test" (pour le développement)
4. Sélectionner une région proche de vos utilisateurs

### 4. Obtenir les clés de configuration

1. Aller dans "Project Settings" (icône engrenage)
2. Dans l'onglet "General", descendre jusqu'à "Your apps"
3. Cliquer sur "Add app" et choisir "Web" (icône </>)
4. Enregistrer l'app avec un nom
5. Copier la configuration Firebase

### 5. Configurer l'application

1. **Copier le fichier d'environnement**
   ```bash
   cp .env.example .env
   ```

2. **Éditer le fichier .env** avec vos clés Firebase :
   ```env
   REACT_APP_FIREBASE_API_KEY=your_api_key_here
   REACT_APP_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
   REACT_APP_FIREBASE_PROJECT_ID=your_project_id
   REACT_APP_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
   REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   REACT_APP_FIREBASE_APP_ID=your_app_id
   ```

## 🔧 Configuration avancée

### Règles de sécurité Firestore

Remplacer les règles par défaut dans Firestore par :

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Appointments - patients and doctors can access
    match /appointments/{appointmentId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.patientId || 
         request.auth.uid == resource.data.doctorId);
    }
    
    // Medications - only patient and their doctors
    match /medications/{medicationId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.patientId;
    }
    
    // Notifications - only for the user
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
  }
}
```

### Configuration des domaines autorisés

1. Dans Firebase Console, aller dans "Authentication"
2. Onglet "Settings"
3. Ajouter votre domaine dans "Authorized domains"
   - `localhost` (pour le développement)
   - Votre domaine de production

## 🏃‍♂️ Démarrage

### Mode développement
```bash
npm start
```
- Ouvre automatiquement http://localhost:3000
- Rechargement automatique lors des modifications
- Affichage des erreurs en temps réel

### Build de production
```bash
npm run build
```
- Crée un dossier `build/` optimisé
- Fichiers minifiés et optimisés
- Prêt pour le déploiement

### Tests
```bash
npm test
```
- Lance les tests en mode interactif
- Surveillance des changements

## 🌐 Déploiement

### Firebase Hosting (Recommandé)

1. **Installer Firebase CLI**
   ```bash
   npm install -g firebase-tools
   ```

2. **Se connecter à Firebase**
   ```bash
   firebase login
   ```

3. **Initialiser Firebase dans le projet**
   ```bash
   firebase init hosting
   ```
   - Choisir le projet Firebase
   - Dossier public : `build`
   - SPA : `Yes`
   - Réécrire index.html : `No`

4. **Builder et déployer**
   ```bash
   npm run build
   firebase deploy
   ```

### Autres options de déploiement

- **Netlify** : Glisser-déposer le dossier `build/`
- **Vercel** : Connecter le repository GitHub
- **GitHub Pages** : Utiliser `gh-pages` package

## 🔍 Dépannage

### Erreurs communes

#### "Module not found"
```bash
# Supprimer node_modules et réinstaller
rm -rf node_modules package-lock.json
npm install
```

#### "Firebase configuration error"
- Vérifier que toutes les variables d'environnement sont définies
- Vérifier que les clés Firebase sont correctes
- Redémarrer le serveur de développement

#### "Permission denied" sur Firebase
- Vérifier les règles de sécurité Firestore
- Vérifier que l'utilisateur est authentifié
- Vérifier les domaines autorisés

#### Port 3000 déjà utilisé
```bash
# Utiliser un autre port
PORT=3001 npm start
```

### Logs et debugging

#### Activer les logs détaillés
```env
REACT_APP_DEBUG_MODE=true
REACT_APP_SHOW_CONSOLE_LOGS=true
```

#### Outils de développement
- React Developer Tools (extension navigateur)
- Redux DevTools (si utilisé)
- Firebase Emulator Suite (pour les tests locaux)

## 📚 Ressources supplémentaires

### Documentation
- [React Documentation](https://reactjs.org/docs/)
- [Firebase Documentation](https://firebase.google.com/docs/)
- [Styled Components](https://styled-components.com/docs/)

### Support
- Issues GitHub du projet
- Documentation Firebase
- Stack Overflow avec tags `reactjs` et `firebase`

### Outils utiles
- [Firebase Emulator Suite](https://firebase.google.com/docs/emulator-suite)
- [React Developer Tools](https://chrome.google.com/webstore/detail/react-developer-tools/)
- [VS Code Extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next)

## ✅ Vérification de l'installation

Une fois l'installation terminée, vous devriez pouvoir :

1. ✅ Accéder à l'application sur http://localhost:3000
2. ✅ Créer un nouveau compte utilisateur
3. ✅ Se connecter avec les identifiants
4. ✅ Accéder au dashboard selon votre rôle
5. ✅ Voir les données se synchroniser avec Firebase

Si tous ces points fonctionnent, votre installation est réussie ! 🎉
