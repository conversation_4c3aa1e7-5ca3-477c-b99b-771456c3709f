# NeuroCare Web Application

Une conversion fidèle de l'application mobile NeuroCare vers une application web React.

## 🚀 Fonctionnalités

### ✅ Implémentées
- **Authentification complète** : Connexion, inscription, mot de passe oublié
- **5 Dashboards spécialisés** selon les rôles utilisateur :
  - 👤 **Patient Dashboard** : Gestion des rendez-vous, médicaments, signes vitaux
  - 👨‍⚕️ **Doctor Dashboard** : Consultation patients, prescriptions, appels vidéo
  - 🛡️ **Admin Dashboard** : Gestion utilisateurs, analytics système
  - 🤝 **Caregiver Dashboard** : Suivi patients, assistance navigation
  - 👁️ **Supervisor Dashboard** : Supervision globale, tracking temps réel
- **Gestion de profil** : Profil complet, QR Code utilisateur
- **Système de thème** : Couleurs spécifiques par rôle, mode sombre
- **Notifications** : Système de notifications intégré
- **Responsive Design** : Interface adaptative mobile/desktop

### 🔄 En cours de développement
- Gestion des rendez-vous
- Prescriptions et médicaments
- Enregistrement des signes vitaux
- Chat et messagerie sécurisée
- Consultations vidéo
- Scanner QR Code
- Navigation GPS

## 🏗️ Architecture

### Structure du projet
```
web/
├── public/                 # Fichiers statiques
├── src/
│   ├── components/        # Composants React
│   │   ├── auth/         # Authentification
│   │   ├── dashboards/   # Dashboards par rôle
│   │   ├── common/       # Composants réutilisables
│   │   ├── profile/      # Gestion profil
│   │   ├── notifications/# Notifications
│   │   └── navigation/   # Navigation et routing
│   ├── contexts/         # Contextes React
│   │   ├── AuthContext.js
│   │   ├── ThemeContext.js
│   │   ├── AppointmentContext.js
│   │   ├── MedicationContext.js
│   │   └── VitalsContext.js
│   ├── config/           # Configuration
│   │   ├── firebase.js
│   │   └── theme.js
│   ├── styles/           # Styles globaux
│   └── utils/            # Utilitaires
```

### Technologies utilisées
- **React 18** : Framework principal
- **React Router** : Navigation
- **Styled Components** : Styling CSS-in-JS
- **Firebase** : Backend et authentification
- **React Context** : Gestion d'état
- **React Toastify** : Notifications
- **QRCode.js** : Génération QR codes

## 🎨 Système de thème

### Couleurs par rôle
- **Patient** : Vert (#4CAF50)
- **Docteur** : Bleu (#2196F3)
- **Admin** : Violet (#9C27B0)
- **Caregiver** : Orange (#FF9800)
- **Supervisor** : Rose/Magenta (#E91E63)

### Composants de thème
- `ThemeProvider` : Fournisseur de thème global
- `useTheme` : Hook pour accéder au thème
- Thèmes adaptatifs selon le rôle utilisateur
- Support mode sombre

## 🔐 Authentification

### Fonctionnalités
- Inscription avec validation email
- Connexion sécurisée
- Réinitialisation mot de passe
- Gestion des rôles utilisateur
- Codes utilisateur uniques (8 caractères)
- Profils utilisateur complets

### Sécurité
- Authentification Firebase
- Validation côté client et serveur
- Gestion des erreurs robuste
- Sessions sécurisées

## 📱 Dashboards

### Dashboard Patient
- Vue d'ensemble santé quotidienne
- Rendez-vous à venir
- Médicaments actifs
- Signes vitaux récents
- Actions rapides (demander RDV, enregistrer signes vitaux)

### Dashboard Docteur
- Planning du jour
- Liste des patients
- Consultations en attente
- Prescriptions récentes
- Démarrage appels vidéo

### Dashboard Admin
- Statistiques système
- Gestion utilisateurs
- Analytics plateforme
- Santé système
- Logs d'activité

### Dashboard Caregiver
- Patients assignés
- Activités de soins
- Assistance navigation
- Suivi temps réel

### Dashboard Supervisor
- Vue d'ensemble supervision
- Tracking patients
- Gestion caregivers
- Alertes système
- Cartes temps réel

## 🚀 Installation et démarrage

### Prérequis
- Node.js 16+
- npm ou yarn
- Compte Firebase

### Installation
```bash
cd web
npm install
```

### Configuration Firebase
1. Créer un projet Firebase
2. Configurer Authentication
3. Configurer Firestore
4. Mettre à jour `src/config/firebase.js`

### Démarrage
```bash
npm start
```

L'application sera disponible sur `http://localhost:3000`

## 📦 Build de production

```bash
npm run build
```

## 🧪 Tests

```bash
npm test
```

## 🔧 Configuration

### Variables d'environnement
Créer un fichier `.env` :
```
REACT_APP_FIREBASE_API_KEY=your_api_key
REACT_APP_FIREBASE_AUTH_DOMAIN=your_auth_domain
REACT_APP_FIREBASE_PROJECT_ID=your_project_id
```

## 📋 Roadmap

### Phase 1 (Actuelle)
- ✅ Structure de base et authentification
- ✅ Dashboards par rôle
- ✅ Système de thème
- ✅ Gestion profil et QR codes

### Phase 2 (Prochaine)
- 🔄 Gestion complète des rendez-vous
- 🔄 Système de prescriptions
- 🔄 Enregistrement signes vitaux
- 🔄 Chat temps réel

### Phase 3 (Future)
- 📹 Consultations vidéo
- 🗺️ Navigation GPS
- 📊 Analytics avancées
- 🔔 Notifications push

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature
3. Commit les changements
4. Push vers la branche
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT.

## 📞 Support

Pour toute question ou support, contactez l'équipe de développement NeuroCare.
