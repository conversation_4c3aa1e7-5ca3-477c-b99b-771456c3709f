{"name": "neurocare-web", "version": "1.0.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "firebase": "^10.8.0", "axios": "^1.6.7", "date-fns": "^4.1.0", "react-chartjs-2": "^5.2.0", "chart.js": "^4.2.1", "qrcode": "^1.5.3", "qrcode-reader": "^1.0.4", "react-webcam": "^7.1.1", "styled-components": "^5.3.9", "react-icons": "^4.8.0", "react-modal": "^3.16.1", "react-toastify": "^9.1.2", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/qrcode": "^1.5.0", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/styled-components": "^5.1.26"}}