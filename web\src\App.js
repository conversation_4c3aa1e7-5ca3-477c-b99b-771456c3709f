import React from 'react';
import { BrowserRouter as Router } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Context Providers
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { VitalsProvider } from './contexts/VitalsContext';
import { AppointmentProvider } from './contexts/AppointmentContext';
import { MedicationProvider } from './contexts/MedicationContext';

// Main App Router
import AppRouter from './components/navigation/AppRouter';

// Global Styles
import './styles/global.css';

function App() {
  return (
    <div className="App">
      <Router>
        <ThemeProvider>
          <AuthProvider>
            <VitalsProvider>
              <AppointmentProvider>
                <MedicationProvider>
                  <AppRouter />
                  <ToastContainer
                    position="top-right"
                    autoClose={3000}
                    hideProgressBar={false}
                    newestOnTop={false}
                    closeOnClick
                    rtl={false}
                    pauseOnFocusLoss
                    draggable
                    pauseOnHover
                    theme="light"
                    style={{ zIndex: 9999 }}
                  />
                </MedicationProvider>
              </AppointmentProvider>
            </VitalsProvider>
          </AuthProvider>
        </ThemeProvider>
      </Router>
    </div>
  );
}

export default App;
