import React from 'react';
import { BrowserRouter as Router } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Context Providers
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { VitalsProvider } from './contexts/VitalsContext';
import { AppointmentProvider } from './contexts/AppointmentContext';
import { MedicationProvider } from './contexts/MedicationContext';
import { ChatProvider } from './contexts/ChatContext';

// Main App Router
import AppRouter from './components/navigation/AppRouter';
import ThemeErrorBoundary from './components/common/ThemeErrorBoundary';
import SafeThemeProvider from './components/common/SafeThemeProvider';

// Global Styles
import './styles/global.css';
import 'react-toastify/dist/ReactToastify.css';

function App() {
  return (
    <div className="App">
      <Router>
        <ThemeErrorBoundary>
          <ThemeProvider>
            <SafeThemeProvider>
              <AuthProvider>
                <ChatProvider>
                  <VitalsProvider>
                    <AppointmentProvider>
                      <MedicationProvider>
                        <AppRouter />
                        <ToastContainer
                          position="top-right"
                          autoClose={3000}
                          hideProgressBar={false}
                          newestOnTop={false}
                          closeOnClick
                          rtl={false}
                          pauseOnFocusLoss
                          draggable
                          pauseOnHover
                          theme="light"
                          style={{ zIndex: 9999 }}
                        />
                      </MedicationProvider>
                    </AppointmentProvider>
                  </VitalsProvider>
                </ChatProvider>
              </AuthProvider>
            </SafeThemeProvider>
          </ThemeProvider>
        </ThemeErrorBoundary>
      </Router>
    </div>
  );
}

export default App;
