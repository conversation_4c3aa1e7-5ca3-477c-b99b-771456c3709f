import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useAppointments } from '../../contexts/AppointmentContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Modal } from '../common';
import { formatDate, getRelativeTime } from '../../utils/dateUtils';
import { APPOINTMENT_STATUS, APPOINTMENT_STATUS_DISPLAY } from '../../utils/constants';

const DetailsContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
`;

const BackButton = styled(Button)`
  margin-bottom: 16px;
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const StatusBadge = styled.span`
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  
  ${props => {
    switch (props.status) {
      case 'scheduled':
        return `background: #E3F2FD; color: #1976D2;`;
      case 'confirmed':
        return `background: #E8F5E8; color: #2E7D32;`;
      case 'in_progress':
        return `background: #FFF3E0; color: #F57C00;`;
      case 'completed':
        return `background: #E8F5E8; color: #2E7D32;`;
      case 'cancelled':
        return `background: #FFEBEE; color: #C62828;`;
      default:
        return `background: #F5F5F5; color: #757575;`;
    }
  }}
`;

const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
`;

const InfoCard = styled(Card)`
  text-align: center;
`;

const InfoIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const InfoTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const InfoValue = styled.p`
  font-size: 16px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
`;

const DetailSection = styled.div`
  margin-bottom: 24px;
`;

const SectionTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid ${props => props.theme.colors.borderLight};
`;

const DetailItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};

  &:last-child {
    border-bottom: none;
  }
`;

const DetailLabel = styled.span`
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  min-width: 120px;
`;

const DetailValue = styled.span`
  color: ${props => props.theme.colors.textSecondary};
  text-align: right;
  flex: 1;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 32px;
`;

const NotesSection = styled.div`
  background: ${props => props.theme.colors.lightGray};
  border-radius: 12px;
  padding: 20px;
  margin-top: 16px;
`;

const AppointmentDetailsScreen = () => {
  const { appointmentId } = useParams();
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const { 
    getAppointmentById, 
    updateAppointment, 
    cancelAppointment 
  } = useAppointments();
  const navigate = useNavigate();

  const [appointment, setAppointment] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', screen: 'dashboard' },
    { label: 'Appointments', icon: '📅', screen: 'appointments' },
    { label: 'Profile', icon: '👤', screen: 'profile' }
  ];

  useEffect(() => {
    loadAppointment();
  }, [appointmentId]);

  const loadAppointment = async () => {
    try {
      setLoading(true);
      const appointmentData = await getAppointmentById(appointmentId);
      setAppointment(appointmentData);
    } catch (error) {
      toast.error('Failed to load appointment details');
      navigate('/appointments');
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmAppointment = async () => {
    if (!appointment) return;

    try {
      setActionLoading(true);
      await updateAppointment(appointment.id, { status: 'confirmed' });
      setAppointment({ ...appointment, status: 'confirmed' });
      toast.success('Appointment confirmed successfully');
    } catch (error) {
      toast.error('Failed to confirm appointment');
    } finally {
      setActionLoading(false);
    }
  };

  const handleCancelAppointment = async () => {
    if (!appointment) return;

    try {
      setActionLoading(true);
      await cancelAppointment(appointment.id);
      setAppointment({ ...appointment, status: 'cancelled' });
      toast.success('Appointment cancelled successfully');
      setShowCancelModal(false);
    } catch (error) {
      toast.error('Failed to cancel appointment');
    } finally {
      setActionLoading(false);
    }
  };

  const handleReschedule = () => {
    navigate('/appointments/reschedule', { state: { appointment } });
  };

  const handleStartVideoCall = () => {
    // Navigate to video call screen
    navigate(`/video-call/${appointment.id}`);
  };

  const canCancelAppointment = () => {
    if (!appointment) return false;
    const appointmentDate = new Date(appointment.appointmentDate);
    const now = new Date();
    const hoursDiff = (appointmentDate - now) / (1000 * 60 * 60);
    
    return hoursDiff > 24 && 
           appointment.status !== 'cancelled' &&
           appointment.status !== 'completed';
  };

  const canConfirmAppointment = () => {
    return appointment?.status === 'scheduled' && user?.role === 'doctor';
  };

  const canStartVideoCall = () => {
    const appointmentDate = new Date(appointment?.appointmentDate);
    const now = new Date();
    const timeDiff = Math.abs(appointmentDate - now) / (1000 * 60); // minutes
    
    return appointment?.status === 'confirmed' && 
           timeDiff <= 15 && // Within 15 minutes of appointment time
           (user?.role === 'doctor' || user?.role === 'patient');
  };

  if (loading) {
    return (
      <DashboardLayout
        title="Appointment Details"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <DetailsContainer>
          <Card>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <div>Loading appointment details...</div>
            </div>
          </Card>
        </DetailsContainer>
      </DashboardLayout>
    );
  }

  if (!appointment) {
    return (
      <DashboardLayout
        title="Appointment Details"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <DetailsContainer>
          <Card>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <div>Appointment not found</div>
              <Button 
                variant="primary" 
                onClick={() => navigate('/appointments')}
                style={{ marginTop: '16px' }}
              >
                Back to Appointments
              </Button>
            </div>
          </Card>
        </DetailsContainer>
      </DashboardLayout>
    );
  }

  const appointmentDate = new Date(appointment.appointmentDate);

  return (
    <DashboardLayout
      title="Appointment Details"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <DetailsContainer>
        <BackButton
          variant="ghost"
          onClick={() => navigate('/appointments')}
          icon="←"
        >
          Back to Appointments
        </BackButton>

        <Header>
          <Title theme={theme}>Appointment Details</Title>
          <StatusBadge status={appointment.status}>
            {APPOINTMENT_STATUS_DISPLAY[appointment.status] || appointment.status}
          </StatusBadge>
        </Header>

        <InfoGrid>
          <InfoCard theme={theme}>
            <InfoIcon>👨‍⚕️</InfoIcon>
            <InfoTitle theme={theme}>
              {user?.role === 'patient' ? 'Doctor' : 'Patient'}
            </InfoTitle>
            <InfoValue theme={theme}>
              {user?.role === 'patient' 
                ? `Dr. ${appointment.doctorName}`
                : appointment.patientName
              }
            </InfoValue>
          </InfoCard>

          <InfoCard theme={theme}>
            <InfoIcon>📅</InfoIcon>
            <InfoTitle theme={theme}>Date & Time</InfoTitle>
            <InfoValue theme={theme}>
              {formatDate(appointmentDate, 'long')}
              <br />
              {appointmentDate.toLocaleTimeString([], { 
                hour: '2-digit', 
                minute: '2-digit' 
              })}
            </InfoValue>
          </InfoCard>

          <InfoCard theme={theme}>
            <InfoIcon>📍</InfoIcon>
            <InfoTitle theme={theme}>Location</InfoTitle>
            <InfoValue theme={theme}>
              {appointment.location || 'NeuroCare Clinic'}
            </InfoValue>
          </InfoCard>

          <InfoCard theme={theme}>
            <InfoIcon>🏥</InfoIcon>
            <InfoTitle theme={theme}>Type</InfoTitle>
            <InfoValue theme={theme}>
              {appointment.type || 'Consultation'}
            </InfoValue>
          </InfoCard>
        </InfoGrid>

        <Card title="Appointment Information" theme={theme}>
          <DetailSection>
            <DetailItem theme={theme}>
              <DetailLabel theme={theme}>Appointment ID:</DetailLabel>
              <DetailValue theme={theme}>{appointment.id}</DetailValue>
            </DetailItem>
            <DetailItem theme={theme}>
              <DetailLabel theme={theme}>Reason for Visit:</DetailLabel>
              <DetailValue theme={theme}>{appointment.reason || 'Not specified'}</DetailValue>
            </DetailItem>
            <DetailItem theme={theme}>
              <DetailLabel theme={theme}>Urgency:</DetailLabel>
              <DetailValue theme={theme}>
                {appointment.urgency ? appointment.urgency.charAt(0).toUpperCase() + appointment.urgency.slice(1) : 'Normal'}
              </DetailValue>
            </DetailItem>
            <DetailItem theme={theme}>
              <DetailLabel theme={theme}>Created:</DetailLabel>
              <DetailValue theme={theme}>
                {appointment.createdAt ? formatDate(appointment.createdAt.toDate(), 'datetime') : 'Unknown'}
              </DetailValue>
            </DetailItem>
          </DetailSection>

          {appointment.notes && (
            <DetailSection>
              <SectionTitle theme={theme}>Notes</SectionTitle>
              <NotesSection theme={theme}>
                {appointment.notes}
              </NotesSection>
            </DetailSection>
          )}
        </Card>

        <ActionButtons>
          {canStartVideoCall() && (
            <Button
              variant="primary"
              onClick={handleStartVideoCall}
              icon="📹"
            >
              Start Video Call
            </Button>
          )}

          {canConfirmAppointment() && (
            <Button
              variant="success"
              onClick={handleConfirmAppointment}
              loading={actionLoading}
              icon="✓"
            >
              Confirm Appointment
            </Button>
          )}

          {canCancelAppointment() && (
            <Button
              variant="outline"
              onClick={handleReschedule}
              icon="📅"
            >
              Reschedule
            </Button>
          )}

          {canCancelAppointment() && (
            <Button
              variant="danger"
              onClick={() => setShowCancelModal(true)}
              icon="✕"
            >
              Cancel Appointment
            </Button>
          )}
        </ActionButtons>

        {/* Cancel Confirmation Modal */}
        <Modal
          isOpen={showCancelModal}
          onClose={() => setShowCancelModal(false)}
          title="Cancel Appointment"
          footer={
            <>
              <Button 
                variant="outline" 
                onClick={() => setShowCancelModal(false)}
                disabled={actionLoading}
              >
                Keep Appointment
              </Button>
              <Button 
                variant="danger" 
                onClick={handleCancelAppointment}
                loading={actionLoading}
              >
                Cancel Appointment
              </Button>
            </>
          }
        >
          <p>Are you sure you want to cancel this appointment?</p>
          <div style={{ 
            padding: '16px', 
            backgroundColor: theme.colors.lightGray, 
            borderRadius: '8px',
            marginTop: '16px'
          }}>
            <strong>
              {user?.role === 'patient' 
                ? `Dr. ${appointment.doctorName}`
                : appointment.patientName
              }
            </strong>
            <br />
            {formatDate(appointmentDate, 'long')} at{' '}
            {appointmentDate.toLocaleTimeString([], { 
              hour: '2-digit', 
              minute: '2-digit' 
            })}
          </div>
          <p style={{ marginTop: '16px', fontSize: '14px', color: theme.colors.textSecondary }}>
            This action cannot be undone. You'll need to schedule a new appointment if you change your mind.
          </p>
        </Modal>
      </DetailsContainer>
    </DashboardLayout>
  );
};

export default AppointmentDetailsScreen;
