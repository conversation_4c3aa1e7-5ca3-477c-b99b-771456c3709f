import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useAppointments } from '../../contexts/AppointmentContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Modal } from '../common';
import { formatDate, getRelativeTime, isToday, isTomorrow } from '../../utils/dateUtils';
import { APPOINTMENT_STATUS, APPOINTMENT_STATUS_DISPLAY } from '../../utils/constants';

const AppointmentsContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const FilterTabs = styled.div`
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  flex-wrap: wrap;
`;

const FilterTab = styled.button`
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid ${props => props.theme.colors.border};

  ${props => props.active ? `
    background: ${props.theme.colors.primary};
    color: white;
    border-color: ${props.theme.colors.primary};
  ` : `
    background: white;
    color: ${props.theme.colors.textSecondary};

    &:hover {
      background: ${props.theme.colors.lightGray};
    }
  `}
`;

const AppointmentsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const AppointmentCard = styled(Card)`
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.lg};
  }
`;

const AppointmentHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
`;

const AppointmentInfo = styled.div`
  flex: 1;
`;

const AppointmentTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const AppointmentDetails = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const AppointmentDetail = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  display: flex;
  align-items: center;
  gap: 8px;
`;

const StatusBadge = styled.span`
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;

  ${props => {
    switch (props.status) {
      case 'scheduled':
        return `background: #E3F2FD; color: #1976D2;`;
      case 'confirmed':
        return `background: #E8F5E8; color: #2E7D32;`;
      case 'in_progress':
        return `background: #FFF3E0; color: #F57C00;`;
      case 'completed':
        return `background: #E8F5E8; color: #2E7D32;`;
      case 'cancelled':
        return `background: #FFEBEE; color: #C62828;`;
      default:
        return `background: #F5F5F5; color: #757575;`;
    }
  }}
`;

const AppointmentActions = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 16px;
  flex-wrap: wrap;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: ${props => props.theme.shadows.md};
`;

const EmptyIcon = styled.div`
  font-size: 64px;
  margin-bottom: 16px;
`;

const EmptyTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const EmptyMessage = styled.p`
  font-size: 16px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0 0 24px 0;
`;

const AppointmentsScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const {
    appointments,
    loading,
    createAppointment,
    updateAppointment,
    cancelAppointment,
    getUpcomingAppointments,
    getPastAppointments
  } = useAppointments();
  const navigate = useNavigate();

  const [filter, setFilter] = useState('all');
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [showCancelModal, setShowCancelModal] = useState(false);

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', screen: 'dashboard' },
    { label: 'Appointments', icon: '📅', screen: 'appointments' },
    { label: 'Profile', icon: '👤', screen: 'profile' }
  ];

  // Filter appointments based on selected filter
  const filteredAppointments = appointments.filter(appointment => {
    switch (filter) {
      case 'upcoming':
        return new Date(appointment.appointmentDate) > new Date();
      case 'past':
        return new Date(appointment.appointmentDate) < new Date();
      case 'today':
        return isToday(appointment.appointmentDate);
      case 'confirmed':
        return appointment.status === APPOINTMENT_STATUS.CONFIRMED;
      case 'pending':
        return appointment.status === APPOINTMENT_STATUS.SCHEDULED;
      default:
        return true;
    }
  });

  const handleAppointmentClick = (appointment) => {
    setSelectedAppointment(appointment);
  };

  const handleCancelAppointment = async () => {
    if (!selectedAppointment) return;

    try {
      await cancelAppointment(selectedAppointment.id);
      toast.success('Appointment cancelled successfully');
      setShowCancelModal(false);
      setSelectedAppointment(null);
    } catch (error) {
      toast.error('Failed to cancel appointment');
    }
  };

  const handleReschedule = (appointment) => {
    navigate('/appointments/reschedule', { state: { appointment } });
  };

  const getAppointmentDateDisplay = (date) => {
    if (isToday(date)) return 'Today';
    if (isTomorrow(date)) return 'Tomorrow';
    return formatDate(date, 'short');
  };

  const getAppointmentTimeDisplay = (date) => {
    return new Date(date).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const canCancelAppointment = (appointment) => {
    const appointmentDate = new Date(appointment.appointmentDate);
    const now = new Date();
    const hoursDiff = (appointmentDate - now) / (1000 * 60 * 60);

    return hoursDiff > 24 &&
           appointment.status !== APPOINTMENT_STATUS.CANCELLED &&
           appointment.status !== APPOINTMENT_STATUS.COMPLETED;
  };

  const canRescheduleAppointment = (appointment) => {
    return canCancelAppointment(appointment);
  };

  const filters = [
    { key: 'all', label: 'All', count: appointments.length },
    { key: 'upcoming', label: 'Upcoming', count: getUpcomingAppointments().length },
    { key: 'today', label: 'Today', count: appointments.filter(a => isToday(a.appointmentDate)).length },
    { key: 'past', label: 'Past', count: getPastAppointments().length },
    { key: 'confirmed', label: 'Confirmed', count: appointments.filter(a => a.status === APPOINTMENT_STATUS.CONFIRMED).length }
  ];

  return (
    <DashboardLayout
      title="Appointments"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <AppointmentsContainer>
        <Header>
          <Title theme={theme}>My Appointments</Title>
          <Button
            variant="primary"
            onClick={() => navigate('/appointments/new')}
            icon="+"
          >
            New Appointment
          </Button>
        </Header>

        <FilterTabs>
          {filters.map(filterOption => (
            <FilterTab
              key={filterOption.key}
              active={filter === filterOption.key}
              onClick={() => setFilter(filterOption.key)}
              theme={theme}
            >
              {filterOption.label} ({filterOption.count})
            </FilterTab>
          ))}
        </FilterTabs>

        {loading ? (
          <Card>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <div>Loading appointments...</div>
            </div>
          </Card>
        ) : filteredAppointments.length > 0 ? (
          <AppointmentsList>
            {filteredAppointments.map(appointment => (
              <AppointmentCard
                key={appointment.id}
                onClick={() => handleAppointmentClick(appointment)}
                theme={theme}
              >
                <AppointmentHeader>
                  <AppointmentInfo>
                    <AppointmentTitle theme={theme}>
                      {user?.role === 'patient'
                        ? `Dr. ${appointment.doctorName || 'Unknown'}`
                        : appointment.patientName || 'Unknown Patient'
                      }
                    </AppointmentTitle>
                    <AppointmentDetails>
                      <AppointmentDetail theme={theme}>
                        📅 {getAppointmentDateDisplay(appointment.appointmentDate)}
                      </AppointmentDetail>
                      <AppointmentDetail theme={theme}>
                        🕐 {getAppointmentTimeDisplay(appointment.appointmentDate)}
                      </AppointmentDetail>
                      <AppointmentDetail theme={theme}>
                        📍 {appointment.location || 'Clinic'}
                      </AppointmentDetail>
                      {appointment.type && (
                        <AppointmentDetail theme={theme}>
                          🏥 {appointment.type}
                        </AppointmentDetail>
                      )}
                    </AppointmentDetails>
                  </AppointmentInfo>
                  <StatusBadge status={appointment.status}>
                    {APPOINTMENT_STATUS_DISPLAY[appointment.status] || appointment.status}
                  </StatusBadge>
                </AppointmentHeader>

                {appointment.notes && (
                  <div style={{
                    marginBottom: '16px',
                    padding: '12px',
                    backgroundColor: theme.colors.lightGray,
                    borderRadius: '8px',
                    fontSize: '14px'
                  }}>
                    <strong>Notes:</strong> {appointment.notes}
                  </div>
                )}

                <AppointmentActions>
                  <Button
                    variant="outline"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate(`/appointments/${appointment.id}`);
                    }}
                  >
                    View Details
                  </Button>

                  {canRescheduleAppointment(appointment) && (
                    <Button
                      variant="outline"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleReschedule(appointment);
                      }}
                    >
                      Reschedule
                    </Button>
                  )}

                  {canCancelAppointment(appointment) && (
                    <Button
                      variant="danger"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedAppointment(appointment);
                        setShowCancelModal(true);
                      }}
                    >
                      Cancel
                    </Button>
                  )}
                </AppointmentActions>
              </AppointmentCard>
            ))}
          </AppointmentsList>
        ) : (
          <EmptyState theme={theme}>
            <EmptyIcon>📅</EmptyIcon>
            <EmptyTitle theme={theme}>
              {filter === 'all' ? 'No appointments yet' : `No ${filter} appointments`}
            </EmptyTitle>
            <EmptyMessage theme={theme}>
              {filter === 'all'
                ? "You don't have any appointments scheduled yet."
                : `No ${filter} appointments found.`
              }
            </EmptyMessage>
            <Button
              variant="primary"
              onClick={() => navigate('/appointments/new')}
            >
              Schedule Your First Appointment
            </Button>
          </EmptyState>
        )}

        {/* Cancel Confirmation Modal */}
        <Modal
          isOpen={showCancelModal}
          onClose={() => setShowCancelModal(false)}
          title="Cancel Appointment"
          footer={
            <>
              <Button
                variant="outline"
                onClick={() => setShowCancelModal(false)}
              >
                Keep Appointment
              </Button>
              <Button
                variant="danger"
                onClick={handleCancelAppointment}
              >
                Cancel Appointment
              </Button>
            </>
          }
        >
          <p>Are you sure you want to cancel this appointment?</p>
          {selectedAppointment && (
            <div style={{
              padding: '16px',
              backgroundColor: theme.colors.lightGray,
              borderRadius: '8px',
              marginTop: '16px'
            }}>
              <strong>
                {user?.role === 'patient'
                  ? `Dr. ${selectedAppointment.doctorName}`
                  : selectedAppointment.patientName
                }
              </strong>
              <br />
              {formatDate(selectedAppointment.appointmentDate, 'long')} at{' '}
              {getAppointmentTimeDisplay(selectedAppointment.appointmentDate)}
            </div>
          )}
          <p style={{ marginTop: '16px', fontSize: '14px', color: theme.colors.textSecondary }}>
            This action cannot be undone. You'll need to schedule a new appointment if you change your mind.
          </p>
        </Modal>
      </AppointmentsContainer>
    </DashboardLayout>
  );
};

export default AppointmentsScreen;
