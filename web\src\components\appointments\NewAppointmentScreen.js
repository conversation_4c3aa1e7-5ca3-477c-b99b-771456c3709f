import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useAppointments } from '../../contexts/AppointmentContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Input, Select } from '../common';
import { useForm } from '../../hooks/useForm';
import { validateRequired } from '../../utils/validation';
import { APPOINTMENT_TYPES } from '../../utils/constants';
import { getTimeSlots, addDays } from '../../utils/dateUtils';

const NewAppointmentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const FormCard = styled(Card)`
  margin-bottom: 24px;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid ${props => props.theme.colors.borderLight};
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
`;

const TimeSlotGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 12px;
  margin-top: 16px;
`;

const TimeSlot = styled.button`
  padding: 12px 16px;
  border: 2px solid ${props => props.available ? props.theme.colors.border : '#FFCDD2'};
  border-radius: 8px;
  background: ${props => {
    if (!props.available) return '#FFEBEE';
    if (props.selected) return props.theme.colors.primary;
    return 'white';
  }};
  color: ${props => {
    if (!props.available) return '#C62828';
    if (props.selected) return 'white';
    return props.theme.colors.text;
  }};
  cursor: ${props => props.available ? 'pointer' : 'not-allowed'};
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;

  &:hover {
    ${props => props.available && !props.selected && `
      border-color: ${props.theme.colors.primary};
      background: ${props.theme.colors.lightGray};
    `}
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const DoctorCard = styled.div`
  border: 2px solid ${props => props.selected ? props.theme.colors.primary : props.theme.colors.border};
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: ${props => props.selected ? `${props.theme.colors.primary}10` : 'white'};

  &:hover {
    border-color: ${props => props.theme.colors.primary};
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.md};
  }
`;

const DoctorInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const DoctorAvatar = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary} 0%, ${props => props.theme.colors.primaryLight} 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: 700;
`;

const DoctorDetails = styled.div`
  flex: 1;
`;

const DoctorName = styled.h4`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 4px 0;
`;

const DoctorSpecialty = styled.p`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0 0 8px 0;
`;

const DoctorRating = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 32px;
`;

const NewAppointmentScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const { createAppointment } = useAppointments();
  const navigate = useNavigate();

  const [selectedDoctor, setSelectedDoctor] = useState(null);
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [availableSlots, setAvailableSlots] = useState([]);
  const [loading, setLoading] = useState(false);

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', screen: 'dashboard' },
    { label: 'Appointments', icon: '📅', screen: 'appointments' },
    { label: 'Profile', icon: '👤', screen: 'profile' }
  ];

  // Mock doctors data - in real app, this would come from an API
  const doctors = [
    {
      id: 1,
      name: 'Dr. Sarah Johnson',
      specialty: 'Neurologist',
      rating: 4.8,
      avatar: 'SJ',
      available: true
    },
    {
      id: 2,
      name: 'Dr. Michael Chen',
      specialty: 'Psychiatrist',
      rating: 4.9,
      avatar: 'MC',
      available: true
    },
    {
      id: 3,
      name: 'Dr. Emily Rodriguez',
      specialty: 'Neuropsychologist',
      rating: 4.7,
      avatar: 'ER',
      available: true
    }
  ];

  const appointmentTypeOptions = Object.keys(APPOINTMENT_TYPES).map(key => ({
    value: APPOINTMENT_TYPES[key],
    label: key.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ')
  }));

  const {
    values,
    errors,
    handleChange,
    handleSubmit,
    getFieldProps,
    setValue
  } = useForm(
    {
      type: APPOINTMENT_TYPES.CONSULTATION,
      reason: '',
      notes: '',
      urgency: 'normal'
    },
    {
      type: (value) => validateRequired(value, 'Appointment type'),
      reason: (value) => validateRequired(value, 'Reason for visit')
    },
    async (formData) => {
      if (!selectedDoctor) {
        toast.error('Please select a doctor');
        return;
      }
      if (!selectedDate) {
        toast.error('Please select a date');
        return;
      }
      if (!selectedTime) {
        toast.error('Please select a time');
        return;
      }

      setLoading(true);
      try {
        const appointmentDate = new Date(`${selectedDate}T${selectedTime}`);
        
        const appointmentData = {
          patientId: user.uid,
          patientName: user.displayName,
          doctorId: selectedDoctor.id,
          doctorName: selectedDoctor.name,
          appointmentDate: appointmentDate.toISOString(),
          type: formData.type,
          reason: formData.reason,
          notes: formData.notes,
          urgency: formData.urgency,
          status: 'scheduled',
          location: 'NeuroCare Clinic'
        };

        await createAppointment(appointmentData);
        toast.success('Appointment scheduled successfully!');
        navigate('/appointments');
      } catch (error) {
        toast.error('Failed to schedule appointment. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  );

  // Generate available time slots when date changes
  useEffect(() => {
    if (selectedDate) {
      const date = new Date(selectedDate);
      const slots = getTimeSlots(date, 30, '09:00', '17:00');
      setAvailableSlots(slots);
      setSelectedTime(''); // Reset selected time
    }
  }, [selectedDate]);

  const handleDoctorSelect = (doctor) => {
    setSelectedDoctor(doctor);
  };

  const handleDateChange = (e) => {
    setSelectedDate(e.target.value);
  };

  const handleTimeSelect = (time) => {
    setSelectedTime(time);
  };

  // Get minimum date (today)
  const getMinDate = () => {
    return new Date().toISOString().split('T')[0];
  };

  // Get maximum date (3 months from now)
  const getMaxDate = () => {
    const maxDate = addDays(new Date(), 90);
    return maxDate.toISOString().split('T')[0];
  };

  return (
    <DashboardLayout
      title="New Appointment"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <NewAppointmentContainer>
        <FormCard title="Schedule New Appointment" theme={theme}>
          <form onSubmit={handleSubmit}>
            {/* Doctor Selection */}
            <FormSection>
              <SectionTitle theme={theme}>Select Doctor</SectionTitle>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                {doctors.map(doctor => (
                  <DoctorCard
                    key={doctor.id}
                    selected={selectedDoctor?.id === doctor.id}
                    onClick={() => handleDoctorSelect(doctor)}
                    theme={theme}
                  >
                    <DoctorInfo>
                      <DoctorAvatar theme={theme}>
                        {doctor.avatar}
                      </DoctorAvatar>
                      <DoctorDetails>
                        <DoctorName theme={theme}>{doctor.name}</DoctorName>
                        <DoctorSpecialty theme={theme}>{doctor.specialty}</DoctorSpecialty>
                        <DoctorRating theme={theme}>
                          ⭐ {doctor.rating} • Available
                        </DoctorRating>
                      </DoctorDetails>
                    </DoctorInfo>
                  </DoctorCard>
                ))}
              </div>
            </FormSection>

            {/* Date and Time Selection */}
            <FormSection>
              <SectionTitle theme={theme}>Select Date & Time</SectionTitle>
              <FormRow>
                <Input
                  type="date"
                  label="Preferred Date"
                  value={selectedDate}
                  onChange={handleDateChange}
                  min={getMinDate()}
                  max={getMaxDate()}
                  required
                />
              </FormRow>

              {selectedDate && (
                <div>
                  <label style={{ 
                    fontSize: '14px', 
                    fontWeight: '600', 
                    color: theme.colors.text,
                    marginBottom: '8px',
                    display: 'block'
                  }}>
                    Available Time Slots
                  </label>
                  <TimeSlotGrid>
                    {availableSlots.map((slot, index) => (
                      <TimeSlot
                        key={index}
                        available={slot.available}
                        selected={selectedTime === slot.time}
                        onClick={() => slot.available && handleTimeSelect(slot.time)}
                        theme={theme}
                        type="button"
                      >
                        {slot.time}
                      </TimeSlot>
                    ))}
                  </TimeSlotGrid>
                </div>
              )}
            </FormSection>

            {/* Appointment Details */}
            <FormSection>
              <SectionTitle theme={theme}>Appointment Details</SectionTitle>
              <FormRow>
                <Select
                  {...getFieldProps('type')}
                  label="Appointment Type"
                  options={appointmentTypeOptions}
                  required
                />
                <Select
                  label="Urgency"
                  value={values.urgency}
                  onChange={(value) => setValue('urgency', value)}
                  options={[
                    { value: 'normal', label: 'Normal' },
                    { value: 'urgent', label: 'Urgent' },
                    { value: 'emergency', label: 'Emergency' }
                  ]}
                />
              </FormRow>
              <Input
                {...getFieldProps('reason')}
                label="Reason for Visit"
                placeholder="Brief description of your concern"
                required
              />
              <Input
                {...getFieldProps('notes')}
                label="Additional Notes"
                placeholder="Any additional information for the doctor"
                multiline
                rows={3}
              />
            </FormSection>

            <ButtonGroup>
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/appointments')}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={loading}
                disabled={!selectedDoctor || !selectedDate || !selectedTime}
              >
                Schedule Appointment
              </Button>
            </ButtonGroup>
          </form>
        </FormCard>
      </NewAppointmentContainer>
    </DashboardLayout>
  );
};

export default NewAppointmentScreen;
