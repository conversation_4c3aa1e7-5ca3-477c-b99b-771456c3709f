import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';

const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary} 0%, ${props => props.theme.colors.primaryLight} 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
`;

const ResetCard = styled.div`
  background: white;
  border-radius: 20px;
  padding: 40px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 32px;
`;

const Logo = styled.div`
  width: 60px;
  height: 60px;
  background-color: ${props => props.theme.colors.primary};
  border-radius: 15px;
  margin: 0 auto 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &::before {
    content: '🔒';
    font-size: 30px;
  }
`;

const Title = styled.h1`
  font-size: 28px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const Subtitle = styled.p`
  font-size: 16px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
  line-height: 1.5;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const InputGroup = styled.div`
  position: relative;
`;

const Label = styled.label`
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  margin-bottom: 8px;
`;

const Input = styled.input`
  width: 100%;
  padding: 16px;
  border: 2px solid ${props => props.theme.colors.border};
  border-radius: 12px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
  
  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
  }
`;

const ResetButton = styled.button`
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary} 0%, ${props => props.theme.colors.primaryDark} 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(76, 175, 80, 0.3);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const BackToLogin = styled(Link)`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-align: center;
  color: rgba(16, 107, 0, 1);
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  margin-top: 16px;
  
  &:hover {
    text-decoration: underline;
  }
`;

const SuccessMessage = styled.div`
  background-color: #e8f5e8;
  border: 1px solid #4caf50;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  text-align: center;
`;

const SuccessIcon = styled.div`
  font-size: 32px;
  margin-bottom: 8px;
`;

const SuccessText = styled.p`
  color: #2e7d32;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
`;

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  
  const { resetPassword } = useAuth();
  const { theme } = useTheme();

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email) {
      toast.error('Please enter your email address');
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    setLoading(true);
    
    try {
      await resetPassword(email);
      setEmailSent(true);
      toast.success('Password reset email sent successfully!');
    } catch (error) {
      console.error('Password reset error:', error);
      if (error.code === 'auth/user-not-found') {
        toast.error('No account found with this email address');
      } else {
        toast.error(error.message || 'Failed to send reset email. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  if (emailSent) {
    return (
      <Container theme={theme}>
        <ResetCard>
          <Header>
            <Logo theme={theme} />
            <Title theme={theme}>Check Your Email</Title>
          </Header>

          <SuccessMessage>
            <SuccessIcon>📧</SuccessIcon>
            <SuccessText>
              We've sent a password reset link to <strong>{email}</strong>
              <br /><br />
              Please check your email and follow the instructions to reset your password.
              Don't forget to check your spam folder!
            </SuccessText>
          </SuccessMessage>

          <BackToLogin to="/login">
            ← Back to Sign In
          </BackToLogin>
        </ResetCard>
      </Container>
    );
  }

  return (
    <Container theme={theme}>
      <ResetCard>
        <Header>
          <Logo theme={theme} />
          <Title theme={theme}>Reset Password</Title>
          <Subtitle theme={theme}>
            Enter your email address and we'll send you a link to reset your password
          </Subtitle>
        </Header>

        <Form onSubmit={handleSubmit}>
          <InputGroup>
            <Label theme={theme}>Email Address</Label>
            <Input
              type="email"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              theme={theme}
              required
            />
          </InputGroup>

          <ResetButton type="submit" disabled={loading} theme={theme}>
            {loading ? 'Sending Reset Link...' : 'Send Reset Link'}
          </ResetButton>
        </Form>

        <BackToLogin to="/login">
          ← Back to Sign In
        </BackToLogin>
      </ResetCard>
    </Container>
  );
};

export default ForgotPassword;
