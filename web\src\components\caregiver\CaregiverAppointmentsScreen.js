import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { <PERSON><PERSON>, Card, Modal } from '../common';
import { firebaseAppointmentsService } from '../../services/firebaseAppointmentsService';
import { firebaseCaregiverService } from '../../services/firebaseCaregiverService';
import { formatDate } from '../../utils/dateUtils';

const AppointmentsContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const FilterContainer = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: wrap;
`;

const FilterButton = styled.button`
  padding: 8px 16px;
  border: 2px solid ${props => props.active ? props.theme.colors.primary : props.theme.colors.border};
  border-radius: 20px;
  background: ${props => props.active ? props.theme.colors.primary : 'transparent'};
  color: ${props => props.active ? 'white' : props.theme.colors.text};
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    border-color: ${props => props.theme.colors.primary};
    background: ${props => props.active ? props.theme.colors.primaryDark : props.theme.colors.primaryLighter};
  }
`;

const AppointmentsList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
`;

const AppointmentCard = styled(Card)`
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid ${props => {
    switch (props.status) {
      case 'confirmed': return '#4CAF50';
      case 'pending': return '#FF9800';
      case 'cancelled': return '#F44336';
      case 'completed': return '#2196F3';
      default: return props.theme.colors.border;
    }
  }};

  &:hover {
    transform: translateY(-4px);
    box-shadow: ${props => props.theme.shadows.lg};
  }
`;

const AppointmentHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
`;

const PatientInfo = styled.div`
  flex: 1;
`;

const PatientName = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 4px 0;
`;

const AppointmentDate = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  margin-bottom: 2px;
`;

const AppointmentTime = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
`;

const StatusBadge = styled.div`
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  
  ${props => {
    switch (props.status) {
      case 'confirmed':
        return `background: #E8F5E8; color: #2E7D32;`;
      case 'pending':
        return `background: #FFF3E0; color: #F57C00;`;
      case 'cancelled':
        return `background: #FFEBEE; color: #C62828;`;
      case 'completed':
        return `background: #E3F2FD; color: #1565C0;`;
      default:
        return `background: #F5F5F5; color: #757575;`;
    }
  }}
`;

const DoctorInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
`;

const DoctorIcon = styled.span`
  color: ${props => props.theme.colors.primary};
`;

const DoctorName = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
`;

const AppointmentReason = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  font-style: italic;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 80px 20px;
  grid-column: 1 / -1;
`;

const EmptyIcon = styled.div`
  font-size: 64px;
  margin-bottom: 16px;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
`;

const LoadingSpinner = styled.div`
  width: 50px;
  height: 50px;
  border: 4px solid ${props => props.theme.colors.border};
  border-top: 4px solid ${props => props.theme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ModalContent = styled.div`
  max-height: 500px;
  overflow-y: auto;
`;

const DetailRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid ${props => props.theme.colors.border};

  &:last-child {
    border-bottom: none;
  }
`;

const DetailLabel = styled.div`
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const DetailValue = styled.div`
  color: ${props => props.theme.colors.textSecondary};
  text-align: right;
  max-width: 60%;
`;

const CaregiverAppointmentsScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();

  const [appointments, setAppointments] = useState([]);
  const [filteredAppointments, setFilteredAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [patients, setPatients] = useState([]);

  const roleColors = getRoleColors(user?.role || 'caregiver');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Patients', icon: '👥', path: '/caregiver/patients' },
    { label: 'Appointments', icon: '📅', path: '/caregiver/appointments' },
    { label: 'Activities', icon: '📋', path: '/caregiver/activities' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  const filterOptions = [
    { value: 'all', label: 'All' },
    { value: 'pending', label: 'Pending' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  useEffect(() => {
    loadData();
  }, [user]);

  const loadData = async () => {
    if (!user?.uid) return;

    try {
      setLoading(true);

      // Get patients assigned to this caregiver
      const patientsData = await firebaseCaregiverService.getCaregiverPatients(user.uid);
      setPatients(patientsData);

      // Get appointments for all assigned patients
      const allAppointments = [];
      for (const patient of patientsData) {
        const patientAppointments = await firebaseAppointmentsService.getUserAppointments(patient.id, 'patient');
        const appointmentsWithPatientInfo = patientAppointments.map(apt => ({
          ...apt,
          patientName: patient.name,
          patientInfo: patient
        }));
        allAppointments.push(...appointmentsWithPatientInfo);
      }

      // Sort by date and time
      allAppointments.sort((a, b) => {
        const dateA = new Date(`${a.date}T${a.time || '00:00'}`);
        const dateB = new Date(`${b.date}T${b.time || '00:00'}`);
        return dateA - dateB;
      });

      setAppointments(allAppointments);
      applyFilter(allAppointments, filterStatus);
    } catch (error) {
      console.error('Error loading appointments:', error);
      toast.error('Failed to load appointments');
    } finally {
      setLoading(false);
    }
  };

  const applyFilter = (appointmentsToFilter, status) => {
    let filtered = [...appointmentsToFilter];
    
    if (status !== 'all') {
      filtered = filtered.filter(appointment => 
        appointment.status?.toLowerCase() === status.toLowerCase()
      );
    }
    
    setFilteredAppointments(filtered);
  };

  const handleFilterChange = (status) => {
    setFilterStatus(status);
    applyFilter(appointments, status);
  };

  const handleAppointmentClick = (appointment) => {
    setSelectedAppointment(appointment);
    setShowDetailModal(true);
  };

  if (loading) {
    return (
      <DashboardLayout
        title="Appointments"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <AppointmentsContainer>
          <LoadingContainer>
            <LoadingSpinner theme={theme} />
          </LoadingContainer>
        </AppointmentsContainer>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Patient Appointments"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <AppointmentsContainer>
        <Header>
          <Title theme={theme}>Patient Appointments</Title>
          <Button
            variant="primary"
            onClick={() => navigate('/caregiver/patients')}
          >
            View Patients
          </Button>
        </Header>

        <FilterContainer>
          {filterOptions.map(option => (
            <FilterButton
              key={option.value}
              theme={theme}
              active={filterStatus === option.value}
              onClick={() => handleFilterChange(option.value)}
            >
              {option.label}
            </FilterButton>
          ))}
        </FilterContainer>

        <AppointmentsList>
          {filteredAppointments.length === 0 ? (
            <EmptyState>
              <EmptyIcon>📅</EmptyIcon>
              <h3>No appointments found</h3>
              <p>
                {filterStatus !== 'all'
                  ? `No ${filterStatus} appointments found. Try another filter.`
                  : 'Your patients have no scheduled appointments.'}
              </p>
            </EmptyState>
          ) : (
            filteredAppointments.map(appointment => (
              <AppointmentCard
                key={appointment.id}
                theme={theme}
                status={appointment.status}
                onClick={() => handleAppointmentClick(appointment)}
              >
                <AppointmentHeader>
                  <PatientInfo>
                    <PatientName theme={theme}>{appointment.patientName}</PatientName>
                    <AppointmentDate theme={theme}>
                      {formatDate(appointment.date, 'date')}
                    </AppointmentDate>
                    <AppointmentTime theme={theme}>
                      {appointment.time}
                    </AppointmentTime>
                  </PatientInfo>
                  <StatusBadge status={appointment.status}>
                    {appointment.status}
                  </StatusBadge>
                </AppointmentHeader>

                <DoctorInfo>
                  <DoctorIcon theme={theme}>👨‍⚕️</DoctorIcon>
                  <DoctorName theme={theme}>
                    {appointment.doctorName || 'Doctor'}
                  </DoctorName>
                </DoctorInfo>

                {appointment.reason && (
                  <AppointmentReason theme={theme}>
                    {appointment.reason}
                  </AppointmentReason>
                )}
              </AppointmentCard>
            ))
          )}
        </AppointmentsList>

        {/* Appointment Detail Modal */}
        <Modal
          isOpen={showDetailModal}
          onClose={() => setShowDetailModal(false)}
          title="Appointment Details"
          maxWidth="600px"
        >
          {selectedAppointment && (
            <ModalContent>
              <DetailRow theme={theme}>
                <DetailLabel theme={theme}>Patient:</DetailLabel>
                <DetailValue theme={theme}>{selectedAppointment.patientName}</DetailValue>
              </DetailRow>

              <DetailRow theme={theme}>
                <DetailLabel theme={theme}>Doctor:</DetailLabel>
                <DetailValue theme={theme}>{selectedAppointment.doctorName || 'Doctor'}</DetailValue>
              </DetailRow>

              <DetailRow theme={theme}>
                <DetailLabel theme={theme}>Date:</DetailLabel>
                <DetailValue theme={theme}>
                  {formatDate(selectedAppointment.date, 'date')}
                </DetailValue>
              </DetailRow>

              <DetailRow theme={theme}>
                <DetailLabel theme={theme}>Time:</DetailLabel>
                <DetailValue theme={theme}>{selectedAppointment.time}</DetailValue>
              </DetailRow>

              <DetailRow theme={theme}>
                <DetailLabel theme={theme}>Status:</DetailLabel>
                <DetailValue>
                  <StatusBadge status={selectedAppointment.status}>
                    {selectedAppointment.status}
                  </StatusBadge>
                </DetailValue>
              </DetailRow>

              {selectedAppointment.reason && (
                <DetailRow theme={theme}>
                  <DetailLabel theme={theme}>Reason:</DetailLabel>
                  <DetailValue theme={theme}>{selectedAppointment.reason}</DetailValue>
                </DetailRow>
              )}

              {selectedAppointment.notes && (
                <DetailRow theme={theme}>
                  <DetailLabel theme={theme}>Notes:</DetailLabel>
                  <DetailValue theme={theme}>{selectedAppointment.notes}</DetailValue>
                </DetailRow>
              )}

              <div style={{ marginTop: '24px', textAlign: 'center' }}>
                <Button
                  variant="outline"
                  onClick={() => setShowDetailModal(false)}
                >
                  Close
                </Button>
              </div>
            </ModalContent>
          )}
        </Modal>
      </AppointmentsContainer>
    </DashboardLayout>
  );
};

export default CaregiverAppointmentsScreen;
