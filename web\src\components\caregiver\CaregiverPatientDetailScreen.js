import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Modal } from '../common';
import { formatDate } from '../../utils/dateUtils';

const PatientDetailContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const BackButton = styled(Button)`
  margin-bottom: 16px;
`;

const PatientHeader = styled(Card)`
  margin-bottom: 24px;
`;

const PatientInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 24px;
`;

const PatientAvatar = styled.div`
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary} 0%, ${props => props.theme.colors.primaryLight} 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 40px;
  font-weight: 700;
  box-shadow: ${props => props.theme.shadows.lg};
`;

const PatientDetails = styled.div`
  flex: 1;
`;

const PatientName = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const PatientMeta = styled.div`
  display: flex;
  gap: 24px;
  color: ${props => props.theme.colors.textSecondary};
  font-size: 16px;
  margin-bottom: 16px;
`;

const StatusBadge = styled.span`
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  
  ${props => {
    switch (props.status) {
      case 'stable':
        return `background: #E8F5E8; color: #2E7D32;`;
      case 'attention':
        return `background: #FFF3E0; color: #F57C00;`;
      case 'critical':
        return `background: #FFEBEE; color: #C62828;`;
      default:
        return `background: #F5F5F5; color: #757575;`;
    }
  }}
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
`;

const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const SectionCard = styled(Card)`
  height: fit-content;
`;

const SectionTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid ${props => props.theme.colors.borderLight};
`;

const VitalsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const VitalItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 8px;
`;

const VitalLabel = styled.span`
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const VitalValue = styled.span`
  color: ${props => {
    switch (props.status) {
      case 'normal': return '#4CAF50';
      case 'warning': return '#FF9800';
      case 'danger': return '#F44336';
      default: return props.theme.colors.text;
    }
  }};
  font-weight: 600;
`;

const ActivityList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const ActivityItem = styled.div`
  padding: 16px;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 12px;
  border-left: 4px solid ${props => props.theme.colors.primary};
`;

const ActivityHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const ActivityTitle = styled.h4`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const ActivityTime = styled.span`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
`;

const ActivityDescription = styled.p`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
  line-height: 1.4;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 40px;
  color: ${props => props.theme.colors.textSecondary};
`;

const CaregiverPatientDetailScreen = () => {
  const { patientId } = useParams();
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();

  const [patient, setPatient] = useState(null);
  const [vitals, setVitals] = useState([]);
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showEmergencyModal, setShowEmergencyModal] = useState(false);

  const roleColors = getRoleColors(user?.role || 'caregiver');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'My Patients', icon: '👥', path: '/caregiver/patients' },
    { label: 'Activities', icon: '📋', path: '/caregiver/activities' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  useEffect(() => {
    if (patientId) {
      loadPatientDetails();
    }
  }, [patientId]);

  const loadPatientDetails = async () => {
    try {
      setLoading(true);
      
      // Mock patient data
      const mockPatient = {
        id: patientId,
        name: 'Marie Dubois',
        age: 72,
        condition: 'Alzheimer\'s Disease',
        status: 'stable',
        location: 'Home',
        address: '123 Rue de la Paix, Paris 75001',
        phone: '+33 1 23 45 67 89',
        emergencyContact: {
          name: 'Jean Dubois',
          relation: 'Spouse',
          phone: '+33 1 98 76 54 32'
        },
        medications: [
          'Donepezil 10mg - Morning',
          'Memantine 20mg - Evening',
          'Vitamin D 1000IU - Daily'
        ],
        allergies: ['Penicillin', 'Shellfish'],
        lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000)
      };

      // Mock vitals data
      const mockVitals = [
        { label: 'Heart Rate', value: '78 bpm', status: 'normal' },
        { label: 'Blood Pressure', value: '130/85 mmHg', status: 'warning' },
        { label: 'Temperature', value: '36.8°C', status: 'normal' },
        { label: 'Oxygen Saturation', value: '97%', status: 'normal' },
        { label: 'Weight', value: '65 kg', status: 'normal' }
      ];

      // Mock activities data
      const mockActivities = [
        {
          id: '1',
          title: 'Morning Medication',
          description: 'Took Donepezil 10mg as prescribed',
          time: new Date(Date.now() - 2 * 60 * 60 * 1000),
          type: 'medication'
        },
        {
          id: '2',
          title: 'Physical Exercise',
          description: '30 minutes walk in the garden',
          time: new Date(Date.now() - 4 * 60 * 60 * 1000),
          type: 'exercise'
        },
        {
          id: '3',
          title: 'Cognitive Activity',
          description: 'Completed memory exercises for 20 minutes',
          time: new Date(Date.now() - 6 * 60 * 60 * 1000),
          type: 'cognitive'
        },
        {
          id: '4',
          title: 'Meal',
          description: 'Lunch completed - good appetite',
          time: new Date(Date.now() - 8 * 60 * 60 * 1000),
          type: 'meal'
        }
      ];

      setPatient(mockPatient);
      setVitals(mockVitals);
      setActivities(mockActivities);
      
    } catch (error) {
      console.error('Error loading patient details:', error);
      toast.error('Failed to load patient details');
    } finally {
      setLoading(false);
    }
  };

  const handleRecordActivity = () => {
    navigate(`/caregiver/patients/${patientId}/record-activity`);
  };

  const handleRecordVitals = () => {
    navigate(`/caregiver/patients/${patientId}/record-vitals`);
  };

  const handleEmergencyCall = () => {
    setShowEmergencyModal(true);
  };

  const handleCallEmergencyContact = () => {
    if (patient?.emergencyContact?.phone) {
      window.open(`tel:${patient.emergencyContact.phone}`);
      setShowEmergencyModal(false);
    }
  };

  const handleCall911 = () => {
    window.open('tel:911');
    setShowEmergencyModal(false);
  };

  const handleNavigation = () => {
    navigate(`/caregiver/patients/${patientId}/navigation`);
  };

  if (loading) {
    return (
      <DashboardLayout
        title="Patient Details"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <PatientDetailContainer>
          <Card theme={theme}>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              Loading patient details...
            </div>
          </Card>
        </PatientDetailContainer>
      </DashboardLayout>
    );
  }

  if (!patient) {
    return (
      <DashboardLayout
        title="Patient Details"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <PatientDetailContainer>
          <Card theme={theme}>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              Patient not found
            </div>
          </Card>
        </PatientDetailContainer>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Patient Details"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <PatientDetailContainer>
        <BackButton
          variant="ghost"
          onClick={() => navigate('/caregiver/patients')}
          icon="←"
        >
          Back to Patients
        </BackButton>

        <PatientHeader theme={theme}>
          <PatientInfo>
            <PatientAvatar theme={theme}>
              {patient.name.charAt(0)}
            </PatientAvatar>
            <PatientDetails>
              <PatientName theme={theme}>{patient.name}</PatientName>
              <PatientMeta theme={theme}>
                <span>Age: {patient.age}</span>
                <span>Condition: {patient.condition}</span>
                <span>📍 {patient.location}</span>
              </PatientMeta>
              <StatusBadge status={patient.status}>
                {patient.status}
              </StatusBadge>
            </PatientDetails>
          </PatientInfo>

          <ActionButtons>
            <Button
              variant="primary"
              onClick={handleRecordActivity}
              icon="📝"
            >
              Record Activity
            </Button>
            <Button
              variant="outline"
              onClick={handleRecordVitals}
              icon="❤️"
            >
              Record Vitals
            </Button>
            <Button
              variant="outline"
              onClick={handleNavigation}
              icon="🧭"
            >
              Navigation
            </Button>
            <Button
              variant="danger"
              onClick={handleEmergencyCall}
              icon="🚨"
            >
              Emergency
            </Button>
          </ActionButtons>
        </PatientHeader>

        <ContentGrid>
          <SectionCard title="Recent Vitals" theme={theme}>
            <SectionTitle theme={theme}>Recent Vitals</SectionTitle>
            <VitalsList>
              {vitals.map((vital, index) => (
                <VitalItem key={index} theme={theme}>
                  <VitalLabel theme={theme}>{vital.label}</VitalLabel>
                  <VitalValue status={vital.status} theme={theme}>
                    {vital.value}
                  </VitalValue>
                </VitalItem>
              ))}
            </VitalsList>
          </SectionCard>

          <SectionCard title="Recent Activities" theme={theme}>
            <SectionTitle theme={theme}>Recent Activities</SectionTitle>
            <ActivityList>
              {activities.length === 0 ? (
                <EmptyState theme={theme}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>📋</div>
                  <p>No activities recorded yet</p>
                </EmptyState>
              ) : (
                activities.map(activity => (
                  <ActivityItem key={activity.id} theme={theme}>
                    <ActivityHeader>
                      <ActivityTitle theme={theme}>{activity.title}</ActivityTitle>
                      <ActivityTime theme={theme}>
                        {formatDate(activity.time, 'time')}
                      </ActivityTime>
                    </ActivityHeader>
                    <ActivityDescription theme={theme}>
                      {activity.description}
                    </ActivityDescription>
                  </ActivityItem>
                ))
              )}
            </ActivityList>
          </SectionCard>
        </ContentGrid>

        {/* Emergency Modal */}
        <Modal
          isOpen={showEmergencyModal}
          onClose={() => setShowEmergencyModal(false)}
          title="Emergency Contact"
          maxWidth="500px"
        >
          <div>
            <p style={{ marginBottom: '24px', color: theme.colors.text }}>
              Choose emergency contact option:
            </p>
            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <Button
                variant="danger"
                onClick={handleCall911}
                icon="🚨"
                style={{ justifyContent: 'flex-start' }}
              >
                Call Emergency Services (911)
              </Button>
              
              {patient.emergencyContact && (
                <Button
                  variant="outline"
                  onClick={handleCallEmergencyContact}
                  icon="📞"
                  style={{ justifyContent: 'flex-start' }}
                >
                  Call {patient.emergencyContact.name} ({patient.emergencyContact.relation})
                  <br />
                  <small>{patient.emergencyContact.phone}</small>
                </Button>
              )}
            </div>
            
            <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '24px' }}>
              <Button
                variant="outline"
                onClick={() => setShowEmergencyModal(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        </Modal>
      </PatientDetailContainer>
    </DashboardLayout>
  );
};

export default CaregiverPatientDetailScreen;
