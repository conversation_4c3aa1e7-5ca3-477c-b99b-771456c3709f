import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Input, Select } from '../common';
import { formatDate } from '../../utils/dateUtils';

const PatientsContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const Controls = styled.div`
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
`;

const PatientsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
`;

const PatientCard = styled(Card)`
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.lg};
  }
`;

const PatientHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
`;

const PatientAvatar = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary} 0%, ${props => props.theme.colors.primaryLight} 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: 700;
  box-shadow: ${props => props.theme.shadows.md};
`;

const PatientInfo = styled.div`
  flex: 1;
`;

const PatientName = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 4px 0;
`;

const PatientMeta = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  margin-bottom: 4px;
`;

const StatusBadge = styled.span`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  
  ${props => {
    switch (props.status) {
      case 'stable':
        return `background: #E8F5E8; color: #2E7D32;`;
      case 'attention':
        return `background: #FFF3E0; color: #F57C00;`;
      case 'critical':
        return `background: #FFEBEE; color: #C62828;`;
      default:
        return `background: #F5F5F5; color: #757575;`;
    }
  }}
`;

const PatientStats = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin: 16px 0;
`;

const StatItem = styled.div`
  text-align: center;
  padding: 8px;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 8px;
`;

const StatValue = styled.div`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const StatLabel = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
  margin-top: 2px;
`;

const PatientActions = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 16px;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: ${props => props.theme.shadows.md};
`;

const EmptyIcon = styled.div`
  font-size: 64px;
  margin-bottom: 16px;
`;

const CaregiverPatientsScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();

  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const roleColors = getRoleColors(user?.role || 'caregiver');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'My Patients', icon: '👥', path: '/caregiver/patients' },
    { label: 'Activities', icon: '📋', path: '/caregiver/activities' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  useEffect(() => {
    loadPatients();
  }, []);

  const loadPatients = async () => {
    try {
      setLoading(true);
      
      // Mock patient data for caregiver
      const mockPatients = [
        {
          id: 'patient-1',
          name: 'Marie Dubois',
          age: 72,
          condition: 'Alzheimer\'s Disease',
          status: 'stable',
          location: 'Home',
          lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
          vitals: {
            heartRate: 78,
            bloodPressure: '130/85',
            temperature: 36.8
          },
          todayActivities: 3,
          medicationsTaken: 2,
          medicationsTotal: 3,
          emergencyContact: '+33 1 23 45 67 89'
        },
        {
          id: 'patient-2',
          name: 'Pierre Martin',
          age: 68,
          condition: 'Parkinson\'s Disease',
          status: 'attention',
          location: 'Day Center',
          lastActivity: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
          vitals: {
            heartRate: 85,
            bloodPressure: '140/90',
            temperature: 37.1
          },
          todayActivities: 1,
          medicationsTaken: 1,
          medicationsTotal: 4,
          emergencyContact: '+33 1 98 76 54 32'
        },
        {
          id: 'patient-3',
          name: 'Jean Dupont',
          age: 75,
          condition: 'Dementia',
          status: 'stable',
          location: 'Home',
          lastActivity: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
          vitals: {
            heartRate: 72,
            bloodPressure: '125/80',
            temperature: 36.6
          },
          todayActivities: 4,
          medicationsTaken: 3,
          medicationsTotal: 3,
          emergencyContact: '+33 1 11 22 33 44'
        }
      ];
      
      setPatients(mockPatients);
      
    } catch (error) {
      console.error('Error loading patients:', error);
      toast.error('Failed to load patients');
    } finally {
      setLoading(false);
    }
  };

  const handlePatientClick = (patient) => {
    navigate(`/caregiver/patients/${patient.id}`);
  };

  const handleRecordActivity = (patient, e) => {
    e.stopPropagation();
    navigate(`/caregiver/patients/${patient.id}/record-activity`);
  };

  const handleRecordVitals = (patient, e) => {
    e.stopPropagation();
    navigate(`/caregiver/patients/${patient.id}/record-vitals`);
  };

  const handleEmergencyCall = (patient, e) => {
    e.stopPropagation();
    window.open(`tel:${patient.emergencyContact}`);
  };

  const filteredPatients = patients.filter(patient => {
    const matchesSearch = patient.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         patient.condition.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || patient.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <DashboardLayout
        title="My Patients"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <PatientsContainer>
          <Card theme={theme}>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              Loading patients...
            </div>
          </Card>
        </PatientsContainer>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="My Patients"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <PatientsContainer>
        <Header>
          <Title theme={theme}>My Patients ({filteredPatients.length})</Title>
          <Controls>
            <Input
              placeholder="Search patients..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{ width: '250px' }}
              icon="🔍"
            />
            <Select
              value={statusFilter}
              onChange={setStatusFilter}
              options={[
                { value: 'all', label: 'All Status' },
                { value: 'stable', label: 'Stable' },
                { value: 'attention', label: 'Needs Attention' },
                { value: 'critical', label: 'Critical' }
              ]}
              style={{ width: '150px' }}
            />
            <Button
              variant="outline"
              onClick={loadPatients}
              loading={loading}
              icon="🔄"
            >
              Refresh
            </Button>
          </Controls>
        </Header>

        {filteredPatients.length === 0 ? (
          <EmptyState theme={theme}>
            <EmptyIcon>👥</EmptyIcon>
            <h3>No patients found</h3>
            <p>
              {searchQuery || statusFilter !== 'all' 
                ? 'No patients match your current filters.'
                : 'You don\'t have any assigned patients yet.'
              }
            </p>
          </EmptyState>
        ) : (
          <PatientsGrid>
            {filteredPatients.map(patient => (
              <PatientCard
                key={patient.id}
                onClick={() => handlePatientClick(patient)}
                theme={theme}
              >
                <PatientHeader>
                  <PatientAvatar theme={theme}>
                    {patient.name.charAt(0)}
                  </PatientAvatar>
                  <PatientInfo>
                    <PatientName theme={theme}>{patient.name}</PatientName>
                    <PatientMeta theme={theme}>
                      Age: {patient.age} • {patient.condition}
                    </PatientMeta>
                    <PatientMeta theme={theme}>
                      📍 {patient.location}
                    </PatientMeta>
                  </PatientInfo>
                  <StatusBadge status={patient.status}>
                    {patient.status}
                  </StatusBadge>
                </PatientHeader>

                <PatientStats>
                  <StatItem theme={theme}>
                    <StatValue theme={theme}>{patient.todayActivities}</StatValue>
                    <StatLabel theme={theme}>Activities</StatLabel>
                  </StatItem>
                  <StatItem theme={theme}>
                    <StatValue theme={theme}>
                      {patient.medicationsTaken}/{patient.medicationsTotal}
                    </StatValue>
                    <StatLabel theme={theme}>Medications</StatLabel>
                  </StatItem>
                  <StatItem theme={theme}>
                    <StatValue theme={theme}>{patient.vitals.heartRate}</StatValue>
                    <StatLabel theme={theme}>Heart Rate</StatLabel>
                  </StatItem>
                </PatientStats>

                <div style={{ 
                  fontSize: '12px', 
                  color: theme.colors.textSecondary,
                  marginBottom: '16px'
                }}>
                  Last activity: {formatDate(patient.lastActivity, 'datetime')}
                </div>

                <PatientActions>
                  <Button
                    variant="primary"
                    size="small"
                    onClick={(e) => handleRecordActivity(patient, e)}
                    icon="📝"
                  >
                    Record Activity
                  </Button>
                  <Button
                    variant="outline"
                    size="small"
                    onClick={(e) => handleRecordVitals(patient, e)}
                    icon="❤️"
                  >
                    Vitals
                  </Button>
                  <Button
                    variant="danger"
                    size="small"
                    onClick={(e) => handleEmergencyCall(patient, e)}
                    icon="🚨"
                  >
                    Emergency
                  </Button>
                </PatientActions>
              </PatientCard>
            ))}
          </PatientsGrid>
        )}
      </PatientsContainer>
    </DashboardLayout>
  );
};

export default CaregiverPatientsScreen;
