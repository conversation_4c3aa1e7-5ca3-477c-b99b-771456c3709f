import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Input, Select } from '../common';
import { useForm } from '../../hooks/useForm';
import { validateRequired } from '../../utils/validation';

const ActivityContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const BackButton = styled(Button)`
  margin-bottom: 16px;
`;

const FormCard = styled(Card)`
  margin-bottom: 24px;
`;

const PatientInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 12px;
  margin-bottom: 24px;
`;

const PatientAvatar = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: ${props => props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: 700;
`;

const PatientDetails = styled.div`
  flex: 1;
`;

const PatientName = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 4px 0;
`;

const PatientMeta = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid ${props => props.theme.colors.borderLight};
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
`;

const FullWidthField = styled.div`
  grid-column: 1 / -1;
`;

const ActivityTypeGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
`;

const ActivityTypeCard = styled.div`
  padding: 20px;
  border: 2px solid ${props => props.selected ? props.theme.colors.primary : props.theme.colors.border};
  border-radius: 12px;
  background: ${props => props.selected ? props.theme.colors.primary + '10' : 'white'};
  cursor: pointer;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    border-color: ${props => props.theme.colors.primary};
  }
`;

const ActivityIcon = styled.div`
  font-size: 32px;
  margin-bottom: 8px;
`;

const ActivityTitle = styled.h4`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 4px 0;
`;

const ActivityDescription = styled.p`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 32px;
`;

const CaregiverRecordActivityScreen = () => {
  const { patientId } = useParams();
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();

  const [patient, setPatient] = useState(null);
  const [loading, setLoading] = useState(false);
  const [selectedActivityType, setSelectedActivityType] = useState('');

  const roleColors = getRoleColors(user?.role || 'caregiver');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'My Patients', icon: '👥', path: '/caregiver/patients' },
    { label: 'Activities', icon: '📋', path: '/caregiver/activities' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  const activityTypes = [
    {
      id: 'medication',
      title: 'Medication',
      description: 'Record medication taken',
      icon: '💊'
    },
    {
      id: 'exercise',
      title: 'Physical Exercise',
      description: 'Physical activity or exercise',
      icon: '🏃‍♂️'
    },
    {
      id: 'cognitive',
      title: 'Cognitive Activity',
      description: 'Memory exercises, puzzles',
      icon: '🧠'
    },
    {
      id: 'meal',
      title: 'Meal',
      description: 'Breakfast, lunch, dinner',
      icon: '🍽️'
    },
    {
      id: 'social',
      title: 'Social Activity',
      description: 'Interaction with others',
      icon: '👥'
    },
    {
      id: 'hygiene',
      title: 'Personal Care',
      description: 'Hygiene and self-care',
      icon: '🧼'
    },
    {
      id: 'therapy',
      title: 'Therapy Session',
      description: 'Physical or occupational therapy',
      icon: '🏥'
    },
    {
      id: 'other',
      title: 'Other',
      description: 'Other activities',
      icon: '📝'
    }
  ];

  const initialValues = {
    activityType: '',
    title: '',
    description: '',
    duration: '',
    notes: '',
    mood: '',
    assistance: '',
    location: 'home'
  };

  const validators = {
    title: (value) => validateRequired(value, 'Activity title'),
    description: (value) => validateRequired(value, 'Activity description'),
    duration: (value) => validateRequired(value, 'Duration')
  };

  const {
    values,
    errors,
    handleChange,
    handleSubmit,
    getFieldProps,
    setValue
  } = useForm(
    initialValues,
    validators,
    async (formData) => {
      if (!selectedActivityType) {
        toast.error('Please select an activity type');
        return;
      }

      setLoading(true);
      try {
        const activityData = {
          ...formData,
          activityType: selectedActivityType,
          patientId: patientId,
          caregiverId: user.uid,
          caregiverName: user.displayName,
          recordedAt: new Date().toISOString(),
          date: new Date().toISOString().split('T')[0]
        };

        // In a real implementation, you would save this to Firebase
        console.log('Recording activity:', activityData);
        
        toast.success('Activity recorded successfully!');
        navigate(`/caregiver/patients/${patientId}`);
        
      } catch (error) {
        console.error('Error recording activity:', error);
        toast.error('Failed to record activity. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  );

  useEffect(() => {
    if (patientId) {
      loadPatientInfo();
    }
  }, [patientId]);

  useEffect(() => {
    setValue('activityType', selectedActivityType);
  }, [selectedActivityType, setValue]);

  const loadPatientInfo = async () => {
    try {
      // Mock patient data
      const mockPatient = {
        id: patientId,
        name: 'Marie Dubois',
        age: 72,
        condition: 'Alzheimer\'s Disease'
      };
      
      setPatient(mockPatient);
    } catch (error) {
      console.error('Error loading patient info:', error);
    }
  };

  const handleActivityTypeSelect = (activityType) => {
    setSelectedActivityType(activityType.id);
    setValue('title', activityType.title);
  };

  return (
    <DashboardLayout
      title="Record Activity"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <ActivityContainer>
        <BackButton
          variant="ghost"
          onClick={() => navigate(`/caregiver/patients/${patientId}`)}
          icon="←"
        >
          Back to Patient Details
        </BackButton>

        <FormCard title="Record Patient Activity" theme={theme}>
          {patient && (
            <PatientInfo theme={theme}>
              <PatientAvatar theme={theme}>
                {patient.name.charAt(0)}
              </PatientAvatar>
              <PatientDetails>
                <PatientName theme={theme}>{patient.name}</PatientName>
                <PatientMeta theme={theme}>
                  Age: {patient.age} • {patient.condition}
                </PatientMeta>
              </PatientDetails>
            </PatientInfo>
          )}

          <form onSubmit={handleSubmit}>
            <FormSection>
              <SectionTitle theme={theme}>Activity Type</SectionTitle>
              <ActivityTypeGrid>
                {activityTypes.map(activityType => (
                  <ActivityTypeCard
                    key={activityType.id}
                    selected={selectedActivityType === activityType.id}
                    onClick={() => handleActivityTypeSelect(activityType)}
                    theme={theme}
                  >
                    <ActivityIcon>{activityType.icon}</ActivityIcon>
                    <ActivityTitle theme={theme}>{activityType.title}</ActivityTitle>
                    <ActivityDescription theme={theme}>
                      {activityType.description}
                    </ActivityDescription>
                  </ActivityTypeCard>
                ))}
              </ActivityTypeGrid>
            </FormSection>

            <FormSection>
              <SectionTitle theme={theme}>Activity Details</SectionTitle>
              <FormGrid>
                <Input
                  {...getFieldProps('title')}
                  label="Activity Title *"
                  placeholder="Enter activity title"
                />
                <Input
                  {...getFieldProps('duration')}
                  label="Duration *"
                  placeholder="e.g., 30 minutes"
                />
                <Select
                  {...getFieldProps('mood')}
                  label="Patient Mood"
                  options={[
                    { value: '', label: 'Select mood' },
                    { value: 'excellent', label: 'Excellent' },
                    { value: 'good', label: 'Good' },
                    { value: 'neutral', label: 'Neutral' },
                    { value: 'poor', label: 'Poor' },
                    { value: 'difficult', label: 'Difficult' }
                  ]}
                />
                <Select
                  {...getFieldProps('assistance')}
                  label="Assistance Level"
                  options={[
                    { value: '', label: 'Select assistance level' },
                    { value: 'independent', label: 'Independent' },
                    { value: 'minimal', label: 'Minimal Assistance' },
                    { value: 'moderate', label: 'Moderate Assistance' },
                    { value: 'maximum', label: 'Maximum Assistance' },
                    { value: 'dependent', label: 'Fully Dependent' }
                  ]}
                />
                <Select
                  {...getFieldProps('location')}
                  label="Location"
                  options={[
                    { value: 'home', label: 'Home' },
                    { value: 'garden', label: 'Garden' },
                    { value: 'daycare', label: 'Day Care Center' },
                    { value: 'clinic', label: 'Clinic' },
                    { value: 'community', label: 'Community Center' },
                    { value: 'other', label: 'Other' }
                  ]}
                />
              </FormGrid>
              
              <FullWidthField>
                <Input
                  {...getFieldProps('description')}
                  label="Description *"
                  placeholder="Describe the activity in detail"
                  multiline
                  rows={3}
                />
              </FullWidthField>
              
              <FullWidthField>
                <Input
                  {...getFieldProps('notes')}
                  label="Additional Notes"
                  placeholder="Any additional observations or notes"
                  multiline
                  rows={3}
                />
              </FullWidthField>
            </FormSection>

            <ButtonGroup>
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate(`/caregiver/patients/${patientId}`)}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={loading}
                disabled={!selectedActivityType}
              >
                Record Activity
              </Button>
            </ButtonGroup>
          </form>
        </FormCard>
      </ActivityContainer>
    </DashboardLayout>
  );
};

export default CaregiverRecordActivityScreen;
