import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useChat } from '../../contexts/ChatContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Input, Modal } from '../common';
import { formatDate } from '../../utils/dateUtils';

const ChatContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px);
  max-width: 1000px;
  margin: 0 auto;
  background: white;
  border-radius: 16px;
  box-shadow: ${props => props.theme.shadows.lg};
  overflow: hidden;
`;

const ChatHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: ${props => props.theme.colors.primary};
  color: white;
`;

const ParticipantInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const ParticipantAvatar = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 18px;
`;

const ParticipantDetails = styled.div``;

const ParticipantName = styled.h3`
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px 0;
`;

const ParticipantStatus = styled.div`
  font-size: 14px;
  opacity: 0.9;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const StatusDot = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => props.online ? '#4CAF50' : '#757575'};
`;

const ChatActions = styled.div`
  display: flex;
  gap: 12px;
`;

const ActionButton = styled(Button)`
  padding: 8px 12px;
  font-size: 14px;
`;

const MessagesArea = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 20px 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const MessageGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: ${props => props.isOwn ? 'flex-end' : 'flex-start'};
`;

const MessageBubble = styled.div`
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  background: ${props => props.isOwn ? props.theme.colors.primary : '#F0F0F0'};
  color: ${props => props.isOwn ? 'white' : props.theme.colors.text};
  word-wrap: break-word;
  position: relative;
`;

const MessageText = styled.div`
  font-size: 15px;
  line-height: 1.4;
`;

const MessageTime = styled.div`
  font-size: 12px;
  opacity: 0.7;
  margin-top: 4px;
  text-align: ${props => props.isOwn ? 'right' : 'left'};
`;

const MessageSender = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
  margin-bottom: 4px;
  padding: 0 4px;
`;

const SystemMessage = styled.div`
  text-align: center;
  padding: 8px 16px;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 12px;
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 8px auto;
  max-width: 300px;
`;

const MessageInput = styled.div`
  padding: 20px 24px;
  border-top: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.lightGray};
`;

const InputContainer = styled.div`
  display: flex;
  gap: 12px;
  align-items: flex-end;
`;

const MessageInputField = styled.textarea`
  flex: 1;
  min-height: 44px;
  max-height: 120px;
  padding: 12px 16px;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 22px;
  outline: none;
  font-size: 15px;
  font-family: inherit;
  resize: none;
  background: white;

  &:focus {
    border-color: ${props => props.theme.colors.primary};
  }

  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
  }
`;

const SendButton = styled(Button)`
  border-radius: 50%;
  width: 44px;
  height: 44px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
`;

const AttachmentButton = styled.button`
  width: 44px;
  height: 44px;
  border: none;
  background: ${props => props.theme.colors.border};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  color: ${props => props.theme.colors.textSecondary};
  transition: all 0.3s ease;

  &:hover {
    background: ${props => props.theme.colors.primary};
    color: white;
  }
`;

const TypingIndicator = styled.div`
  padding: 8px 16px;
  color: ${props => props.theme.colors.textSecondary};
  font-style: italic;
  font-size: 14px;
`;

const EmptyState = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: ${props => props.theme.colors.textSecondary};
`;

const EmptyIcon = styled.div`
  font-size: 64px;
  margin-bottom: 16px;
`;

const ChatRoomScreen = () => {
  const { conversationId } = useParams();
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const { sendMessage, getConversationMessages } = useChat();
  const navigate = useNavigate();

  const [conversation, setConversation] = useState(null);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [loading, setLoading] = useState(true);
  const [showVideoCallModal, setShowVideoCallModal] = useState(false);

  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Messages', icon: '💬', path: '/messages' },
    { label: 'Appointments', icon: '📅', path: '/appointments' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  useEffect(() => {
    if (conversationId) {
      loadConversation();
      loadMessages();
    }
  }, [conversationId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const loadConversation = async () => {
    try {
      // Mock conversation data
      const mockConversation = {
        id: conversationId,
        type: 'medical_consultation',
        title: 'Medical Consultation',
        participants: [
          {
            id: 'doctor-1',
            name: 'Dr. Sarah Johnson',
            role: 'doctor',
            avatar: 'SJ',
            status: 'online',
            lastSeen: new Date()
          },
          {
            id: user.uid,
            name: user.displayName,
            role: user.role,
            avatar: user.displayName?.charAt(0) || 'U',
            status: 'online',
            lastSeen: new Date()
          }
        ],
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        updatedAt: new Date()
      };
      
      setConversation(mockConversation);
    } catch (error) {
      console.error('Error loading conversation:', error);
      toast.error('Failed to load conversation');
    }
  };

  const loadMessages = async () => {
    try {
      setLoading(true);
      
      // Mock messages data
      const mockMessages = [
        {
          id: '1',
          text: 'Hello! How are you feeling today?',
          senderId: 'doctor-1',
          senderName: 'Dr. Sarah Johnson',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          type: 'text'
        },
        {
          id: '2',
          text: 'Hi Doctor! I\'m feeling much better today. The medication seems to be working well.',
          senderId: user.uid,
          senderName: user.displayName,
          timestamp: new Date(Date.now() - 90 * 60 * 1000),
          type: 'text'
        },
        {
          id: '3',
          text: 'That\'s great to hear! Have you been taking the medication as prescribed?',
          senderId: 'doctor-1',
          senderName: 'Dr. Sarah Johnson',
          timestamp: new Date(Date.now() - 85 * 60 * 1000),
          type: 'text'
        },
        {
          id: '4',
          text: 'Yes, I\'ve been taking it every morning with breakfast as you recommended.',
          senderId: user.uid,
          senderName: user.displayName,
          timestamp: new Date(Date.now() - 80 * 60 * 1000),
          type: 'text'
        },
        {
          id: '5',
          text: 'Perfect! Let\'s schedule a follow-up appointment for next week to monitor your progress.',
          senderId: 'doctor-1',
          senderName: 'Dr. Sarah Johnson',
          timestamp: new Date(Date.now() - 75 * 60 * 1000),
          type: 'text'
        }
      ];
      
      setMessages(mockMessages);
    } catch (error) {
      console.error('Error loading messages:', error);
      toast.error('Failed to load messages');
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;

    try {
      const messageData = {
        text: newMessage,
        conversationId,
        senderId: user.uid,
        senderName: user.displayName,
        timestamp: new Date(),
        type: 'text'
      };

      // Add message to local state immediately
      setMessages(prev => [...prev, { ...messageData, id: Date.now().toString() }]);
      setNewMessage('');

      // In a real implementation, you would send this to Firebase
      await sendMessage(messageData);

      // Simulate response from other participant
      setTimeout(() => {
        const response = {
          id: (Date.now() + 1).toString(),
          text: 'Thank you for the update. I\'ll review your information.',
          senderId: 'doctor-1',
          senderName: 'Dr. Sarah Johnson',
          timestamp: new Date(),
          type: 'text'
        };
        setMessages(prev => [...prev, response]);
      }, 2000);

    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleVideoCall = () => {
    setShowVideoCallModal(true);
  };

  const startVideoCall = () => {
    setShowVideoCallModal(false);
    navigate(`/video-call/${conversationId}`);
  };

  const handleVoiceCall = () => {
    toast.info('Voice call feature coming soon!');
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const getOtherParticipant = () => {
    return conversation?.participants?.find(p => p.id !== user.uid);
  };

  const groupMessagesBySender = (messages) => {
    const groups = [];
    let currentGroup = null;

    messages.forEach(message => {
      if (!currentGroup || currentGroup.senderId !== message.senderId) {
        currentGroup = {
          senderId: message.senderId,
          senderName: message.senderName,
          messages: [message],
          isOwn: message.senderId === user.uid
        };
        groups.push(currentGroup);
      } else {
        currentGroup.messages.push(message);
      }
    });

    return groups;
  };

  if (loading) {
    return (
      <DashboardLayout
        title="Chat"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <ChatContainer theme={theme}>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            height: '400px'
          }}>
            Loading conversation...
          </div>
        </ChatContainer>
      </DashboardLayout>
    );
  }

  if (!conversation) {
    return (
      <DashboardLayout
        title="Chat"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <ChatContainer theme={theme}>
          <EmptyState theme={theme}>
            <EmptyIcon>💬</EmptyIcon>
            <h3>Conversation not found</h3>
            <p>The conversation you're looking for doesn't exist or has been deleted.</p>
            <Button 
              variant="primary" 
              onClick={() => navigate('/messages')}
              style={{ marginTop: '16px' }}
            >
              Back to Messages
            </Button>
          </EmptyState>
        </ChatContainer>
      </DashboardLayout>
    );
  }

  const otherParticipant = getOtherParticipant();
  const messageGroups = groupMessagesBySender(messages);

  return (
    <DashboardLayout
      title="Chat"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <ChatContainer theme={theme}>
        <ChatHeader theme={theme}>
          <ParticipantInfo>
            <ParticipantAvatar>
              {otherParticipant?.avatar || '?'}
            </ParticipantAvatar>
            <ParticipantDetails>
              <ParticipantName>{otherParticipant?.name || 'Unknown'}</ParticipantName>
              <ParticipantStatus>
                <StatusDot online={otherParticipant?.status === 'online'} />
                {otherParticipant?.status === 'online' ? 'Online' : 'Offline'}
              </ParticipantStatus>
            </ParticipantDetails>
          </ParticipantInfo>
          
          <ChatActions>
            <ActionButton
              variant="light"
              onClick={handleVoiceCall}
              icon="📞"
            >
              Call
            </ActionButton>
            <ActionButton
              variant="light"
              onClick={handleVideoCall}
              icon="📹"
            >
              Video
            </ActionButton>
          </ChatActions>
        </ChatHeader>

        <MessagesArea>
          {messageGroups.length === 0 ? (
            <EmptyState theme={theme}>
              <EmptyIcon>💬</EmptyIcon>
              <h3>No messages yet</h3>
              <p>Start the conversation by sending a message below.</p>
            </EmptyState>
          ) : (
            messageGroups.map((group, groupIndex) => (
              <MessageGroup key={groupIndex} isOwn={group.isOwn}>
                {!group.isOwn && (
                  <MessageSender theme={theme}>{group.senderName}</MessageSender>
                )}
                {group.messages.map((message, messageIndex) => (
                  <MessageBubble key={message.id} isOwn={group.isOwn} theme={theme}>
                    <MessageText>{message.text}</MessageText>
                    {messageIndex === group.messages.length - 1 && (
                      <MessageTime isOwn={group.isOwn}>
                        {formatDate(message.timestamp, 'time')}
                      </MessageTime>
                    )}
                  </MessageBubble>
                ))}
              </MessageGroup>
            ))
          )}
          
          {isTyping && (
            <TypingIndicator theme={theme}>
              {otherParticipant?.name} is typing...
            </TypingIndicator>
          )}
          
          <div ref={messagesEndRef} />
        </MessagesArea>

        <MessageInput theme={theme}>
          <InputContainer>
            <AttachmentButton theme={theme} title="Attach file">
              📎
            </AttachmentButton>
            <MessageInputField
              ref={inputRef}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              theme={theme}
            />
            <SendButton
              variant="primary"
              onClick={handleSendMessage}
              disabled={!newMessage.trim()}
            >
              ➤
            </SendButton>
          </InputContainer>
        </MessageInput>

        {/* Video Call Confirmation Modal */}
        <Modal
          isOpen={showVideoCallModal}
          onClose={() => setShowVideoCallModal(false)}
          title="Start Video Call"
          maxWidth="400px"
        >
          <div>
            <p style={{ marginBottom: '24px', color: theme.colors.text }}>
              Start a video call with {otherParticipant?.name}?
            </p>
            
            <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
              <Button
                variant="outline"
                onClick={() => setShowVideoCallModal(false)}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={startVideoCall}
              >
                Start Call
              </Button>
            </div>
          </div>
        </Modal>
      </ChatContainer>
    </DashboardLayout>
  );
};

export default ChatRoomScreen;
