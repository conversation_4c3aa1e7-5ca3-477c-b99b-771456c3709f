import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Input, Card } from '../common';
import { formatDate } from '../../utils/dateUtils';

const ChatContainer = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
`;

const ChatHeader = styled.div`
  background: white;
  padding: 20px;
  border-radius: 12px 12px 0 0;
  box-shadow: ${props => props.theme.shadows.sm};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const ChatTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const ChatSubtitle = styled.p`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
`;

const MessagesContainer = styled.div`
  flex: 1;
  background: white;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const MessageBubble = styled.div`
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;

  ${props => props.isOwn ? `
    align-self: flex-end;
    background: ${props.theme.colors.primary};
    color: white;
    border-bottom-right-radius: 4px;
  ` : `
    align-self: flex-start;
    background: ${props.theme.colors.lightGray};
    color: ${props.theme.colors.text};
    border-bottom-left-radius: 4px;
  `}
`;

const MessageText = styled.p`
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
`;

const MessageTime = styled.div`
  font-size: 11px;
  margin-top: 4px;
  opacity: 0.7;
`;

const MessageSender = styled.div`
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
  opacity: 0.8;
`;

const ChatInput = styled.div`
  background: white;
  padding: 20px;
  border-radius: 0 0 12px 12px;
  box-shadow: ${props => props.theme.shadows.sm};
  border-top: 1px solid ${props => props.theme.colors.border};
  display: flex;
  gap: 12px;
  align-items: flex-end;
`;

const MessageInput = styled(Input)`
  flex: 1;
  margin: 0;
`;

const SendButton = styled(Button)`
  min-width: 80px;
`;

const EmptyState = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: ${props => props.theme.colors.textSecondary};
`;

const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const ChatScreen = () => {
  const { chatId } = useParams();
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();
  const messagesEndRef = useRef(null);

  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [chatInfo, setChatInfo] = useState(null);

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Messages', icon: '💬', path: '/chat' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  useEffect(() => {
    if (chatId) {
      loadChatMessages();
    } else {
      setLoading(false);
    }
  }, [chatId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadChatMessages = async () => {
    try {
      setLoading(true);

      // Mock chat info
      setChatInfo({
        id: chatId,
        title: 'Medical Consultation',
        type: 'consultation',
        participants: ['patient', 'doctor']
      });

      // Mock messages
      const mockMessages = [
        {
          id: '1',
          senderId: 'doctor-123',
          senderName: 'Dr. Sarah Johnson',
          senderRole: 'doctor',
          text: 'Hello! How are you feeling today?',
          createdAt: { toDate: () => new Date(Date.now() - 60000) }
        },
        {
          id: '2',
          senderId: user.uid,
          senderName: user.displayName,
          senderRole: user.role,
          text: 'Hi Doctor, I\'m feeling much better since our last appointment.',
          createdAt: { toDate: () => new Date(Date.now() - 30000) }
        }
      ];

      setMessages(mockMessages);
    } catch (error) {
      console.error('Error loading chat messages:', error);
      toast.error('Failed to load messages');
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || sending) return;

    try {
      setSending(true);

      const messageData = {
        id: Date.now().toString(),
        senderId: user.uid,
        senderName: user.displayName,
        senderRole: user.role,
        text: newMessage.trim(),
        createdAt: { toDate: () => new Date() }
      };

      setMessages(prev => [...prev, messageData]);
      setNewMessage('');

    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    } finally {
      setSending(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!chatId) {
    return (
      <DashboardLayout
        title="Messages"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <ChatContainer>
          <Card theme={theme}>
            <EmptyState theme={theme}>
              <EmptyIcon>💬</EmptyIcon>
              <h3>Messages</h3>
              <p>Select a conversation to start messaging or create a new chat.</p>
              <Button
                variant="primary"
                onClick={() => navigate('/chat/consultation')}
                style={{ marginTop: '16px' }}
              >
                Start Medical Consultation
              </Button>
            </EmptyState>
          </Card>
        </ChatContainer>
      </DashboardLayout>
    );
  }

  if (loading) {
    return (
      <DashboardLayout
        title="Chat"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <ChatContainer>
          <Card theme={theme}>
            <div style={{ padding: '40px', textAlign: 'center' }}>
              Loading chat...
            </div>
          </Card>
        </ChatContainer>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Chat"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <ChatContainer>
        <ChatHeader theme={theme}>
          <ChatTitle theme={theme}>
            {chatInfo?.title || 'Chat'}
          </ChatTitle>
          <ChatSubtitle theme={theme}>
            Secure medical consultation chat
          </ChatSubtitle>
        </ChatHeader>

        <MessagesContainer>
          {messages.length === 0 ? (
            <EmptyState theme={theme}>
              <EmptyIcon>💬</EmptyIcon>
              <h3>No messages yet</h3>
              <p>Start the conversation by sending a message below.</p>
            </EmptyState>
          ) : (
            <>
              {messages.map((message, index) => {
                const isOwn = message.senderId === user.uid;
                const showSender = !isOwn && (index === 0 || messages[index - 1].senderId !== message.senderId);

                return (
                  <MessageBubble key={message.id} isOwn={isOwn} theme={theme}>
                    {showSender && (
                      <MessageSender>
                        {message.senderName} ({message.senderRole})
                      </MessageSender>
                    )}
                    <MessageText>{message.text}</MessageText>
                    <MessageTime>
                      {formatDate(message.createdAt.toDate(), 'time')}
                    </MessageTime>
                  </MessageBubble>
                );
              })}

              <div ref={messagesEndRef} />
            </>
          )}
        </MessagesContainer>

        <ChatInput theme={theme}>
          <MessageInput
            placeholder="Type your message..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            multiline
            rows={1}
          />
          <SendButton
            variant="primary"
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || sending}
            loading={sending}
          >
            Send
          </SendButton>
        </ChatInput>
      </ChatContainer>
    </DashboardLayout>
  );
};

export default ChatScreen;
