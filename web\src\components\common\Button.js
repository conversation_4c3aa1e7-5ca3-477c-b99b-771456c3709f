import React from 'react';
import styled from 'styled-components';
import { useTheme } from '../../contexts/ThemeContext';

const StyledButton = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: ${props => {
    switch (props.size) {
      case 'small': return '8px 16px';
      case 'large': return '16px 32px';
      default: return '12px 24px';
    }
  }};
  border-radius: ${props => props.rounded ? '50px' : '8px'};
  font-size: ${props => {
    switch (props.size) {
      case 'small': return '14px';
      case 'large': return '18px';
      default: return '16px';
    }
  }};
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  min-width: ${props => props.fullWidth ? '100%' : 'auto'};
  width: ${props => props.fullWidth ? '100%' : 'auto'};

  /* Variant styles */
  ${props => {
    const { variant, theme } = props;
    
    switch (variant) {
      case 'primary':
        return `
          background: linear-gradient(135deg, ${theme.colors.primary} 0%, ${theme.colors.primaryDark} 100%);
          color: white;
          box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
          
          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(76, 175, 80, 0.4);
          }
          
          &:active:not(:disabled) {
            transform: translateY(0);
          }
        `;
        
      case 'secondary':
        return `
          background: transparent;
          color: ${theme.colors.primary};
          border: 2px solid ${theme.colors.primary};
          
          &:hover:not(:disabled) {
            background: ${theme.colors.primary};
            color: white;
            transform: translateY(-1px);
          }
        `;
        
      case 'outline':
        return `
          background: transparent;
          color: ${theme.colors.textSecondary};
          border: 2px solid ${theme.colors.border};
          
          &:hover:not(:disabled) {
            background: ${theme.colors.lightGray};
            border-color: ${theme.colors.textSecondary};
            color: ${theme.colors.text};
          }
        `;
        
      case 'ghost':
        return `
          background: transparent;
          color: ${theme.colors.textSecondary};
          border: none;
          
          &:hover:not(:disabled) {
            background: ${theme.colors.lightGray};
            color: ${theme.colors.text};
          }
        `;
        
      case 'danger':
        return `
          background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
          color: white;
          box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
          
          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(244, 67, 54, 0.4);
          }
        `;
        
      case 'success':
        return `
          background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
          color: white;
          box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
          
          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(76, 175, 80, 0.4);
          }
        `;
        
      case 'warning':
        return `
          background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
          color: white;
          box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
          
          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(255, 152, 0, 0.4);
          }
        `;
        
      case 'info':
        return `
          background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
          color: white;
          box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
          
          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(33, 150, 243, 0.4);
          }
        `;
        
      default:
        return `
          background: ${theme.colors.lightGray};
          color: ${theme.colors.text};
          
          &:hover:not(:disabled) {
            background: ${theme.colors.border};
          }
        `;
    }
  }}

  /* Disabled state */
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }

  /* Loading state */
  ${props => props.loading && `
    pointer-events: none;
    
    &::after {
      content: '';
      position: absolute;
      width: 16px;
      height: 16px;
      margin: auto;
      border: 2px solid transparent;
      border-top-color: currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `}

  /* Focus styles */
  &:focus {
    outline: 2px solid ${props => props.theme.colors.primary};
    outline-offset: 2px;
  }

  /* Icon styles */
  .button-icon {
    display: flex;
    align-items: center;
    font-size: ${props => {
      switch (props.size) {
        case 'small': return '16px';
        case 'large': return '24px';
        default: return '20px';
      }
    }};
  }

  /* Text hidden when loading */
  ${props => props.loading && `
    color: transparent;
  `}
`;

const Button = ({
  children,
  variant = 'default',
  size = 'medium',
  loading = false,
  disabled = false,
  fullWidth = false,
  rounded = false,
  icon,
  iconPosition = 'left',
  onClick,
  type = 'button',
  className,
  style,
  ...props
}) => {
  const { theme } = useTheme();

  const handleClick = (event) => {
    if (loading || disabled) {
      event.preventDefault();
      return;
    }
    
    if (onClick) {
      onClick(event);
    }
  };

  return (
    <StyledButton
      type={type}
      variant={variant}
      size={size}
      loading={loading}
      disabled={disabled || loading}
      fullWidth={fullWidth}
      rounded={rounded}
      onClick={handleClick}
      className={className}
      style={style}
      theme={theme}
      {...props}
    >
      {icon && iconPosition === 'left' && (
        <span className="button-icon">{icon}</span>
      )}
      
      {!loading && children}
      
      {icon && iconPosition === 'right' && (
        <span className="button-icon">{icon}</span>
      )}
    </StyledButton>
  );
};

// Pre-built button variants
export const PrimaryButton = (props) => (
  <Button variant="primary" {...props} />
);

export const SecondaryButton = (props) => (
  <Button variant="secondary" {...props} />
);

export const OutlineButton = (props) => (
  <Button variant="outline" {...props} />
);

export const GhostButton = (props) => (
  <Button variant="ghost" {...props} />
);

export const DangerButton = (props) => (
  <Button variant="danger" {...props} />
);

export const SuccessButton = (props) => (
  <Button variant="success" {...props} />
);

export const WarningButton = (props) => (
  <Button variant="warning" {...props} />
);

export const InfoButton = (props) => (
  <Button variant="info" {...props} />
);

// Icon button component
export const IconButton = ({
  icon,
  size = 'medium',
  variant = 'ghost',
  rounded = true,
  ...props
}) => {
  const iconSize = {
    small: '32px',
    medium: '40px',
    large: '48px'
  };

  return (
    <StyledButton
      variant={variant}
      size={size}
      rounded={rounded}
      style={{
        width: iconSize[size],
        height: iconSize[size],
        padding: 0,
        minWidth: iconSize[size]
      }}
      {...props}
    >
      <span className="button-icon">{icon}</span>
    </StyledButton>
  );
};

// Floating Action Button
export const FloatingActionButton = ({
  icon,
  position = 'bottom-right',
  ...props
}) => {
  const positions = {
    'bottom-right': { bottom: '24px', right: '24px' },
    'bottom-left': { bottom: '24px', left: '24px' },
    'top-right': { top: '24px', right: '24px' },
    'top-left': { top: '24px', left: '24px' }
  };

  return (
    <div style={{
      position: 'fixed',
      ...positions[position],
      zIndex: 1000
    }}>
      <IconButton
        icon={icon}
        variant="primary"
        size="large"
        style={{
          width: '56px',
          height: '56px',
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2)'
        }}
        {...props}
      />
    </div>
  );
};

export default Button;
