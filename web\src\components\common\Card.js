import React from 'react';
import styled from 'styled-components';
import { useTheme } from '../../contexts/ThemeContext';

const StyledCard = styled.div`
  background: white;
  border-radius: ${props => {
    switch (props.borderRadius) {
      case 'small': return '8px';
      case 'large': return '20px';
      case 'none': return '0';
      default: return '12px';
    }
  }};
  box-shadow: ${props => {
    switch (props.elevation) {
      case 'none': return 'none';
      case 'small': return props.theme.shadows?.sm || '0 1px 3px rgba(0, 0, 0, 0.12)';
      case 'medium': return props.theme.shadows?.md || '0 3px 6px rgba(0, 0, 0, 0.16)';
      case 'large': return props.theme.shadows?.lg || '0 10px 20px rgba(0, 0, 0, 0.19)';
      case 'extra-large': return props.theme.shadows?.xl || '0 14px 28px rgba(0, 0, 0, 0.25)';
      default: return props.theme.shadows?.sm || '0 1px 3px rgba(0, 0, 0, 0.12)';
    }
  }};
  border: ${props => props.bordered ? `1px solid ${props.theme.colors?.borderLight || '#F0F0F0'}` : 'none'};
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  cursor: ${props => props.clickable ? 'pointer' : 'default'};
  
  ${props => props.clickable && `
    &:hover {
      transform: translateY(-2px);
      box-shadow: ${props.theme.shadows?.lg || '0 10px 20px rgba(0, 0, 0, 0.19)'};
    }
    
    &:active {
      transform: translateY(0);
    }
  `}

  ${props => props.fullHeight && `
    height: 100%;
    display: flex;
    flex-direction: column;
  `}

  ${props => props.accentColor && `
    border-top: 4px solid ${props.accentColor};
  `}

  ${props => props.gradient && `
    background: linear-gradient(135deg, ${props.gradient.from} 0%, ${props.gradient.to} 100%);
    color: white;
  `}
`;

const CardHeader = styled.div`
  padding: ${props => {
    switch (props.size) {
      case 'small': return '12px 16px';
      case 'large': return '24px 32px';
      default: return '20px 24px';
    }
  }};
  border-bottom: ${props => props.divider ? `1px solid ${props.theme.colors?.borderLight || '#F0F0F0'}` : 'none'};
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  ${props => props.sticky && `
    position: sticky;
    top: 0;
    background: white;
    z-index: 10;
  `}
`;

const CardTitle = styled.h3`
  font-size: ${props => {
    switch (props.size) {
      case 'small': return '16px';
      case 'large': return '24px';
      default: return '20px';
    }
  }};
  font-weight: 600;
  color: ${props => props.theme.colors?.text || '#212121'};
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const CardSubtitle = styled.p`
  font-size: 14px;
  color: ${props => props.theme.colors?.textSecondary || '#757575'};
  margin: 4px 0 0 0;
  line-height: 1.4;
`;

const CardActions = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const CardBody = styled.div`
  padding: ${props => {
    if (props.noPadding) return '0';
    switch (props.size) {
      case 'small': return '12px 16px';
      case 'large': return '24px 32px';
      default: return '20px 24px';
    }
  }};
  
  ${props => props.scrollable && `
    overflow-y: auto;
    max-height: ${props.maxHeight || '300px'};
  `}
  
  ${props => props.fullHeight && `
    flex: 1;
    display: flex;
    flex-direction: column;
  `}
`;

const CardFooter = styled.div`
  padding: ${props => {
    switch (props.size) {
      case 'small': return '12px 16px';
      case 'large': return '24px 32px';
      default: return '20px 24px';
    }
  }};
  border-top: ${props => props.divider ? `1px solid ${props.theme.colors?.borderLight || '#F0F0F0'}` : 'none'};
  background-color: ${props => props.background || 'transparent'};
  display: flex;
  align-items: center;
  justify-content: ${props => props.align || 'flex-end'};
  gap: 12px;
  
  ${props => props.sticky && `
    position: sticky;
    bottom: 0;
    z-index: 10;
  `}
`;

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
`;

const LoadingSpinner = styled.div`
  width: 32px;
  height: 32px;
  border: 3px solid ${props => props.theme.colors?.borderLight || '#F0F0F0'};
  border-top: 3px solid ${props => props.theme.colors?.primary || '#4CAF50'};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const Card = ({
  children,
  title,
  subtitle,
  icon,
  actions,
  footer,
  size = 'medium',
  elevation = 'small',
  borderRadius = 'medium',
  bordered = false,
  clickable = false,
  fullHeight = false,
  accentColor,
  gradient,
  loading = false,
  headerDivider = false,
  footerDivider = false,
  stickyHeader = false,
  stickyFooter = false,
  scrollable = false,
  maxHeight,
  noPadding = false,
  footerAlign = 'flex-end',
  footerBackground,
  onClick,
  className,
  style,
  ...props
}) => {
  const { theme } = useTheme();

  const handleClick = (event) => {
    if (clickable && onClick && !loading) {
      onClick(event);
    }
  };

  return (
    <StyledCard
      elevation={elevation}
      borderRadius={borderRadius}
      bordered={bordered}
      clickable={clickable}
      fullHeight={fullHeight}
      accentColor={accentColor}
      gradient={gradient}
      onClick={handleClick}
      className={className}
      style={style}
      theme={theme}
      {...props}
    >
      {loading && (
        <LoadingOverlay>
          <LoadingSpinner theme={theme} />
        </LoadingOverlay>
      )}

      {(title || subtitle || actions) && (
        <CardHeader
          size={size}
          divider={headerDivider}
          sticky={stickyHeader}
          theme={theme}
        >
          <div>
            <CardTitle size={size} theme={theme}>
              {icon && <span>{icon}</span>}
              {title}
            </CardTitle>
            {subtitle && (
              <CardSubtitle theme={theme}>{subtitle}</CardSubtitle>
            )}
          </div>
          {actions && (
            <CardActions>{actions}</CardActions>
          )}
        </CardHeader>
      )}

      <CardBody
        size={size}
        noPadding={noPadding}
        scrollable={scrollable}
        maxHeight={maxHeight}
        fullHeight={fullHeight}
      >
        {children}
      </CardBody>

      {footer && (
        <CardFooter
          size={size}
          divider={footerDivider}
          sticky={stickyFooter}
          align={footerAlign}
          background={footerBackground}
          theme={theme}
        >
          {footer}
        </CardFooter>
      )}
    </StyledCard>
  );
};

// Pre-built card variants
export const StatCard = ({
  title,
  value,
  change,
  changeType,
  icon,
  color,
  ...props
}) => {
  const { theme } = useTheme();

  return (
    <Card
      accentColor={color || theme.colors?.primary}
      clickable
      {...props}
    >
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div>
          <div style={{ 
            fontSize: '32px', 
            fontWeight: '700', 
            color: color || theme.colors?.primary,
            marginBottom: '4px'
          }}>
            {value}
          </div>
          <div style={{ 
            fontSize: '14px', 
            color: theme.colors?.textSecondary,
            marginBottom: '8px'
          }}>
            {title}
          </div>
          {change && (
            <div style={{ 
              fontSize: '12px', 
              color: changeType === 'positive' ? '#4CAF50' : changeType === 'negative' ? '#F44336' : theme.colors?.textSecondary,
              fontWeight: '600'
            }}>
              {changeType === 'positive' ? '↗️' : changeType === 'negative' ? '↘️' : ''} {change}
            </div>
          )}
        </div>
        {icon && (
          <div style={{ fontSize: '48px', opacity: 0.3 }}>
            {icon}
          </div>
        )}
      </div>
    </Card>
  );
};

export const ActionCard = ({
  title,
  description,
  icon,
  buttonText = 'Action',
  onAction,
  ...props
}) => {
  const { theme } = useTheme();

  return (
    <Card
      title={title}
      icon={icon}
      clickable
      onClick={onAction}
      {...props}
    >
      <div style={{ textAlign: 'center', padding: '20px 0' }}>
        {icon && (
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>
            {icon}
          </div>
        )}
        <div style={{ 
          fontSize: '16px', 
          fontWeight: '600', 
          marginBottom: '8px',
          color: theme.colors?.text
        }}>
          {title}
        </div>
        {description && (
          <div style={{ 
            fontSize: '14px', 
            color: theme.colors?.textSecondary,
            marginBottom: '16px'
          }}>
            {description}
          </div>
        )}
        <button style={{
          background: theme.colors?.primary,
          color: 'white',
          border: 'none',
          borderRadius: '8px',
          padding: '8px 16px',
          fontSize: '14px',
          fontWeight: '600',
          cursor: 'pointer'
        }}>
          {buttonText}
        </button>
      </div>
    </Card>
  );
};

export default Card;
