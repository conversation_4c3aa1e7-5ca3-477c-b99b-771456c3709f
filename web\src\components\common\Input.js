import React, { useState, forwardRef } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../contexts/ThemeContext';

const InputContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
`;

const Label = styled.label`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin-bottom: 4px;
  
  ${props => props.required && `
    &::after {
      content: ' *';
      color: #F44336;
    }
  `}
`;

const InputWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;
`;

const StyledInput = styled.input`
  width: 100%;
  padding: ${props => {
    switch (props.size) {
      case 'small': return '8px 12px';
      case 'large': return '16px 20px';
      default: return '12px 16px';
    }
  }};
  padding-left: ${props => props.hasLeftIcon ? '44px' : props.size === 'large' ? '20px' : '16px'};
  padding-right: ${props => props.hasRightIcon ? '44px' : props.size === 'large' ? '20px' : '16px'};
  border: 2px solid ${props => {
    if (props.error) return '#F44336';
    if (props.success) return '#4CAF50';
    return props.theme.colors.border;
  }};
  border-radius: ${props => props.rounded ? '50px' : '8px'};
  font-size: ${props => {
    switch (props.size) {
      case 'small': return '14px';
      case 'large': return '18px';
      default: return '16px';
    }
  }};
  font-family: inherit;
  background-color: ${props => props.disabled ? props.theme.colors.lightGray : 'white'};
  color: ${props => props.theme.colors.text};
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.error ? '#F44336' : props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.error ? 'rgba(244, 67, 54, 0.1)' : `${props.theme.colors.primary}20`};
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
    opacity: 0.7;
  }

  /* Remove default styling for specific input types */
  &[type="search"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  &[type="number"] {
    -moz-appearance: textfield;
    
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }
`;

const StyledTextArea = styled.textarea`
  width: 100%;
  padding: 12px 16px;
  border: 2px solid ${props => {
    if (props.error) return '#F44336';
    if (props.success) return '#4CAF50';
    return props.theme.colors.border;
  }};
  border-radius: 8px;
  font-size: 16px;
  font-family: inherit;
  background-color: ${props => props.disabled ? props.theme.colors.lightGray : 'white'};
  color: ${props => props.theme.colors.text};
  transition: all 0.3s ease;
  resize: vertical;
  min-height: ${props => props.rows ? 'auto' : '100px'};

  &:focus {
    outline: none;
    border-color: ${props => props.error ? '#F44336' : props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.error ? 'rgba(244, 67, 54, 0.1)' : `${props.theme.colors.primary}20`};
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
    opacity: 0.7;
  }
`;

const IconContainer = styled.div`
  position: absolute;
  ${props => props.position === 'left' ? 'left: 12px' : 'right: 12px'};
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.textSecondary};
  font-size: 20px;
  pointer-events: ${props => props.clickable ? 'auto' : 'none'};
  cursor: ${props => props.clickable ? 'pointer' : 'default'};
  transition: color 0.3s ease;

  &:hover {
    color: ${props => props.clickable ? props.theme.colors.primary : props.theme.colors.textSecondary};
  }
`;

const HelperText = styled.div`
  font-size: 12px;
  color: ${props => {
    if (props.error) return '#F44336';
    if (props.success) return '#4CAF50';
    return props.theme.colors.textSecondary;
  }};
  margin-top: 4px;
  line-height: 1.4;
`;

const CharacterCount = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
  text-align: right;
  margin-top: 4px;
`;

const Input = forwardRef(({
  label,
  type = 'text',
  placeholder,
  value,
  onChange,
  onBlur,
  onFocus,
  error,
  success,
  helperText,
  disabled = false,
  required = false,
  size = 'medium',
  rounded = false,
  leftIcon,
  rightIcon,
  onLeftIconClick,
  onRightIconClick,
  multiline = false,
  rows,
  maxLength,
  showCharacterCount = false,
  className,
  style,
  ...props
}, ref) => {
  const { theme } = useTheme();
  const [showPassword, setShowPassword] = useState(false);

  const handlePasswordToggle = () => {
    setShowPassword(!showPassword);
  };

  const inputType = type === 'password' && showPassword ? 'text' : type;
  const hasLeftIcon = !!leftIcon;
  const hasRightIcon = !!rightIcon || type === 'password';

  const InputComponent = multiline ? StyledTextArea : StyledInput;

  const inputProps = {
    ref,
    type: multiline ? undefined : inputType,
    placeholder,
    value,
    onChange,
    onBlur,
    onFocus,
    disabled,
    required,
    error,
    success,
    size: multiline ? undefined : size,
    rounded: multiline ? false : rounded,
    hasLeftIcon,
    hasRightIcon,
    theme,
    maxLength,
    rows: multiline ? rows : undefined,
    className,
    style,
    ...props
  };

  return (
    <InputContainer>
      {label && (
        <Label required={required} theme={theme}>
          {label}
        </Label>
      )}
      
      <InputWrapper>
        {leftIcon && (
          <IconContainer
            position="left"
            clickable={!!onLeftIconClick}
            onClick={onLeftIconClick}
            theme={theme}
          >
            {leftIcon}
          </IconContainer>
        )}
        
        <InputComponent {...inputProps} />
        
        {type === 'password' && (
          <IconContainer
            position="right"
            clickable
            onClick={handlePasswordToggle}
            theme={theme}
          >
            {showPassword ? '👁️' : '👁️‍🗨️'}
          </IconContainer>
        )}
        
        {rightIcon && type !== 'password' && (
          <IconContainer
            position="right"
            clickable={!!onRightIconClick}
            onClick={onRightIconClick}
            theme={theme}
          >
            {rightIcon}
          </IconContainer>
        )}
      </InputWrapper>
      
      {(error || success || helperText) && (
        <HelperText error={error} success={success} theme={theme}>
          {error || helperText}
        </HelperText>
      )}
      
      {showCharacterCount && maxLength && (
        <CharacterCount theme={theme}>
          {value ? value.length : 0} / {maxLength}
        </CharacterCount>
      )}
    </InputContainer>
  );
});

Input.displayName = 'Input';

// Pre-built input variants
export const SearchInput = (props) => (
  <Input
    type="search"
    leftIcon="🔍"
    placeholder="Search..."
    rounded
    {...props}
  />
);

export const PasswordInput = (props) => (
  <Input
    type="password"
    {...props}
  />
);

export const EmailInput = (props) => (
  <Input
    type="email"
    leftIcon="📧"
    placeholder="Enter your email"
    {...props}
  />
);

export const PhoneInput = (props) => (
  <Input
    type="tel"
    leftIcon="📞"
    placeholder="Enter your phone number"
    {...props}
  />
);

export const NumberInput = (props) => (
  <Input
    type="number"
    {...props}
  />
);

export const TextArea = (props) => (
  <Input
    multiline
    {...props}
  />
);

export default Input;
