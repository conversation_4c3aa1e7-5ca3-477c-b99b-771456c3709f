import React from 'react';
import styled from 'styled-components';
import { useTheme } from '../../contexts/ThemeContext';

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: ${props => props.theme.colors.background};
`;

const Spinner = styled.div`
  width: 50px;
  height: 50px;
  border: 4px solid ${props => props.theme.colors.border};
  border-top: 4px solid ${props => props.theme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const LoadingText = styled.p`
  font-size: 18px;
  color: ${props => props.theme.colors.textSecondary};
  font-weight: 500;
  margin: 0;
`;

const SubText = styled.p`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 8px 0 0 0;
  opacity: 0.7;
`;

const LoadingSpinner = ({ 
  text = 'Loading NeuroCare...', 
  subText = 'Please wait while we prepare your healthcare dashboard',
  size = 'large' 
}) => {
  const { theme } = useTheme();

  const spinnerSize = size === 'small' ? '30px' : size === 'medium' ? '40px' : '50px';
  const borderWidth = size === 'small' ? '3px' : '4px';

  return (
    <LoadingContainer theme={theme}>
      <Spinner 
        theme={theme}
        style={{
          width: spinnerSize,
          height: spinnerSize,
          borderWidth: borderWidth,
          borderTopWidth: borderWidth
        }}
      />
      <LoadingText theme={theme}>{text}</LoadingText>
      {subText && <SubText theme={theme}>{subText}</SubText>}
    </LoadingContainer>
  );
};

export default LoadingSpinner;
