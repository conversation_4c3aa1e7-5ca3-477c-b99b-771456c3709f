import React, { useEffect } from 'react';
import { createPortal } from 'react-dom';
import styled from 'styled-components';
import { useTheme } from '../../contexts/ThemeContext';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: ${props => props.theme.zIndex?.modal || 1050};
  padding: 20px;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
`;

const ModalContainer = styled.div`
  background: white;
  border-radius: 16px;
  box-shadow: ${props => props.theme.shadows?.xl || '0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)'};
  max-width: ${props => props.maxWidth || '500px'};
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: slideIn 0.3s ease-out;

  @keyframes slideIn {
    from {
      transform: translateY(-50px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @media (max-width: 768px) {
    max-width: 95vw;
    margin: 10px;
  }
`;

const ModalHeader = styled.div`
  padding: 24px 24px 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: ${props => props.showBorder ? `1px solid ${props.theme.colors?.borderLight || '#F0F0F0'}` : 'none'};
  padding-bottom: ${props => props.showBorder ? '16px' : '0'};
  margin-bottom: ${props => props.showBorder ? '24px' : '0'};
`;

const ModalTitle = styled.h2`
  font-size: 24px;
  font-weight: 700;
  color: ${props => props.theme.colors?.text || '#212121'};
  margin: 0;
  flex: 1;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: ${props => props.theme.colors?.textSecondary || '#757575'};
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;

  &:hover {
    background-color: ${props => props.theme.colors?.lightGray || '#F5F5F5'};
    color: ${props => props.theme.colors?.text || '#212121'};
  }

  &:focus {
    outline: 2px solid ${props => props.theme.colors?.primary || '#4CAF50'};
    outline-offset: 2px;
  }
`;

const ModalBody = styled.div`
  padding: ${props => props.noPadding ? '0' : '0 24px'};
`;

const ModalFooter = styled.div`
  padding: 24px;
  display: flex;
  gap: 12px;
  justify-content: ${props => props.align || 'flex-end'};
  border-top: ${props => props.showBorder ? `1px solid ${props.theme.colors?.borderLight || '#F0F0F0'}` : 'none'};
  margin-top: ${props => props.showBorder ? '24px' : '0'};

  @media (max-width: 480px) {
    flex-direction: column;
    
    button {
      width: 100%;
    }
  }
`;

const Button = styled.button`
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  min-width: 100px;

  &.primary {
    background: ${props => props.theme.colors?.primary || '#4CAF50'};
    color: white;

    &:hover:not(:disabled) {
      background: ${props => props.theme.colors?.primaryDark || '#388E3C'};
      transform: translateY(-2px);
    }
  }

  &.secondary {
    background: transparent;
    color: ${props => props.theme.colors?.textSecondary || '#757575'};
    border: 2px solid ${props => props.theme.colors?.border || '#E0E0E0'};

    &:hover:not(:disabled) {
      background: ${props => props.theme.colors?.lightGray || '#F5F5F5'};
    }
  }

  &.danger {
    background: #F44336;
    color: white;

    &:hover:not(:disabled) {
      background: #D32F2F;
      transform: translateY(-2px);
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }

  &:focus {
    outline: 2px solid ${props => props.theme.colors?.primary || '#4CAF50'};
    outline-offset: 2px;
  }
`;

const Modal = ({
  isOpen,
  onClose,
  title,
  children,
  footer,
  maxWidth,
  closeOnOverlayClick = true,
  closeOnEscape = true,
  showCloseButton = true,
  headerBorder = true,
  footerBorder = true,
  noPadding = false,
  footerAlign = 'flex-end',
  className,
  ...props
}) => {
  const { theme } = useTheme();

  // Handle escape key
  useEffect(() => {
    if (!isOpen || !closeOnEscape) return;

    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape, onClose]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const handleOverlayClick = (event) => {
    if (closeOnOverlayClick && event.target === event.currentTarget) {
      onClose();
    }
  };

  const modalContent = (
    <ModalOverlay onClick={handleOverlayClick} theme={theme}>
      <ModalContainer 
        maxWidth={maxWidth} 
        className={className}
        theme={theme}
        {...props}
      >
        {(title || showCloseButton) && (
          <ModalHeader showBorder={headerBorder} theme={theme}>
            {title && <ModalTitle theme={theme}>{title}</ModalTitle>}
            {showCloseButton && (
              <CloseButton onClick={onClose} theme={theme} aria-label="Close modal">
                ×
              </CloseButton>
            )}
          </ModalHeader>
        )}

        <ModalBody noPadding={noPadding}>
          {children}
        </ModalBody>

        {footer && (
          <ModalFooter 
            showBorder={footerBorder} 
            align={footerAlign}
            theme={theme}
          >
            {footer}
          </ModalFooter>
        )}
      </ModalContainer>
    </ModalOverlay>
  );

  // Render modal in a portal
  return createPortal(modalContent, document.body);
};

// Pre-built modal variants
export const ConfirmModal = ({
  isOpen,
  onClose,
  onConfirm,
  title = 'Confirm Action',
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  confirmVariant = 'primary',
  loading = false,
  ...props
}) => {
  const { theme } = useTheme();

  const footer = (
    <>
      <Button 
        className="secondary" 
        onClick={onClose}
        disabled={loading}
        theme={theme}
      >
        {cancelText}
      </Button>
      <Button 
        className={confirmVariant} 
        onClick={onConfirm}
        disabled={loading}
        theme={theme}
      >
        {loading ? 'Loading...' : confirmText}
      </Button>
    </>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      footer={footer}
      maxWidth="400px"
      {...props}
    >
      <div style={{ padding: '24px 0' }}>
        <p style={{ 
          margin: 0, 
          fontSize: '16px', 
          lineHeight: '1.5',
          color: theme.colors?.text || '#212121'
        }}>
          {message}
        </p>
      </div>
    </Modal>
  );
};

export const AlertModal = ({
  isOpen,
  onClose,
  title = 'Alert',
  message,
  buttonText = 'OK',
  variant = 'primary',
  ...props
}) => {
  const { theme } = useTheme();

  const footer = (
    <Button 
      className={variant} 
      onClick={onClose}
      theme={theme}
    >
      {buttonText}
    </Button>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      footer={footer}
      maxWidth="400px"
      {...props}
    >
      <div style={{ padding: '24px 0' }}>
        <p style={{ 
          margin: 0, 
          fontSize: '16px', 
          lineHeight: '1.5',
          color: theme.colors?.text || '#212121'
        }}>
          {message}
        </p>
      </div>
    </Modal>
  );
};

export default Modal;
