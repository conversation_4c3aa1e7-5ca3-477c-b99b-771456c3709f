import React from 'react';
import { ThemeProvider as StyledThemeProvider } from 'styled-components';
import { useTheme } from '../../contexts/ThemeContext';
import { DEFAULT_THEME, COLORS } from '../../config/theme';

/**
 * Safe Theme Provider that ensures all styled-components have access to a complete theme
 */
const SafeThemeProvider = ({ children }) => {
  const { theme } = useTheme();

  // Create a safe theme that always has all required properties
  const safeTheme = React.useMemo(() => {
    // If theme is undefined or incomplete, use default
    if (!theme || !theme.colors) {
      console.warn('Theme is undefined or incomplete, using default theme');
      return DEFAULT_THEME;
    }

    // Ensure all color properties exist
    const requiredColors = [
      'primary', 'primaryDark', 'primaryLight', 'primaryLighter',
      'secondary', 'background', 'surface', 'text', 'textSecondary', 'textLight',
      'lightGray', 'gray', 'darkGray', 'border', 'borderLight',
      'white', 'black', 'success', 'warning', 'error', 'info'
    ];

    const safeColors = { ...COLORS };
    
    // Override with theme colors if they exist
    requiredColors.forEach(colorKey => {
      if (theme.colors[colorKey]) {
        safeColors[colorKey] = theme.colors[colorKey];
      }
    });

    // Add any additional colors from the theme
    Object.keys(theme.colors).forEach(colorKey => {
      safeColors[colorKey] = theme.colors[colorKey];
    });

    return {
      ...DEFAULT_THEME,
      ...theme,
      colors: safeColors
    };
  }, [theme]);

  return (
    <StyledThemeProvider theme={safeTheme}>
      {children}
    </StyledThemeProvider>
  );
};

export default SafeThemeProvider;
