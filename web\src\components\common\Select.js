import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../contexts/ThemeContext';

const SelectContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  position: relative;
`;

const Label = styled.label`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin-bottom: 4px;
  
  ${props => props.required && `
    &::after {
      content: ' *';
      color: #F44336;
    }
  `}
`;

const SelectWrapper = styled.div`
  position: relative;
`;

const SelectButton = styled.button`
  width: 100%;
  padding: ${props => {
    switch (props.size) {
      case 'small': return '8px 12px';
      case 'large': return '16px 20px';
      default: return '12px 16px';
    }
  }};
  padding-right: 40px;
  border: 2px solid ${props => {
    if (props.error) return '#F44336';
    if (props.success) return '#4CAF50';
    return props.theme.colors.border;
  }};
  border-radius: ${props => props.rounded ? '50px' : '8px'};
  font-size: ${props => {
    switch (props.size) {
      case 'small': return '14px';
      case 'large': return '18px';
      default: return '16px';
    }
  }};
  font-family: inherit;
  background-color: ${props => props.disabled ? props.theme.colors.lightGray : 'white'};
  color: ${props => props.hasValue ? props.theme.colors.text : props.theme.colors.textSecondary};
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.3s ease;
  text-align: left;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:focus {
    outline: none;
    border-color: ${props => props.error ? '#F44336' : props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.error ? 'rgba(244, 67, 54, 0.1)' : `${props.theme.colors.primary}20`};
  }

  &:disabled {
    opacity: 0.6;
  }

  &:hover:not(:disabled) {
    border-color: ${props => props.error ? '#F44336' : props.theme.colors.primary};
  }
`;

const SelectIcon = styled.div`
  color: ${props => props.theme.colors.textSecondary};
  font-size: 16px;
  transition: transform 0.3s ease;
  transform: ${props => props.isOpen ? 'rotate(180deg)' : 'rotate(0deg)'};
  pointer-events: none;
`;

const DropdownContainer = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border: 2px solid ${props => props.theme.colors.border};
  border-radius: 8px;
  box-shadow: ${props => props.theme.shadows.lg};
  max-height: 200px;
  overflow-y: auto;
  margin-top: 4px;
  animation: slideDown 0.2s ease-out;

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

const Option = styled.div`
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: ${props => props.theme.colors.text};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: ${props => props.theme.colors.lightGray};
  }

  ${props => props.selected && `
    background-color: ${props.theme.colors.primary};
    color: white;
    
    &:hover {
      background-color: ${props.theme.colors.primaryDark};
    }
  `}

  ${props => props.disabled && `
    opacity: 0.5;
    cursor: not-allowed;
    
    &:hover {
      background-color: transparent;
    }
  `}
`;

const HelperText = styled.div`
  font-size: 12px;
  color: ${props => {
    if (props.error) return '#F44336';
    if (props.success) return '#4CAF50';
    return props.theme.colors.textSecondary;
  }};
  margin-top: 4px;
  line-height: 1.4;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 8px 12px;
  border: none;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  font-size: 14px;
  outline: none;
  background: ${props => props.theme.colors.lightGray};

  &:focus {
    border-bottom-color: ${props => props.theme.colors.primary};
  }
`;

const NoOptions = styled.div`
  padding: 12px 16px;
  color: ${props => props.theme.colors.textSecondary};
  font-style: italic;
  text-align: center;
`;

const Select = ({
  label,
  options = [],
  value,
  onChange,
  placeholder = 'Select an option...',
  error,
  success,
  helperText,
  disabled = false,
  required = false,
  size = 'medium',
  rounded = false,
  searchable = false,
  clearable = false,
  multiple = false,
  className,
  style,
  ...props
}) => {
  const { theme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const selectRef = useRef(null);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (selectRef.current && !selectRef.current.contains(event.target)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Filter options based on search term
  const filteredOptions = searchable && searchTerm
    ? options.filter(option => 
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;

  const selectedOption = options.find(option => option.value === value);
  const hasValue = value !== undefined && value !== null && value !== '';

  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      if (!isOpen) {
        setSearchTerm('');
      }
    }
  };

  const handleOptionClick = (option) => {
    if (option.disabled) return;
    
    if (multiple) {
      // Handle multiple selection
      const currentValues = Array.isArray(value) ? value : [];
      const newValues = currentValues.includes(option.value)
        ? currentValues.filter(v => v !== option.value)
        : [...currentValues, option.value];
      onChange(newValues);
    } else {
      onChange(option.value);
      setIsOpen(false);
      setSearchTerm('');
    }
  };

  const handleClear = (e) => {
    e.stopPropagation();
    onChange(multiple ? [] : '');
  };

  const getDisplayValue = () => {
    if (multiple && Array.isArray(value)) {
      if (value.length === 0) return placeholder;
      if (value.length === 1) {
        const option = options.find(opt => opt.value === value[0]);
        return option ? option.label : placeholder;
      }
      return `${value.length} items selected`;
    }
    
    return selectedOption ? selectedOption.label : placeholder;
  };

  return (
    <SelectContainer ref={selectRef} className={className} style={style}>
      {label && (
        <Label required={required} theme={theme}>
          {label}
        </Label>
      )}
      
      <SelectWrapper>
        <SelectButton
          type="button"
          onClick={handleToggle}
          disabled={disabled}
          error={error}
          success={success}
          size={size}
          rounded={rounded}
          hasValue={hasValue}
          theme={theme}
          {...props}
        >
          <span>{getDisplayValue()}</span>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {clearable && hasValue && !disabled && (
              <span
                onClick={handleClear}
                style={{
                  cursor: 'pointer',
                  color: theme.colors.textSecondary,
                  fontSize: '16px',
                  padding: '2px'
                }}
              >
                ×
              </span>
            )}
            <SelectIcon isOpen={isOpen} theme={theme}>
              ▼
            </SelectIcon>
          </div>
        </SelectButton>
        
        {isOpen && (
          <DropdownContainer ref={dropdownRef} theme={theme}>
            {searchable && (
              <SearchInput
                type="text"
                placeholder="Search options..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                theme={theme}
                autoFocus
              />
            )}
            
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option) => (
                <Option
                  key={option.value}
                  onClick={() => handleOptionClick(option)}
                  selected={multiple 
                    ? Array.isArray(value) && value.includes(option.value)
                    : option.value === value
                  }
                  disabled={option.disabled}
                  theme={theme}
                >
                  {option.label}
                </Option>
              ))
            ) : (
              <NoOptions theme={theme}>
                {searchTerm ? 'No options found' : 'No options available'}
              </NoOptions>
            )}
          </DropdownContainer>
        )}
      </SelectWrapper>
      
      {(error || success || helperText) && (
        <HelperText error={error} success={success} theme={theme}>
          {error || helperText}
        </HelperText>
      )}
    </SelectContainer>
  );
};

export default Select;
