import React from 'react';
import { DEFAULT_THEME } from '../../config/theme';

class ThemeErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error for debugging
    console.error('Theme Error Boundary caught an error:', error, errorInfo);
    
    // You can also log the error to an error reporting service here
    if (error.message && error.message.includes('lightGray')) {
      console.error('Theme color error detected. Falling back to default theme.');
    }
  }

  render() {
    if (this.state.hasError) {
      // Fallback UI with inline styles to avoid theme dependency
      return (
        <div style={{
          padding: '20px',
          margin: '20px',
          border: '2px solid #f44336',
          borderRadius: '8px',
          backgroundColor: '#ffebee',
          color: '#c62828',
          textAlign: 'center',
          fontFamily: 'Arial, sans-serif'
        }}>
          <h2 style={{ margin: '0 0 16px 0', fontSize: '24px' }}>
            🎨 Theme Error
          </h2>
          <p style={{ margin: '0 0 16px 0', fontSize: '16px' }}>
            There was an error loading the theme. The application is using a fallback theme.
          </p>
          <p style={{ margin: '0 0 16px 0', fontSize: '14px', opacity: 0.8 }}>
            Error: {this.state.error?.message || 'Unknown theme error'}
          </p>
          <button
            onClick={() => {
              this.setState({ hasError: false, error: null });
              window.location.reload();
            }}
            style={{
              padding: '8px 16px',
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            Reload Application
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Higher-order component to provide theme fallback
export const withThemeFallback = (Component) => {
  return React.forwardRef((props, ref) => {
    const [themeError, setThemeError] = React.useState(false);

    // Provide a safe theme object as fallback
    const safeTheme = React.useMemo(() => {
      try {
        return props.theme || DEFAULT_THEME;
      } catch (error) {
        console.error('Error accessing theme:', error);
        setThemeError(true);
        return DEFAULT_THEME;
      }
    }, [props.theme]);

    if (themeError) {
      return (
        <div style={{
          padding: '16px',
          backgroundColor: '#fff3cd',
          border: '1px solid #ffeaa7',
          borderRadius: '4px',
          color: '#856404',
          margin: '8px'
        }}>
          <strong>Theme Warning:</strong> Using fallback theme due to theme loading error.
        </div>
      );
    }

    return <Component {...props} theme={safeTheme} ref={ref} />;
  });
};

// Hook to safely access theme
export const useSafeTheme = (theme) => {
  return React.useMemo(() => {
    if (!theme || !theme.colors) {
      console.warn('Theme is undefined or missing colors, using default theme');
      return DEFAULT_THEME;
    }

    // Ensure all required color properties exist
    const requiredColors = [
      'primary', 'secondary', 'background', 'surface', 'text', 'textSecondary',
      'lightGray', 'gray', 'darkGray', 'border', 'borderLight', 'white', 'black',
      'success', 'warning', 'error', 'info'
    ];

    const missingColors = requiredColors.filter(color => !theme.colors[color]);
    
    if (missingColors.length > 0) {
      console.warn('Missing theme colors:', missingColors, 'Using default values');
      return {
        ...theme,
        colors: {
          ...DEFAULT_THEME.colors,
          ...theme.colors
        }
      };
    }

    return theme;
  }, [theme]);
};

export default ThemeErrorBoundary;
