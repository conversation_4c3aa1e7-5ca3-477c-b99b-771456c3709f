import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import { useAppointments } from '../../contexts/AppointmentContext';
import { useTheme } from '../../contexts/ThemeContext';
import { toast } from 'react-toastify';

// Import role-specific dashboards
import PatientDashboard from './patient/PatientDashboard';
import DoctorDashboard from './doctor/DoctorDashboard';
import AdminDashboard from './admin/AdminDashboard';
import CaregiverDashboard from './caregiver/CaregiverDashboard';
import SupervisorDashboard from './supervisor/SupervisorDashboard';

// Import common components
import LoadingSpinner from '../common/LoadingSpinner';
import ProfileNotification from '../notifications/ProfileNotification';

const DashboardContainer = styled.div`
  min-height: 100vh;
  background-color: ${props => props.theme.colors.background};
`;

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { theme } = useTheme();

  useEffect(() => {
    // Simulate loading time for dashboard initialization
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (user && !user.profileComplete) {
      toast.info('Please complete your profile to access all features', {
        autoClose: 5000,
      });
    }
  }, [user]);

  if (loading) {
    return (
      <LoadingSpinner 
        text="Loading Dashboard..." 
        subText="Preparing your personalized healthcare interface"
      />
    );
  }

  if (!user) {
    return (
      <LoadingSpinner 
        text="Authentication Required" 
        subText="Please sign in to access your dashboard"
      />
    );
  }

  const renderDashboard = () => {
    switch (user.role) {
      case 'patient':
        return <PatientDashboard />;
      case 'doctor':
        return <DoctorDashboard />;
      case 'admin':
        return <AdminDashboard />;
      case 'caregiver':
        return <CaregiverDashboard />;
      case 'supervisor':
        return <SupervisorDashboard />;
      default:
        return <PatientDashboard />; // Default to patient dashboard
    }
  };

  return (
    <DashboardContainer theme={theme}>
      {/* Profile completion notification */}
      {user && !user.profileComplete && (
        <ProfileNotification />
      )}
      
      {/* Render role-specific dashboard */}
      {renderDashboard()}
    </DashboardContainer>
  );
};

export default Dashboard;
