import React from 'react';
import styled from 'styled-components';
import { useTheme } from '../../contexts/ThemeContext';

const Card = styled.div`
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: ${props => props.theme.shadows.md};
  transition: all 0.3s ease;
  cursor: ${props => props.onClick ? 'pointer' : 'default'};
  border: 1px solid ${props => props.theme.colors.borderLight};
  position: relative;
  overflow: hidden;

  &:hover {
    ${props => props.onClick && `
      transform: translateY(-4px);
      box-shadow: ${props.theme.shadows.lg};
    `}
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: ${props => props.accentColor || props.theme.colors.primary};
  }
`;

const CardHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${props => props.hasContent ? '16px' : '0'};
`;

const CardTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const CardIcon = styled.span`
  font-size: 20px;
  color: ${props => props.accentColor || props.theme.colors.primary};
`;

const CardAction = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.textSecondary};
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    color: ${props => props.theme.colors.primary};
    background-color: ${props => props.theme.colors.lightGray};
  }
`;

const CardContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const StatValue = styled.div`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.color || props.theme.colors.text};
  line-height: 1;
`;

const StatLabel = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  font-weight: 500;
`;

const StatChange = styled.div`
  font-size: 12px;
  font-weight: 600;
  color: ${props => {
    if (props.type === 'positive') return '#4CAF50';
    if (props.type === 'negative') return '#F44336';
    return props.theme.colors.textSecondary;
  }};
  display: flex;
  align-items: center;
  gap: 4px;
`;

const MetricsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const MetricItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};

  &:last-child {
    border-bottom: none;
  }
`;

const MetricLabel = styled.span`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
`;

const MetricValue = styled.span`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.color || props.theme.colors.text};
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background-color: ${props => props.theme.colors.lightGray};
  border-radius: 4px;
  overflow: hidden;
  margin-top: 8px;
`;

const ProgressFill = styled.div`
  height: 100%;
  background: linear-gradient(90deg, ${props => props.color || props.theme.colors.primary} 0%, ${props => props.endColor || props.color || props.theme.colors.primaryLight} 100%);
  width: ${props => props.percentage || 0}%;
  transition: width 0.3s ease;
`;

const DashboardCard = ({
  title,
  icon,
  value,
  label,
  change,
  changeType,
  metrics = [],
  progress,
  accentColor,
  onClick,
  onAction,
  actionIcon = '⋯',
  children,
  ...props
}) => {
  const { theme } = useTheme();

  const renderChange = () => {
    if (!change) return null;
    
    const changeIcon = changeType === 'positive' ? '↗️' : changeType === 'negative' ? '↘️' : '';
    
    return (
      <StatChange type={changeType} theme={theme}>
        {changeIcon} {change}
      </StatChange>
    );
  };

  const renderProgress = () => {
    if (!progress) return null;
    
    return (
      <div>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '4px' }}>
          <StatLabel theme={theme}>{progress.label}</StatLabel>
          <span style={{ fontSize: '12px', fontWeight: '600', color: theme.colors.text }}>
            {progress.percentage}%
          </span>
        </div>
        <ProgressBar theme={theme}>
          <ProgressFill 
            percentage={progress.percentage} 
            color={progress.color || accentColor}
            endColor={progress.endColor}
            theme={theme}
          />
        </ProgressBar>
      </div>
    );
  };

  const hasContent = value || metrics.length > 0 || progress || children;

  return (
    <Card 
      onClick={onClick} 
      accentColor={accentColor} 
      theme={theme} 
      {...props}
    >
      <CardHeader hasContent={hasContent}>
        <CardTitle theme={theme}>
          {icon && <CardIcon accentColor={accentColor} theme={theme}>{icon}</CardIcon>}
          {title}
        </CardTitle>
        {onAction && (
          <CardAction onClick={(e) => { e.stopPropagation(); onAction(); }} theme={theme}>
            {actionIcon}
          </CardAction>
        )}
      </CardHeader>

      {hasContent && (
        <CardContent>
          {value && (
            <div>
              <StatValue color={accentColor} theme={theme}>{value}</StatValue>
              {label && <StatLabel theme={theme}>{label}</StatLabel>}
              {renderChange()}
            </div>
          )}

          {metrics.length > 0 && (
            <MetricsList>
              {metrics.map((metric, index) => (
                <MetricItem key={index} theme={theme}>
                  <MetricLabel theme={theme}>{metric.label}</MetricLabel>
                  <MetricValue color={metric.color} theme={theme}>
                    {metric.value}
                  </MetricValue>
                </MetricItem>
              ))}
            </MetricsList>
          )}

          {renderProgress()}

          {children}
        </CardContent>
      )}
    </Card>
  );
};

export default DashboardCard;
