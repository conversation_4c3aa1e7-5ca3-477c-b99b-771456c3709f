import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { toast } from 'react-toastify';

const LayoutContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background-color: ${props => props.theme.colors.background};
`;

const Sidebar = styled.div`
  width: ${props => props.isOpen ? '280px' : '80px'};
  background: linear-gradient(180deg, ${props => props.headerBackgroundColor || props.theme.colors.primary} 0%, ${props => props.accentColor || props.theme.colors.primaryDark} 100%);
  transition: width 0.3s ease;
  position: fixed;
  height: 100vh;
  z-index: 1000;
  overflow-y: auto;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);

  @media (max-width: 768px) {
    width: ${props => props.isOpen ? '100%' : '0'};
    transform: translateX(${props => props.isOpen ? '0' : '-100%'});
  }
`;

const SidebarHeader = styled.div`
  padding: 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
`;

const Logo = styled.div`
  width: 40px;
  height: 40px;
  background-color: white;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  flex-shrink: 0;
`;

const SidebarTitle = styled.h2`
  color: white;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  opacity: ${props => props.isOpen ? 1 : 0};
  transition: opacity 0.3s ease;
`;

const MenuItems = styled.div`
  padding: 16px 0;
`;

const MenuItem = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 24px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-left-color: white;
  }

  &.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-left-color: white;
  }
`;

const MenuIcon = styled.span`
  font-size: 20px;
  flex-shrink: 0;
`;

const MenuLabel = styled.span`
  font-size: 16px;
  font-weight: 500;
  opacity: ${props => props.isOpen ? 1 : 0};
  transition: opacity 0.3s ease;
`;

const MainContent = styled.div`
  flex: 1;
  margin-left: ${props => props.sidebarOpen ? '280px' : '80px'};
  transition: margin-left 0.3s ease;

  @media (max-width: 768px) {
    margin-left: 0;
  }
`;

const Header = styled.header`
  background: white;
  padding: 16px 24px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 999;
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const MenuToggle = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: ${props => props.theme.colors.text};
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: ${props => props.theme.colors.lightGray};
  }
`;

const HeaderTitle = styled.h1`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const HeaderRight = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const UserProfile = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 12px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: ${props => props.theme.colors.lightGray};
  }
`;

const UserAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary} 0%, ${props => props.theme.colors.primaryLight} 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  @media (max-width: 480px) {
    display: none;
  }
`;

const UserName = styled.span`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const UserRole = styled.span`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
  text-transform: capitalize;
`;

const ContentArea = styled.main`
  padding: 24px;
  min-height: calc(100vh - 80px);
`;

const LogoutButton = styled.button`
  background: none;
  border: 1px solid ${props => props.theme.colors.border};
  color: ${props => props.theme.colors.textSecondary};
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${props => props.theme.colors.error};
    color: white;
    border-color: ${props => props.theme.colors.error};
  }
`;

const DashboardLayout = ({ 
  title, 
  roleName, 
  menuItems = [], 
  headerBackgroundColor, 
  accentColor, 
  children 
}) => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [activeMenuItem, setActiveMenuItem] = useState('Dashboard');
  
  const { user, logout } = useAuth();
  const { theme } = useTheme();
  const navigate = useNavigate();

  const handleMenuItemClick = (item) => {
    setActiveMenuItem(item.label);
    if (item.screen) {
      navigate(`/${item.screen.toLowerCase()}`);
    }
    if (item.action) {
      item.action();
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      toast.success('Logged out successfully');
      navigate('/');
    } catch (error) {
      toast.error('Failed to logout');
    }
  };

  const getUserInitials = () => {
    if (user?.displayName) {
      return user.displayName
        .split(' ')
        .map(name => name[0])
        .join('')
        .toUpperCase()
        .slice(0, 2);
    }
    return user?.email?.[0]?.toUpperCase() || 'U';
  };

  return (
    <LayoutContainer theme={theme}>
      <Sidebar 
        isOpen={sidebarOpen} 
        headerBackgroundColor={headerBackgroundColor}
        accentColor={accentColor}
        theme={theme}
      >
        <SidebarHeader>
          <Logo>🧠</Logo>
          <SidebarTitle isOpen={sidebarOpen}>NeuroCare</SidebarTitle>
        </SidebarHeader>

        <MenuItems>
          {menuItems.map((item, index) => (
            <MenuItem
              key={index}
              className={activeMenuItem === item.label ? 'active' : ''}
              onClick={() => handleMenuItemClick(item)}
            >
              <MenuIcon>{item.icon}</MenuIcon>
              <MenuLabel isOpen={sidebarOpen}>{item.label}</MenuLabel>
            </MenuItem>
          ))}
        </MenuItems>
      </Sidebar>

      <MainContent sidebarOpen={sidebarOpen}>
        <Header theme={theme}>
          <HeaderLeft>
            <MenuToggle 
              onClick={() => setSidebarOpen(!sidebarOpen)}
              theme={theme}
            >
              ☰
            </MenuToggle>
            <HeaderTitle theme={theme}>{title}</HeaderTitle>
          </HeaderLeft>

          <HeaderRight>
            <LogoutButton onClick={handleLogout} theme={theme}>
              Logout
            </LogoutButton>
            <UserProfile onClick={() => navigate('/profile')}>
              <UserAvatar theme={theme}>
                {getUserInitials()}
              </UserAvatar>
              <UserInfo>
                <UserName theme={theme}>
                  {user?.displayName || 'User'}
                </UserName>
                <UserRole theme={theme}>
                  {roleName || user?.role || 'User'}
                </UserRole>
              </UserInfo>
            </UserProfile>
          </HeaderRight>
        </Header>

        <ContentArea>
          {children}
        </ContentArea>
      </MainContent>
    </LayoutContainer>
  );
};

export default DashboardLayout;
