import React from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../../../contexts/AuthContext';
import { useTheme } from '../../../contexts/ThemeContext';
import { useAppointments } from '../../../contexts/AppointmentContext';
import DashboardLayout from '../DashboardLayout';
import DashboardCard from '../DashboardCard';

const DashboardGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
`;

const QuickActionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
`;

const ActionButton = styled.button`
  background: white;
  border: 2px solid ${props => props.theme.colors.admin};
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-align: center;

  &:hover {
    background-color: ${props => props.theme.colors.admin};
    color: white;
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.md};
  }
`;

const ActionIcon = styled.div`
  font-size: 32px;
`;

const ActionLabel = styled.span`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const SectionTitle = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 20px 0;
`;

const AdminDashboard = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const { appointments } = useAppointments();
  const navigate = useNavigate();

  const adminColors = getRoleColors('admin');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', screen: 'dashboard' },
    { label: 'User Management', icon: '👥', screen: 'users' },
    { label: 'System Analytics', icon: '📊', screen: 'analytics' },
    { label: 'Reports', icon: '📋', screen: 'reports' },
    { label: 'Settings', icon: '⚙️', screen: 'settings' },
    { label: 'Security', icon: '🔒', screen: 'security' },
    { label: 'Backup', icon: '💾', screen: 'backup' },
    { label: 'Logs', icon: '📝', screen: 'logs' },
    { label: 'Profile', icon: '👤', screen: 'profile' }
  ];

  const quickActions = [
    { 
      icon: '👥', 
      label: 'Manage Users', 
      action: () => navigate('/admin/users') 
    },
    { 
      icon: '📊', 
      label: 'View Analytics', 
      action: () => navigate('/admin/analytics') 
    },
    { 
      icon: '📋', 
      label: 'Generate Report', 
      action: () => navigate('/admin/reports') 
    },
    { 
      icon: '🔒', 
      label: 'Security Settings', 
      action: () => navigate('/admin/security') 
    }
  ];

  // Mock system statistics
  const systemStats = {
    totalUsers: 1247,
    activeUsers: 892,
    totalAppointments: appointments.length,
    systemUptime: '99.9%'
  };

  return (
    <DashboardLayout
      title="Admin Dashboard"
      roleName="Administrator"
      menuItems={menuItems}
      headerBackgroundColor={adminColors.primary}
      accentColor={adminColors.secondary}
    >
      {/* Welcome Section */}
      <div style={{ marginBottom: '32px' }}>
        <h1 style={{ 
          fontSize: '32px', 
          fontWeight: '700', 
          color: theme.colors.text, 
          margin: '0 0 8px 0' 
        }}>
          System Overview 🛡️
        </h1>
        <p style={{ 
          fontSize: '16px', 
          color: theme.colors.textSecondary, 
          margin: '0' 
        }}>
          Monitor and manage the NeuroCare platform
        </p>
      </div>

      {/* System Overview Cards */}
      <DashboardGrid>
        <DashboardCard
          title="Total Users"
          icon="👥"
          value={systemStats.totalUsers.toLocaleString()}
          label="registered users"
          change="+12 this week"
          changeType="positive"
          accentColor={adminColors.primary}
          onClick={() => navigate('/admin/users')}
        />

        <DashboardCard
          title="Active Users"
          icon="🟢"
          value={systemStats.activeUsers.toLocaleString()}
          label="online now"
          accentColor={adminColors.primary}
          progress={{
            label: "Activity Rate",
            percentage: Math.round((systemStats.activeUsers / systemStats.totalUsers) * 100),
            color: adminColors.primary
          }}
        />

        <DashboardCard
          title="Total Appointments"
          icon="📅"
          value={systemStats.totalAppointments.toLocaleString()}
          label="all time"
          accentColor={adminColors.primary}
          onClick={() => navigate('/admin/appointments')}
        />

        <DashboardCard
          title="System Uptime"
          icon="⚡"
          value={systemStats.systemUptime}
          label="this month"
          accentColor={adminColors.primary}
          progress={{
            label: "Reliability",
            percentage: 99.9,
            color: '#4CAF50'
          }}
        />
      </DashboardGrid>

      {/* Quick Actions */}
      <SectionTitle theme={theme}>Administrative Actions</SectionTitle>
      <QuickActionsGrid>
        {quickActions.map((action, index) => (
          <ActionButton 
            key={index} 
            onClick={action.action}
            theme={theme}
          >
            <ActionIcon>{action.icon}</ActionIcon>
            <ActionLabel theme={theme}>{action.label}</ActionLabel>
          </ActionButton>
        ))}
      </QuickActionsGrid>

      {/* System Status */}
      <DashboardGrid>
        <DashboardCard
          title="User Distribution"
          icon="📊"
          accentColor={adminColors.primary}
          onClick={() => navigate('/admin/analytics')}
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {[
              { role: 'Patients', count: 856, color: '#4CAF50' },
              { role: 'Doctors', count: 124, color: '#2196F3' },
              { role: 'Caregivers', count: 89, color: '#FF9800' },
              { role: 'Supervisors', count: 45, color: '#E91E63' },
              { role: 'Admins', count: 12, color: '#9C27B0' }
            ].map((item, index) => (
              <div key={index} style={{ 
                display: 'flex', 
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '8px 0',
                borderBottom: index < 4 ? `1px solid ${theme.colors.borderLight}` : 'none'
              }}>
                <span style={{ fontSize: '14px', color: theme.colors.textSecondary }}>
                  {item.role}
                </span>
                <span style={{ 
                  fontSize: '14px', 
                  fontWeight: '600',
                  color: item.color
                }}>
                  {item.count}
                </span>
              </div>
            ))}
          </div>
        </DashboardCard>

        <DashboardCard
          title="Recent Activity"
          icon="📝"
          accentColor={adminColors.primary}
          onClick={() => navigate('/admin/logs')}
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {[
              { action: 'New user registration', time: '2 min ago', type: 'info' },
              { action: 'System backup completed', time: '1 hour ago', type: 'success' },
              { action: 'Failed login attempt', time: '3 hours ago', type: 'warning' },
              { action: 'Database maintenance', time: '6 hours ago', type: 'info' }
            ].map((activity, index) => (
              <div key={index} style={{ 
                padding: '12px', 
                backgroundColor: theme.colors.lightGray, 
                borderRadius: '8px',
                borderLeft: `4px solid ${
                  activity.type === 'success' ? '#4CAF50' :
                  activity.type === 'warning' ? '#FF9800' :
                  activity.type === 'error' ? '#F44336' : adminColors.primary
                }`
              }}>
                <div style={{ fontWeight: '600', fontSize: '14px', marginBottom: '4px' }}>
                  {activity.action}
                </div>
                <div style={{ fontSize: '12px', color: theme.colors.textSecondary }}>
                  {activity.time}
                </div>
              </div>
            ))}
          </div>
        </DashboardCard>

        <DashboardCard
          title="System Health"
          icon="💚"
          accentColor={adminColors.primary}
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            {[
              { metric: 'CPU Usage', value: '23%', status: 'good' },
              { metric: 'Memory Usage', value: '67%', status: 'good' },
              { metric: 'Disk Space', value: '45%', status: 'good' },
              { metric: 'Network', value: '12ms', status: 'excellent' }
            ].map((metric, index) => (
              <div key={index}>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  marginBottom: '4px' 
                }}>
                  <span style={{ fontSize: '14px', color: theme.colors.textSecondary }}>
                    {metric.metric}
                  </span>
                  <span style={{ fontSize: '14px', fontWeight: '600' }}>
                    {metric.value}
                  </span>
                </div>
                <div style={{ 
                  width: '100%', 
                  height: '6px', 
                  backgroundColor: theme.colors.lightGray,
                  borderRadius: '3px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    height: '100%',
                    width: metric.metric === 'Network' ? '95%' : 
                           metric.metric === 'CPU Usage' ? '23%' :
                           metric.metric === 'Memory Usage' ? '67%' : '45%',
                    backgroundColor: metric.status === 'excellent' ? '#4CAF50' :
                                   metric.status === 'good' ? '#8BC34A' : '#FF9800',
                    transition: 'width 0.3s ease'
                  }} />
                </div>
              </div>
            ))}
          </div>
        </DashboardCard>
      </DashboardGrid>
    </DashboardLayout>
  );
};

export default AdminDashboard;
