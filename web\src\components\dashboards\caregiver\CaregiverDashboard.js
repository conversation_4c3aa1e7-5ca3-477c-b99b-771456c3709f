import React from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../../../contexts/AuthContext';
import { useTheme } from '../../../contexts/ThemeContext';
import { useAppointments } from '../../../contexts/AppointmentContext';
import DashboardLayout from '../DashboardLayout';
import DashboardCard from '../DashboardCard';

const DashboardGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
`;

const QuickActionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
`;

const ActionButton = styled.button`
  background: white;
  border: 2px solid ${props => props.theme.colors.caregiver};
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-align: center;

  &:hover {
    background-color: ${props => props.theme.colors.caregiver};
    color: white;
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.md};
  }
`;

const ActionIcon = styled.div`
  font-size: 32px;
`;

const ActionLabel = styled.span`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const SectionTitle = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 20px 0;
`;

const PatientCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid ${props => props.theme.colors.borderLight};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: ${props => props.theme.shadows.md};
    transform: translateY(-2px);
  }
`;

const CaregiverDashboard = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const { getTodaysAppointments } = useAppointments();
  const navigate = useNavigate();

  const caregiverColors = getRoleColors('caregiver');
  
  const todaysAppointments = getTodaysAppointments();

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', screen: 'dashboard' },
    { label: 'My Patients', icon: '👥', screen: 'patients' },
    { label: 'Appointments', icon: '📅', screen: 'appointments' },
    { label: 'Patient Care', icon: '🤝', screen: 'patient-care' },
    { label: 'Health Records', icon: '📋', screen: 'health-records' },
    { label: 'Navigation', icon: '🗺️', screen: 'navigation' },
    { label: 'Activities', icon: '🏃', screen: 'activities' },
    { label: 'Reports', icon: '📊', screen: 'reports' },
    { label: 'Profile', icon: '👤', screen: 'profile' }
  ];

  const quickActions = [
    { 
      icon: '👥', 
      label: 'View Patients', 
      action: () => navigate('/caregiver/patients') 
    },
    { 
      icon: '❤️', 
      label: 'Record Vitals', 
      action: () => navigate('/caregiver/vitals') 
    },
    { 
      icon: '🏃', 
      label: 'Log Activity', 
      action: () => navigate('/caregiver/activities') 
    },
    { 
      icon: '🗺️', 
      label: 'Patient Navigation', 
      action: () => navigate('/caregiver/navigation') 
    }
  ];

  // Mock patient data
  const assignedPatients = [
    { 
      id: 1, 
      name: 'Mary Johnson', 
      age: 78, 
      condition: 'Alzheimer\'s', 
      lastCheck: '2 hours ago',
      status: 'stable'
    },
    { 
      id: 2, 
      name: 'Robert Wilson', 
      age: 65, 
      condition: 'Parkinson\'s', 
      lastCheck: '4 hours ago',
      status: 'needs_attention'
    },
    { 
      id: 3, 
      name: 'Linda Davis', 
      age: 72, 
      condition: 'Dementia', 
      lastCheck: '1 hour ago',
      status: 'stable'
    }
  ];

  const recentActivities = [
    { patient: 'Mary Johnson', activity: 'Medication reminder', time: '30 min ago' },
    { patient: 'Robert Wilson', activity: 'Vitals recorded', time: '1 hour ago' },
    { patient: 'Linda Davis', activity: 'Navigation assistance', time: '2 hours ago' }
  ];

  return (
    <DashboardLayout
      title="Caregiver Dashboard"
      roleName="Caregiver"
      menuItems={menuItems}
      headerBackgroundColor={caregiverColors.primary}
      accentColor={caregiverColors.secondary}
    >
      {/* Welcome Section */}
      <div style={{ marginBottom: '32px' }}>
        <h1 style={{ 
          fontSize: '32px', 
          fontWeight: '700', 
          color: theme.colors.text, 
          margin: '0 0 8px 0' 
        }}>
          Welcome, {user?.displayName?.split(' ')[0] || 'Caregiver'}! 🤝
        </h1>
        <p style={{ 
          fontSize: '16px', 
          color: theme.colors.textSecondary, 
          margin: '0' 
        }}>
          You're caring for {assignedPatients.length} patients today
        </p>
      </div>

      {/* Today's Overview Cards */}
      <DashboardGrid>
        <DashboardCard
          title="Assigned Patients"
          icon="👥"
          value={assignedPatients.length}
          label="under your care"
          accentColor={caregiverColors.primary}
          onClick={() => navigate('/caregiver/patients')}
        />

        <DashboardCard
          title="Today's Appointments"
          icon="📅"
          value={todaysAppointments.length}
          label="scheduled visits"
          accentColor={caregiverColors.primary}
          onClick={() => navigate('/appointments')}
        />

        <DashboardCard
          title="Patients Needing Attention"
          icon="⚠️"
          value={assignedPatients.filter(p => p.status === 'needs_attention').length}
          label="require immediate care"
          accentColor={caregiverColors.primary}
          changeType={assignedPatients.filter(p => p.status === 'needs_attention').length > 0 ? 'negative' : 'positive'}
        />

        <DashboardCard
          title="Care Hours Today"
          icon="⏰"
          value="5.2h"
          label="active care time"
          progress={{
            label: "Daily Goal Progress",
            percentage: 65,
            color: caregiverColors.primary
          }}
          accentColor={caregiverColors.primary}
        />
      </DashboardGrid>

      {/* Quick Actions */}
      <SectionTitle theme={theme}>Quick Actions</SectionTitle>
      <QuickActionsGrid>
        {quickActions.map((action, index) => (
          <ActionButton 
            key={index} 
            onClick={action.action}
            theme={theme}
          >
            <ActionIcon>{action.icon}</ActionIcon>
            <ActionLabel theme={theme}>{action.label}</ActionLabel>
          </ActionButton>
        ))}
      </QuickActionsGrid>

      {/* Patient Overview */}
      <DashboardGrid>
        <DashboardCard
          title="My Patients"
          icon="👥"
          accentColor={caregiverColors.primary}
          onClick={() => navigate('/caregiver/patients')}
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {assignedPatients.map((patient, index) => (
              <PatientCard key={patient.id} theme={theme}>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'flex-start',
                  marginBottom: '8px'
                }}>
                  <div>
                    <div style={{ fontWeight: '600', fontSize: '14px' }}>
                      {patient.name}
                    </div>
                    <div style={{ fontSize: '12px', color: theme.colors.textSecondary }}>
                      Age {patient.age} • {patient.condition}
                    </div>
                  </div>
                  <div style={{
                    width: '12px',
                    height: '12px',
                    borderRadius: '50%',
                    backgroundColor: patient.status === 'stable' ? '#4CAF50' : '#FF9800'
                  }} />
                </div>
                <div style={{ fontSize: '12px', color: theme.colors.textSecondary }}>
                  Last check: {patient.lastCheck}
                </div>
              </PatientCard>
            ))}
          </div>
        </DashboardCard>

        <DashboardCard
          title="Recent Activities"
          icon="📝"
          accentColor={caregiverColors.primary}
          onClick={() => navigate('/caregiver/activities')}
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {recentActivities.map((activity, index) => (
              <div key={index} style={{ 
                padding: '12px', 
                backgroundColor: theme.colors.lightGray, 
                borderRadius: '8px'
              }}>
                <div style={{ fontWeight: '600', fontSize: '14px', marginBottom: '4px' }}>
                  {activity.activity}
                </div>
                <div style={{ fontSize: '12px', color: theme.colors.textSecondary }}>
                  {activity.patient} • {activity.time}
                </div>
              </div>
            ))}
          </div>
        </DashboardCard>

        <DashboardCard
          title="Patient Navigation"
          icon="🗺️"
          accentColor={caregiverColors.primary}
          onClick={() => navigate('/caregiver/navigation')}
        >
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>🗺️</div>
            <div style={{ fontSize: '16px', fontWeight: '600', marginBottom: '8px' }}>
              Navigation Assistance
            </div>
            <div style={{ fontSize: '14px', color: theme.colors.textSecondary, marginBottom: '16px' }}>
              Help patients navigate to their destinations
            </div>
            <button style={{
              background: caregiverColors.primary,
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '8px 16px',
              fontSize: '14px',
              fontWeight: '600',
              cursor: 'pointer'
            }}>
              Start Navigation
            </button>
          </div>
        </DashboardCard>
      </DashboardGrid>
    </DashboardLayout>
  );
};

export default CaregiverDashboard;
