import React from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../../../contexts/AuthContext';
import { useTheme } from '../../../contexts/ThemeContext';
import { useAppointments } from '../../../contexts/AppointmentContext';
import { useMedications } from '../../../contexts/MedicationContext';
import DashboardLayout from '../DashboardLayout';
import DashboardCard from '../DashboardCard';

const DashboardGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
`;

const QuickActionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
`;

const ActionButton = styled.button`
  background: white;
  border: 2px solid ${props => props.theme.colors.doctor};
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-align: center;

  &:hover {
    background-color: ${props => props.theme.colors.doctor};
    color: white;
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.md};
  }
`;

const ActionIcon = styled.div`
  font-size: 32px;
`;

const ActionLabel = styled.span`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const SectionTitle = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 20px 0;
`;

const PatientCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid ${props => props.theme.colors.borderLight};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: ${props => props.theme.shadows.md};
    transform: translateY(-2px);
  }
`;

const DoctorDashboard = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const { getUpcomingAppointments, getTodaysAppointments } = useAppointments();
  const { prescriptions } = useMedications();
  const navigate = useNavigate();

  const doctorColors = getRoleColors('doctor');
  
  const upcomingAppointments = getUpcomingAppointments();
  const todaysAppointments = getTodaysAppointments();
  const recentPrescriptions = prescriptions.slice(0, 5);

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', screen: 'dashboard' },
    { label: 'Appointments', icon: '📅', screen: 'appointments' },
    { label: 'My Patients', icon: '👥', screen: 'patients' },
    { label: 'Consultations', icon: '💬', screen: 'consultations' },
    { label: 'Prescriptions', icon: '💊', screen: 'prescriptions' },
    { label: 'Medical Records', icon: '📋', screen: 'medical-records' },
    { label: 'Video Calls', icon: '📹', screen: 'video-calls' },
    { label: 'Profile', icon: '👤', screen: 'profile' },
    { label: 'Settings', icon: '⚙️', screen: 'settings' }
  ];

  const quickActions = [
    { 
      icon: '👥', 
      label: 'Select Patient', 
      action: () => navigate('/patients/select') 
    },
    { 
      icon: '💊', 
      label: 'New Prescription', 
      action: () => navigate('/prescriptions/new') 
    },
    { 
      icon: '📹', 
      label: 'Start Video Call', 
      action: () => navigate('/video-call/start') 
    },
    { 
      icon: '📋', 
      label: 'Medical Notes', 
      action: () => navigate('/medical-notes') 
    }
  ];

  // Mock patient data - in real app, this would come from a patients context
  const recentPatients = [
    { id: 1, name: 'John Doe', lastVisit: '2024-01-15', condition: 'Hypertension' },
    { id: 2, name: 'Jane Smith', lastVisit: '2024-01-14', condition: 'Diabetes' },
    { id: 3, name: 'Bob Johnson', lastVisit: '2024-01-13', condition: 'Anxiety' }
  ];

  return (
    <DashboardLayout
      title="Doctor Dashboard"
      roleName="Doctor"
      menuItems={menuItems}
      headerBackgroundColor={doctorColors.primary}
      accentColor={doctorColors.secondary}
    >
      {/* Welcome Section */}
      <div style={{ marginBottom: '32px' }}>
        <h1 style={{ 
          fontSize: '32px', 
          fontWeight: '700', 
          color: theme.colors.text, 
          margin: '0 0 8px 0' 
        }}>
          Good morning, Dr. {user?.displayName?.split(' ')[0] || 'Doctor'}! 👨‍⚕️
        </h1>
        <p style={{ 
          fontSize: '16px', 
          color: theme.colors.textSecondary, 
          margin: '0' 
        }}>
          You have {todaysAppointments.length} appointments scheduled for today
        </p>
      </div>

      {/* Today's Overview Cards */}
      <DashboardGrid>
        <DashboardCard
          title="Today's Appointments"
          icon="📅"
          value={todaysAppointments.length}
          label="patients to see"
          accentColor={doctorColors.primary}
          onClick={() => navigate('/appointments')}
        />

        <DashboardCard
          title="Total Patients"
          icon="👥"
          value="127"
          label="active patients"
          change="+3 this week"
          changeType="positive"
          accentColor={doctorColors.primary}
          onClick={() => navigate('/patients')}
        />

        <DashboardCard
          title="Prescriptions Issued"
          icon="💊"
          value={recentPrescriptions.length}
          label="this week"
          accentColor={doctorColors.primary}
          onClick={() => navigate('/prescriptions')}
        />

        <DashboardCard
          title="Consultation Hours"
          icon="⏰"
          value="6.5h"
          label="today"
          progress={{
            label: "Daily Goal Progress",
            percentage: 81,
            color: doctorColors.primary
          }}
          accentColor={doctorColors.primary}
        />
      </DashboardGrid>

      {/* Quick Actions */}
      <SectionTitle theme={theme}>Quick Actions</SectionTitle>
      <QuickActionsGrid>
        {quickActions.map((action, index) => (
          <ActionButton 
            key={index} 
            onClick={action.action}
            theme={theme}
          >
            <ActionIcon>{action.icon}</ActionIcon>
            <ActionLabel theme={theme}>{action.label}</ActionLabel>
          </ActionButton>
        ))}
      </QuickActionsGrid>

      {/* Recent Activity */}
      <DashboardGrid>
        <DashboardCard
          title="Today's Schedule"
          icon="📅"
          accentColor={doctorColors.primary}
          onClick={() => navigate('/appointments')}
        >
          {todaysAppointments.length > 0 ? (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {todaysAppointments.slice(0, 4).map((appointment, index) => (
                <div key={index} style={{ 
                  padding: '12px', 
                  backgroundColor: theme.colors.lightGray, 
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <div>
                    <div style={{ fontWeight: '600', fontSize: '14px' }}>
                      {appointment.patientName || 'Patient'}
                    </div>
                    <div style={{ fontSize: '12px', color: theme.colors.textSecondary }}>
                      {appointment.type || 'Consultation'}
                    </div>
                  </div>
                  <div style={{ 
                    fontSize: '12px', 
                    color: doctorColors.primary,
                    fontWeight: '600'
                  }}>
                    {new Date(appointment.appointmentDate).toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p style={{ color: theme.colors.textSecondary, fontSize: '14px', margin: '0' }}>
              No appointments scheduled for today
            </p>
          )}
        </DashboardCard>

        <DashboardCard
          title="Recent Patients"
          icon="👥"
          accentColor={doctorColors.primary}
          onClick={() => navigate('/patients')}
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {recentPatients.map((patient, index) => (
              <PatientCard key={patient.id} theme={theme}>
                <div style={{ fontWeight: '600', fontSize: '14px', marginBottom: '4px' }}>
                  {patient.name}
                </div>
                <div style={{ fontSize: '12px', color: theme.colors.textSecondary }}>
                  Last visit: {new Date(patient.lastVisit).toLocaleDateString()}
                </div>
                <div style={{ 
                  fontSize: '12px', 
                  color: doctorColors.primary,
                  fontWeight: '500'
                }}>
                  {patient.condition}
                </div>
              </PatientCard>
            ))}
          </div>
        </DashboardCard>

        <DashboardCard
          title="Chat Consultation"
          icon="💬"
          accentColor={doctorColors.primary}
          onClick={() => navigate('/consultations')}
        >
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>
            <div style={{ fontSize: '16px', fontWeight: '600', marginBottom: '8px' }}>
              Start Consultation
            </div>
            <div style={{ fontSize: '14px', color: theme.colors.textSecondary }}>
              Select a patient and begin video consultation
            </div>
          </div>
        </DashboardCard>
      </DashboardGrid>
    </DashboardLayout>
  );
};

export default DoctorDashboard;
