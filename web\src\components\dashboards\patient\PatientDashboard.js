import React from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../../../contexts/AuthContext';
import { useTheme } from '../../../contexts/ThemeContext';
import { useAppointments } from '../../../contexts/AppointmentContext';
import { useMedications } from '../../../contexts/MedicationContext';
import { useVitals } from '../../../contexts/VitalsContext';
import DashboardLayout from '../DashboardLayout';
import DashboardCard from '../DashboardCard';

const DashboardGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
`;

const QuickActionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
`;

const ActionButton = styled.button`
  background: white;
  border: 2px solid ${props => props.theme.colors.primary};
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-align: center;

  &:hover {
    background-color: ${props => props.theme.colors.primary};
    color: white;
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.md};
  }
`;

const ActionIcon = styled.div`
  font-size: 32px;
`;

const ActionLabel = styled.span`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const SectionTitle = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 20px 0;
`;

const PatientDashboard = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const { getUpcomingAppointments, getTodaysAppointments } = useAppointments();
  const { getRecentPrescriptions, getActiveMedications } = useMedications();
  const { getRecentVitals, getTodaysVitals } = useVitals();
  const navigate = useNavigate();

  const patientColors = getRoleColors('patient');
  
  const upcomingAppointments = getUpcomingAppointments();
  const todaysAppointments = getTodaysAppointments();
  const recentPrescriptions = getRecentPrescriptions();
  const activeMedications = getActiveMedications();
  const recentVitals = getRecentVitals();
  const todaysVitals = getTodaysVitals();

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', screen: 'dashboard' },
    { label: 'Appointments', icon: '📅', screen: 'appointments' },
    { label: 'My Doctors', icon: '👨‍⚕️', screen: 'doctors' },
    { label: 'Prescriptions', icon: '💊', screen: 'prescriptions' },
    { label: 'Health Records', icon: '📋', screen: 'health-records' },
    { label: 'Vitals', icon: '❤️', screen: 'vitals' },
    { label: 'Messages', icon: '💬', screen: 'messages' },
    { label: 'Profile', icon: '👤', screen: 'profile' },
    { label: 'Settings', icon: '⚙️', screen: 'settings' }
  ];

  const quickActions = [
    { 
      icon: '📅', 
      label: 'Request Appointment', 
      action: () => navigate('/appointments/new') 
    },
    { 
      icon: '📍', 
      label: 'My Locations', 
      action: () => navigate('/locations') 
    },
    { 
      icon: '📝', 
      label: 'Record Vitals', 
      action: () => navigate('/vitals/record') 
    },
    { 
      icon: '💊', 
      label: 'Add Medication', 
      action: () => navigate('/medications/add') 
    }
  ];

  return (
    <DashboardLayout
      title="Patient Dashboard"
      roleName="Patient"
      menuItems={menuItems}
      headerBackgroundColor={patientColors.primary}
      accentColor={patientColors.secondary}
    >
      {/* Welcome Section */}
      <div style={{ marginBottom: '32px' }}>
        <h1 style={{ 
          fontSize: '32px', 
          fontWeight: '700', 
          color: theme.colors.text, 
          margin: '0 0 8px 0' 
        }}>
          Welcome back, {user?.displayName?.split(' ')[0] || 'Patient'}! 👋
        </h1>
        <p style={{ 
          fontSize: '16px', 
          color: theme.colors.textSecondary, 
          margin: '0' 
        }}>
          Here's your health overview for today
        </p>
      </div>

      {/* Today's Overview Cards */}
      <DashboardGrid>
        <DashboardCard
          title="Today's Appointments"
          icon="📅"
          value={todaysAppointments.length}
          label="appointments scheduled"
          accentColor={patientColors.primary}
          onClick={() => navigate('/appointments')}
        />

        <DashboardCard
          title="Active Medications"
          icon="💊"
          value={activeMedications.length}
          label="medications to take"
          accentColor={patientColors.primary}
          onClick={() => navigate('/medications')}
        />

        <DashboardCard
          title="Vitals Recorded"
          icon="❤️"
          value={todaysVitals.length}
          label="measurements today"
          accentColor={patientColors.primary}
          onClick={() => navigate('/vitals')}
        />

        <DashboardCard
          title="Health Score"
          icon="📊"
          value="85%"
          label="overall health"
          change="+5% from last week"
          changeType="positive"
          accentColor={patientColors.primary}
          progress={{
            label: "Health Progress",
            percentage: 85,
            color: patientColors.primary
          }}
        />
      </DashboardGrid>

      {/* Quick Actions */}
      <SectionTitle theme={theme}>Quick Actions</SectionTitle>
      <QuickActionsGrid>
        {quickActions.map((action, index) => (
          <ActionButton 
            key={index} 
            onClick={action.action}
            theme={theme}
          >
            <ActionIcon>{action.icon}</ActionIcon>
            <ActionLabel theme={theme}>{action.label}</ActionLabel>
          </ActionButton>
        ))}
      </QuickActionsGrid>

      {/* Recent Activity */}
      <DashboardGrid>
        <DashboardCard
          title="Upcoming Appointments"
          icon="📅"
          accentColor={patientColors.primary}
          onClick={() => navigate('/appointments')}
        >
          {upcomingAppointments.length > 0 ? (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {upcomingAppointments.slice(0, 3).map((appointment, index) => (
                <div key={index} style={{ 
                  padding: '12px', 
                  backgroundColor: theme.colors.lightGray, 
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <div>
                    <div style={{ fontWeight: '600', fontSize: '14px' }}>
                      Dr. {appointment.doctorName || 'Unknown'}
                    </div>
                    <div style={{ fontSize: '12px', color: theme.colors.textSecondary }}>
                      {new Date(appointment.appointmentDate).toLocaleDateString()}
                    </div>
                  </div>
                  <div style={{ 
                    fontSize: '12px', 
                    color: patientColors.primary,
                    fontWeight: '600'
                  }}>
                    {new Date(appointment.appointmentDate).toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p style={{ color: theme.colors.textSecondary, fontSize: '14px', margin: '0' }}>
              No upcoming appointments
            </p>
          )}
        </DashboardCard>

        <DashboardCard
          title="Recent Prescriptions"
          icon="💊"
          accentColor={patientColors.primary}
          onClick={() => navigate('/prescriptions')}
        >
          {recentPrescriptions.length > 0 ? (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {recentPrescriptions.map((prescription, index) => (
                <div key={index} style={{ 
                  padding: '12px', 
                  backgroundColor: theme.colors.lightGray, 
                  borderRadius: '8px'
                }}>
                  <div style={{ fontWeight: '600', fontSize: '14px' }}>
                    {prescription.medicationName}
                  </div>
                  <div style={{ fontSize: '12px', color: theme.colors.textSecondary }}>
                    {prescription.dosage} - {prescription.frequency}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p style={{ color: theme.colors.textSecondary, fontSize: '14px', margin: '0' }}>
              No recent prescriptions
            </p>
          )}
        </DashboardCard>

        <DashboardCard
          title="Recent Vitals"
          icon="❤️"
          accentColor={patientColors.primary}
          onClick={() => navigate('/vitals')}
        >
          {recentVitals.length > 0 ? (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {recentVitals.slice(0, 3).map((vital, index) => (
                <div key={index} style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '8px 0',
                  borderBottom: index < 2 ? `1px solid ${theme.colors.borderLight}` : 'none'
                }}>
                  <span style={{ fontSize: '14px', color: theme.colors.textSecondary }}>
                    {vital.type?.replace('_', ' ').toUpperCase()}
                  </span>
                  <span style={{ fontSize: '14px', fontWeight: '600' }}>
                    {vital.value} {vital.unit}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <p style={{ color: theme.colors.textSecondary, fontSize: '14px', margin: '0' }}>
              No recent vitals recorded
            </p>
          )}
        </DashboardCard>
      </DashboardGrid>
    </DashboardLayout>
  );
};

export default PatientDashboard;
