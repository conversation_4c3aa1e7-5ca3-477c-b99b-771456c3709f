import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAuth } from '../../../contexts/AuthContext';
import { useVitals } from '../../../contexts/VitalsContext';
import { useAppointments } from '../../../contexts/AppointmentContext';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../DashboardLayout';
import DashboardCard from '../DashboardCard';
import UpcomingList from '../UpcomingList';
import MedicationReminders from './medications/MedicationReminders';
import RecentPrescriptions from './prescriptions/RecentPrescriptions';
import { ROLE_COLORS } from '../../../config/theme';

// Get patient colors for use in styles (EXACT from mobile)
const PATIENT_COLORS = ROLE_COLORS.patient;

const Container = styled.div`
  padding: 16px;
  background: #f5f7fa;
  min-height: 100vh;
`;

const RefreshContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
`;

const RefreshButton = styled.button`
  background: ${PATIENT_COLORS.primary};
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  
  &:hover {
    background: ${PATIENT_COLORS.primaryLight};
  }
`;

const LoadingIndicator = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  color: #666;
`;

const PatientDashboardMobile = ({ notifications = [] }) => {
  const { user } = useAuth();
  const { getAllVitals } = useVitals();
  const { getUpcomingAppointments, fetchAppointments } = useAppointments();
  const navigate = useNavigate();
  
  // State variables (EXACT from mobile)
  const [refreshing, setRefreshing] = useState(false);
  const [healthStats, setHealthStats] = useState({
    heartRate: '--',
    bloodPressure: '--/--',
    bloodGlucose: '--',
    weight: '--'
  });
  const [loadingVitals, setLoadingVitals] = useState(true);
  const [vitalStatus, setVitalStatus] = useState({
    heartRate: 'normal',
    bloodPressure: 'normal',
    bloodGlucose: 'normal',
    weight: 'normal'
  });
  const [appointments, setAppointments] = useState([]);
  const [medications, setMedications] = useState([]);
  const [recentActivities, setRecentActivities] = useState([]);

  // Game states (EXACT from mobile)
  const [numberGameActive, setNumberGameActive] = useState(false);
  const [numberGameStep, setNumberGameStep] = useState('intro');
  const [numberSequence, setNumberSequence] = useState([4, 7, 2, 9, 5]);
  const [userNumberInput, setUserNumberInput] = useState([]);
  const [numberGameResult, setNumberGameResult] = useState(null);

  const [visualGameActive, setVisualGameActive] = useState(false);
  const [visualGameStep, setVisualGameStep] = useState('intro');
  const [highlightedCells, setHighlightedCells] = useState([2, 5, 7]);
  const [userCellsSelection, setUserCellsSelection] = useState([]);
  const [visualGameResult, setVisualGameResult] = useState(null);

  // Menu items (EXACT from mobile)
  const menuItems = [
    { label: 'Dashboard', icon: 'home', screen: '/dashboard' },
    { label: 'Appointments', icon: 'calendar', screen: '/appointments' },
    { label: 'Medications', icon: 'medkit', screen: '/medications' },
    { label: 'Prescriptions', icon: 'document', screen: '/prescriptions' },
    { label: 'My Doctors', icon: 'medical', screen: '/doctors' },
    { label: 'My Locations', icon: 'map', screen: '/map' },
    { label: 'Guidance', icon: 'navigate', screen: '/guidance' },
    { label: 'Messages', icon: 'chatbubble', screen: '/messages' },
    { label: 'Payments', icon: 'card', screen: '/payments' },
    { label: 'My QR Code', icon: 'qr-code', screen: '/profile/qr' },
    { label: 'Profile', icon: 'person', screen: '/profile' },
    { label: 'Settings', icon: 'settings', screen: '/settings' }
  ];

  // Refresh function (EXACT from mobile)
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      // Reload vitals, medications, and appointments
      await loadLatestVitals();
      await loadMedications();
      await loadAppointments();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Function to load medications (EXACT from mobile)
  const loadMedications = async () => {
    try {
      const patientId = user?.uid || 'local-user';
      // For web, we'll use a different service or mock data
      const medsData = []; // TODO: Implement web medication service

      // Format medications for the UpcomingList component
      const formattedMedications = medsData.map(med => ({
        id: med.id,
        title: med.name,
        description: `${med.dosage}, ${med.frequency}`,
        time: med.nextDose || 'As prescribed',
        type: 'medication',
        status: 'active'
      }));

      setMedications(formattedMedications);
    } catch (error) {
      console.error('Error loading medications:', error);
    }
  };

  // Function to load appointments from Firebase (EXACT from mobile)
  const loadAppointments = async () => {
    try {
      console.log('Loading appointments...');
      console.log('Current user:', user?.uid, user?.email);

      // Refresh appointments from Firebase
      const fetchedAppointments = await fetchAppointments(true);
      console.log('Appointments fetched directly:', fetchedAppointments?.length || 0);

      // Wait a short moment to ensure context is updated
      await new Promise(resolve => setTimeout(resolve, 500));

      // Get upcoming appointments from context after fetching
      let upcomingAppointments = getUpcomingAppointments();
      console.log('Upcoming appointments from context:', upcomingAppointments);

      // If context is empty but we have appointments, use fetched appointments directly
      if ((!upcomingAppointments || upcomingAppointments.length === 0) && fetchedAppointments && fetchedAppointments.length > 0) {
        console.log('Context is empty but we have fetched appointments, using them directly');
        // Filter upcoming appointments (not cancelled and in the future)
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        upcomingAppointments = fetchedAppointments.filter(appointment => {
          // Check if appointment is cancelled
          if (appointment.status?.toLowerCase() === 'cancelled') {
            return false;
          }

          try {
            // Try to parse the date
            const dateParts = appointment.date.split('-');
            if (dateParts.length !== 3) return false;

            const year = parseInt(dateParts[0], 10);
            const month = parseInt(dateParts[1], 10) - 1;
            const day = parseInt(dateParts[2], 10);

            const appointmentDate = new Date(year, month, day);
            return appointmentDate >= today;
          } catch (error) {
            console.error('Error parsing date:', error);
            return false;
          }
        });
      }

      // Format appointments for the UpcomingList component (EXACT from mobile)
      const formattedAppointments = upcomingAppointments.map(appointment => {
        console.log('Formatting appointment for display:', appointment);

        // Parse the appointment date
        let appointmentDate = new Date();

        try {
          // Parse the date part
          const dateParts = appointment.date.split('-');
          if (dateParts.length === 3) {
            const year = parseInt(dateParts[0], 10);
            const month = parseInt(dateParts[1], 10) - 1; // Months are 0-indexed in JS
            const day = parseInt(dateParts[2], 10);

            // Create a new date object with the date parts
            appointmentDate = new Date(year, month, day);

            // Handle 12-hour format with AM/PM for the time
            if (appointment.time.includes('AM') || appointment.time.includes('PM')) {
              // Extract hours and minutes from time string (e.g., "10:00 AM")
              const timeParts = appointment.time.replace(/\s*(AM|PM)\s*$/i, '').split(':');
              let hours = parseInt(timeParts[0], 10);
              const minutes = parseInt(timeParts[1], 10);

              // Adjust hours for PM
              if (appointment.time.includes('PM') && hours < 12) {
                hours += 12;
              }
              // Adjust for 12 AM
              if (appointment.time.includes('AM') && hours === 12) {
                hours = 0;
              }

              // Set the time part
              appointmentDate.setHours(hours, minutes, 0, 0);
            } else {
              // Handle 24-hour format
              const timeParts = appointment.time.split(':');
              if (timeParts.length >= 2) {
                const hours = parseInt(timeParts[0], 10);
                const minutes = parseInt(timeParts[1], 10);
                appointmentDate.setHours(hours, minutes, 0, 0);
              }
            }
          }
        } catch (error) {
          console.error('Error parsing appointment date:', error);
          // Use current date as fallback
          appointmentDate = new Date();
        }

        const now = new Date();

        // Format the time display
        let timeDisplay;
        const dayDiff = Math.round((appointmentDate - now) / (24 * 60 * 60 * 1000));

        if (dayDiff === 0) {
          timeDisplay = `Today, ${appointment.time}`;
        } else if (dayDiff === 1) {
          timeDisplay = `Tomorrow, ${appointment.time}`;
        } else {
          timeDisplay = `${appointmentDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}, ${appointment.time}`;
        }

        // Format the appointment information for display
        return {
          id: appointment.id,
          title: appointment.doctor,
          description: `${appointment.specialty || 'Medical Appointment'}${appointment.reason ? ` - ${appointment.reason}` : ''}`,
          time: timeDisplay,
          type: 'appointment',
          status: appointment.status?.toLowerCase() || 'pending',
          appointmentDate: appointment.date,
          appointmentDateObj: appointmentDate,
          appointmentTime: appointment.time,
          location: appointment.location || 'Neuro Care Medical Center',
          duration: appointment.duration ? `${appointment.duration} min` : '30 min',
          dateObject: appointmentDate
        };
      });

      // Sort appointments by date (from earliest to latest) (EXACT from mobile)
      formattedAppointments.sort((a, b) => {
        if (a.dateObject && b.dateObject) {
          return a.dateObject.getTime() - b.dateObject.getTime();
        }
        return a.appointmentDate.localeCompare(b.appointmentDate);
      });

      setAppointments(formattedAppointments);
    } catch (error) {
      console.error('Error loading appointments from Firebase:', error);
    }
  };

  // Navigation functions (adapted for web)
  const navigateToRecordVitals = () => {
    navigate('/vitals/record');
  };

  const navigateToHealthRecords = () => {
    navigate('/vitals');
  };

  const handleRequestAppointment = () => {
    navigate('/appointments', { state: { openRequestModal: true } });
  };

  // Status determination functions (EXACT from mobile)
  const getHeartRateStatus = (rate) => {
    if (!rate || rate === '--') return 'normal';
    const numRate = parseInt(rate);
    if (numRate < 60) return 'low';
    if (numRate > 100) return 'high';
    return 'normal';
  };

  const getBloodPressureStatus = (bp) => {
    if (!bp || bp === '--/--') return 'normal';
    const [systolic, diastolic] = bp.split('/').map(v => parseInt(v));
    if (systolic > 140 || diastolic > 90) return 'high';
    if (systolic < 90 || diastolic < 60) return 'low';
    return 'normal';
  };

  const getBloodGlucoseStatus = (glucose) => {
    if (!glucose || glucose === '--') return 'normal';
    const numGlucose = parseInt(glucose);
    if (numGlucose > 140) return 'high';
    if (numGlucose < 70) return 'low';
    return 'normal';
  };

  const getWeightStatus = (weight) => {
    if (!weight || weight === '--') return 'normal';
    return 'normal';
  };

  // Load latest vitals function (EXACT from mobile logic)
  const loadLatestVitals = async () => {
    setLoadingVitals(true);
    try {
      // Get all vitals from Firebase using VitalsContext
      const allVitals = await getAllVitals();

      // Get the most recent vital of each type for the health stats
      const newHealthStats = { ...healthStats };
      const newVitalStatus = { ...vitalStatus };
      const newRecentActivities = [];

      // Process heart rate readings
      if (allVitals.heartRate && allVitals.heartRate.length > 0) {
        const sortedHeartRates = [...allVitals.heartRate].sort(
          (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
        );

        const latestHeartRate = sortedHeartRates[0];
        newHealthStats.heartRate = latestHeartRate.values.value.toString();
        newVitalStatus.heartRate = getHeartRateStatus(newHealthStats.heartRate);

        sortedHeartRates.forEach(reading => {
          newRecentActivities.push({
            type: 'heartRate',
            value: reading.values.value.toString(),
            timestamp: reading.timestamp,
            icon: 'heart',
            color: '#EA4335'
          });
        });
      }

      // Process blood pressure readings
      if (allVitals.bloodPressure && allVitals.bloodPressure.length > 0) {
        const sortedBP = [...allVitals.bloodPressure].sort(
          (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
        );

        const latestBP = sortedBP[0];
        const bp = latestBP.values;
        newHealthStats.bloodPressure = `${bp.systolic}/${bp.diastolic}`;
        newVitalStatus.bloodPressure = getBloodPressureStatus(newHealthStats.bloodPressure);

        sortedBP.forEach(reading => {
          const bpValue = `${reading.values.systolic}/${reading.values.diastolic}`;
          newRecentActivities.push({
            type: 'bloodPressure',
            value: bpValue,
            timestamp: reading.timestamp,
            icon: 'fitness',
            color: '#4285F4'
          });
        });
      }

      // Process blood glucose readings
      if (allVitals.bloodGlucose && allVitals.bloodGlucose.length > 0) {
        const sortedGlucose = [...allVitals.bloodGlucose].sort(
          (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
        );

        const latestGlucose = sortedGlucose[0];
        newHealthStats.bloodGlucose = latestGlucose.values.value.toString();
        newVitalStatus.bloodGlucose = getBloodGlucoseStatus(newHealthStats.bloodGlucose);

        sortedGlucose.forEach(reading => {
          newRecentActivities.push({
            type: 'bloodGlucose',
            value: reading.values.value.toString(),
            timestamp: reading.timestamp,
            icon: 'water',
            color: '#FBBC05'
          });
        });
      }

      // Process weight readings
      if (allVitals.weight && allVitals.weight.length > 0) {
        const sortedWeight = [...allVitals.weight].sort(
          (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
        );

        const latestWeight = sortedWeight[0];
        newHealthStats.weight = latestWeight.values.value.toString();
        newVitalStatus.weight = getWeightStatus(newHealthStats.weight);

        sortedWeight.forEach(reading => {
          newRecentActivities.push({
            type: 'weight',
            value: reading.values.value.toString(),
            timestamp: reading.timestamp,
            icon: 'body',
            color: '#34A853'
          });
        });
      }

      // Update state
      setHealthStats(newHealthStats);
      setVitalStatus(newVitalStatus);
      setRecentActivities(newRecentActivities);
    } catch (error) {
      console.error('Error loading vitals:', error);
    } finally {
      setLoadingVitals(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadLatestVitals();
    loadMedications();
    loadAppointments();
  }, [user]);

  if (loadingVitals) {
    return (
      <Container>
        <LoadingIndicator>Loading dashboard...</LoadingIndicator>
      </Container>
    );
  }

  return (
    <DashboardLayout menuItems={menuItems} userRole="patient">
      <Container>
        <RefreshContainer>
          <RefreshButton onClick={onRefresh} disabled={refreshing}>
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </RefreshButton>
        </RefreshContainer>

        {/* Health Stats Cards */}
        <DashboardCard
          title="Health Overview"
          subtitle="Your latest vital signs"
          onPress={navigateToHealthRecords}
        >
          {/* Health stats content will go here */}
        </DashboardCard>

        {/* Upcoming Appointments */}
        <DashboardCard
          title="Upcoming Appointments"
          subtitle={`${appointments.length} scheduled`}
          onPress={() => navigate('/appointments')}
        >
          <UpcomingList items={appointments} />
        </DashboardCard>

        {/* Medication Reminders */}
        <MedicationReminders medications={medications} />

        {/* Recent Prescriptions */}
        <RecentPrescriptions />

        {/* Quick Actions */}
        <DashboardCard
          title="Quick Actions"
          subtitle="Common tasks"
        >
          <button onClick={navigateToRecordVitals}>Record Vitals</button>
          <button onClick={handleRequestAppointment}>Request Appointment</button>
        </DashboardCard>
      </Container>
    </DashboardLayout>
  );
};

export default PatientDashboardMobile;
