import React from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../../../contexts/AuthContext';
import { useTheme } from '../../../contexts/ThemeContext';
import { useAppointments } from '../../../contexts/AppointmentContext';
import DashboardLayout from '../DashboardLayout';
import DashboardCard from '../DashboardCard';

const DashboardGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
`;

const QuickActionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
`;

const ActionButton = styled.button`
  background: white;
  border: 2px solid ${props => props.theme.colors.supervisor};
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-align: center;

  &:hover {
    background-color: ${props => props.theme.colors.supervisor};
    color: white;
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.md};
  }
`;

const ActionIcon = styled.div`
  font-size: 32px;
`;

const ActionLabel = styled.span`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const SectionTitle = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 20px 0;
`;

const SupervisorDashboard = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const { appointments } = useAppointments();
  const navigate = useNavigate();

  const supervisorColors = getRoleColors('supervisor');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', screen: 'dashboard' },
    { label: 'Caregivers', icon: '🤝', screen: 'caregivers' },
    { label: 'Patients', icon: '👥', screen: 'patients' },
    { label: 'Patient Tracking', icon: '📍', screen: 'tracking' },
    { label: 'Appointments', icon: '📅', screen: 'appointments' },
    { label: 'Patient Health', icon: '❤️', screen: 'patient-health' },
    { label: 'Guidance System', icon: '🗺️', screen: 'guidance' },
    { label: 'Reports', icon: '📊', screen: 'reports' },
    { label: 'Profile', icon: '👤', screen: 'profile' }
  ];

  const quickActions = [
    { 
      icon: '👥', 
      label: 'Add Patient', 
      action: () => navigate('/supervisor/patients/add') 
    },
    { 
      icon: '🤝', 
      label: 'Add Caregiver', 
      action: () => navigate('/supervisor/caregivers/add') 
    },
    { 
      icon: '📍', 
      label: 'Track Patients', 
      action: () => navigate('/supervisor/tracking') 
    },
    { 
      icon: '🗺️', 
      label: 'Send Route', 
      action: () => navigate('/supervisor/guidance') 
    }
  ];

  // Mock data for supervisor overview
  const supervisorStats = {
    totalPatients: 45,
    activeCaregivers: 12,
    todaysAppointments: appointments.length,
    patientsNeedingAttention: 3
  };

  const recentAlerts = [
    { 
      patient: 'John Doe', 
      alert: 'Missed medication reminder', 
      time: '15 min ago',
      severity: 'medium'
    },
    { 
      patient: 'Mary Smith', 
      alert: 'Unusual location detected', 
      time: '1 hour ago',
      severity: 'high'
    },
    { 
      patient: 'Bob Johnson', 
      alert: 'Vitals recorded successfully', 
      time: '2 hours ago',
      severity: 'low'
    }
  ];

  const caregiverStatus = [
    { name: 'Sarah Wilson', patients: 4, status: 'active', lastUpdate: '5 min ago' },
    { name: 'Mike Brown', patients: 3, status: 'active', lastUpdate: '12 min ago' },
    { name: 'Lisa Garcia', patients: 5, status: 'break', lastUpdate: '45 min ago' }
  ];

  return (
    <DashboardLayout
      title="Supervisor Dashboard"
      roleName="Supervisor"
      menuItems={menuItems}
      headerBackgroundColor={supervisorColors.primary}
      accentColor={supervisorColors.secondary}
    >
      {/* Welcome Section */}
      <div style={{ marginBottom: '32px' }}>
        <h1 style={{ 
          fontSize: '32px', 
          fontWeight: '700', 
          color: theme.colors.text, 
          margin: '0 0 8px 0' 
        }}>
          Supervision Center 👁️
        </h1>
        <p style={{ 
          fontSize: '16px', 
          color: theme.colors.textSecondary, 
          margin: '0' 
        }}>
          Monitor and coordinate care for {supervisorStats.totalPatients} patients
        </p>
      </div>

      {/* Overview Cards */}
      <DashboardGrid>
        <DashboardCard
          title="Total Patients"
          icon="👥"
          value={supervisorStats.totalPatients}
          label="under supervision"
          change="+2 this week"
          changeType="positive"
          accentColor={supervisorColors.primary}
          onClick={() => navigate('/supervisor/patients')}
        />

        <DashboardCard
          title="Active Caregivers"
          icon="🤝"
          value={supervisorStats.activeCaregivers}
          label="on duty"
          accentColor={supervisorColors.primary}
          onClick={() => navigate('/supervisor/caregivers')}
        />

        <DashboardCard
          title="Today's Appointments"
          icon="📅"
          value={supervisorStats.todaysAppointments}
          label="scheduled"
          accentColor={supervisorColors.primary}
          onClick={() => navigate('/supervisor/appointments')}
        />

        <DashboardCard
          title="Alerts"
          icon="⚠️"
          value={supervisorStats.patientsNeedingAttention}
          label="require attention"
          accentColor={supervisorStats.patientsNeedingAttention > 0 ? '#FF9800' : supervisorColors.primary}
          changeType={supervisorStats.patientsNeedingAttention > 0 ? 'negative' : 'positive'}
        />
      </DashboardGrid>

      {/* Quick Actions */}
      <SectionTitle theme={theme}>Supervision Tools</SectionTitle>
      <QuickActionsGrid>
        {quickActions.map((action, index) => (
          <ActionButton 
            key={index} 
            onClick={action.action}
            theme={theme}
          >
            <ActionIcon>{action.icon}</ActionIcon>
            <ActionLabel theme={theme}>{action.label}</ActionLabel>
          </ActionButton>
        ))}
      </QuickActionsGrid>

      {/* Monitoring Sections */}
      <DashboardGrid>
        <DashboardCard
          title="Recent Alerts"
          icon="🚨"
          accentColor={supervisorColors.primary}
          onClick={() => navigate('/supervisor/alerts')}
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {recentAlerts.map((alert, index) => (
              <div key={index} style={{ 
                padding: '12px', 
                backgroundColor: theme.colors.lightGray, 
                borderRadius: '8px',
                borderLeft: `4px solid ${
                  alert.severity === 'high' ? '#F44336' :
                  alert.severity === 'medium' ? '#FF9800' : '#4CAF50'
                }`
              }}>
                <div style={{ fontWeight: '600', fontSize: '14px', marginBottom: '4px' }}>
                  {alert.patient}
                </div>
                <div style={{ fontSize: '12px', color: theme.colors.textSecondary, marginBottom: '4px' }}>
                  {alert.alert}
                </div>
                <div style={{ fontSize: '11px', color: theme.colors.textSecondary }}>
                  {alert.time}
                </div>
              </div>
            ))}
          </div>
        </DashboardCard>

        <DashboardCard
          title="Caregiver Status"
          icon="🤝"
          accentColor={supervisorColors.primary}
          onClick={() => navigate('/supervisor/caregivers')}
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {caregiverStatus.map((caregiver, index) => (
              <div key={index} style={{ 
                padding: '12px', 
                backgroundColor: theme.colors.lightGray, 
                borderRadius: '8px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <div>
                  <div style={{ fontWeight: '600', fontSize: '14px' }}>
                    {caregiver.name}
                  </div>
                  <div style={{ fontSize: '12px', color: theme.colors.textSecondary }}>
                    {caregiver.patients} patients • {caregiver.lastUpdate}
                  </div>
                </div>
                <div style={{
                  padding: '4px 8px',
                  borderRadius: '12px',
                  fontSize: '11px',
                  fontWeight: '600',
                  backgroundColor: caregiver.status === 'active' ? '#E8F5E8' : '#FFF3E0',
                  color: caregiver.status === 'active' ? '#2E7D32' : '#F57C00'
                }}>
                  {caregiver.status.toUpperCase()}
                </div>
              </div>
            ))}
          </div>
        </DashboardCard>

        <DashboardCard
          title="Patient Tracking Map"
          icon="🗺️"
          accentColor={supervisorColors.primary}
          onClick={() => navigate('/supervisor/tracking')}
        >
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>🗺️</div>
            <div style={{ fontSize: '16px', fontWeight: '600', marginBottom: '8px' }}>
              Real-time Tracking
            </div>
            <div style={{ fontSize: '14px', color: theme.colors.textSecondary, marginBottom: '16px' }}>
              Monitor patient locations and send navigation assistance
            </div>
            <button style={{
              background: supervisorColors.primary,
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '8px 16px',
              fontSize: '14px',
              fontWeight: '600',
              cursor: 'pointer'
            }}>
              Open Map
            </button>
          </div>
        </DashboardCard>
      </DashboardGrid>

      {/* Additional Monitoring */}
      <DashboardGrid>
        <DashboardCard
          title="System Performance"
          icon="📊"
          accentColor={supervisorColors.primary}
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            {[
              { metric: 'Response Time', value: '98%', target: '95%' },
              { metric: 'Caregiver Efficiency', value: '92%', target: '90%' },
              { metric: 'Patient Satisfaction', value: '96%', target: '95%' },
              { metric: 'Alert Resolution', value: '89%', target: '85%' }
            ].map((metric, index) => (
              <div key={index}>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  marginBottom: '4px' 
                }}>
                  <span style={{ fontSize: '14px', color: theme.colors.textSecondary }}>
                    {metric.metric}
                  </span>
                  <span style={{ fontSize: '14px', fontWeight: '600' }}>
                    {metric.value}
                  </span>
                </div>
                <div style={{ 
                  width: '100%', 
                  height: '6px', 
                  backgroundColor: theme.colors.lightGray,
                  borderRadius: '3px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    height: '100%',
                    width: metric.value,
                    backgroundColor: supervisorColors.primary,
                    transition: 'width 0.3s ease'
                  }} />
                </div>
                <div style={{ 
                  fontSize: '11px', 
                  color: theme.colors.textSecondary,
                  marginTop: '2px'
                }}>
                  Target: {metric.target}
                </div>
              </div>
            ))}
          </div>
        </DashboardCard>
      </DashboardGrid>
    </DashboardLayout>
  );
};

export default SupervisorDashboard;
