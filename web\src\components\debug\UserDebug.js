import React from 'react';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';

const DebugContainer = styled.div`
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 16px;
  border-radius: 8px;
  font-family: monospace;
  font-size: 12px;
  max-width: 400px;
  max-height: 300px;
  overflow: auto;
  z-index: 10000;
  border: 2px solid #4CAF50;
`;

const DebugTitle = styled.h3`
  margin: 0 0 12px 0;
  color: #4CAF50;
  font-size: 14px;
`;

const DebugSection = styled.div`
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #333;
`;

const DebugLabel = styled.div`
  color: #FFA726;
  font-weight: bold;
  margin-bottom: 4px;
`;

const DebugValue = styled.div`
  color: #E0E0E0;
  word-break: break-all;
`;

const UserDebug = () => {
  const { user, loading } = useAuth();

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <DebugContainer>
      <DebugTitle>🔍 User Debug Info</DebugTitle>
      
      <DebugSection>
        <DebugLabel>Loading:</DebugLabel>
        <DebugValue>{loading ? 'true' : 'false'}</DebugValue>
      </DebugSection>

      <DebugSection>
        <DebugLabel>User Exists:</DebugLabel>
        <DebugValue>{user ? 'true' : 'false'}</DebugValue>
      </DebugSection>

      {user && (
        <>
          <DebugSection>
            <DebugLabel>UID:</DebugLabel>
            <DebugValue>{user.uid || 'undefined'}</DebugValue>
          </DebugSection>

          <DebugSection>
            <DebugLabel>Email:</DebugLabel>
            <DebugValue>{user.email || 'undefined'}</DebugValue>
          </DebugSection>

          <DebugSection>
            <DebugLabel>Display Name:</DebugLabel>
            <DebugValue>{user.displayName || 'undefined'}</DebugValue>
          </DebugSection>

          <DebugSection>
            <DebugLabel>Role:</DebugLabel>
            <DebugValue>{user.role || 'undefined'}</DebugValue>
          </DebugSection>

          <DebugSection>
            <DebugLabel>Profile Completed:</DebugLabel>
            <DebugValue style={{ color: user.profileCompleted ? '#4CAF50' : '#F44336' }}>
              {user.profileCompleted ? 'true' : 'false'}
            </DebugValue>
          </DebugSection>

          <DebugSection>
            <DebugLabel>Email Verified:</DebugLabel>
            <DebugValue>{user.emailVerified ? 'true' : 'false'}</DebugValue>
          </DebugSection>

          <DebugSection>
            <DebugLabel>Created At:</DebugLabel>
            <DebugValue>{user.createdAt || 'undefined'}</DebugValue>
          </DebugSection>

          <DebugSection>
            <DebugLabel>User Code:</DebugLabel>
            <DebugValue>{user.userCode || 'undefined'}</DebugValue>
          </DebugSection>

          <DebugSection>
            <DebugLabel>First Name:</DebugLabel>
            <DebugValue>{user.firstName || 'undefined'}</DebugValue>
          </DebugSection>

          <DebugSection>
            <DebugLabel>Last Name:</DebugLabel>
            <DebugValue>{user.lastName || 'undefined'}</DebugValue>
          </DebugSection>
        </>
      )}
    </DebugContainer>
  );
};

export default UserDebug;
