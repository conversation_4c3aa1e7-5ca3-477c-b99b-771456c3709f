import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Input, Modal } from '../common';
import { formatDate } from '../../utils/dateUtils';

const DoctorsContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const SearchContainer = styled.div`
  display: flex;
  gap: 16px;
  align-items: center;
`;

const DoctorsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
`;

const DoctorCard = styled(Card)`
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.lg};
  }
`;

const DoctorHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
`;

const DoctorAvatar = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #2196F3 0%, #64B5F6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  font-weight: 700;
  box-shadow: ${props => props.theme.shadows.md};
`;

const DoctorInfo = styled.div`
  flex: 1;
`;

const DoctorName = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const DoctorMeta = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  color: ${props => props.theme.colors.textSecondary};
  font-size: 14px;
`;

const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
`;

const StatusDot = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => {
    switch (props.status) {
      case 'available': return '#4CAF50';
      case 'busy': return '#FF9800';
      case 'offline': return '#757575';
      default: return '#757575';
    }
  }};
`;

const StatusText = styled.span`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
`;

const DoctorStats = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin: 20px 0;
`;

const StatItem = styled.div`
  text-align: center;
  padding: 12px;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 8px;
`;

const StatValue = styled.div`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const StatLabel = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
  margin-top: 4px;
`;

const DoctorActions = styled.div`
  display: flex;
  gap: 12px;
  margin-top: 20px;
`;

const NextAppointment = styled.div`
  background: ${props => props.theme.colors.lightGray};
  border-radius: 8px;
  padding: 12px;
  margin: 16px 0;
`;

const AppointmentTitle = styled.h4`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 4px 0;
`;

const AppointmentDate = styled.p`
  font-size: 13px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: ${props => props.theme.shadows.md};
`;

const EmptyIcon = styled.div`
  font-size: 64px;
  margin-bottom: 16px;
`;

const MyDoctorsScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();

  const [doctors, setDoctors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDoctor, setSelectedDoctor] = useState(null);
  const [showDoctorModal, setShowDoctorModal] = useState(false);

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'My Doctors', icon: '👨‍⚕️', path: '/my-doctors' },
    { label: 'Appointments', icon: '📅', path: '/appointments' },
    { label: 'Messages', icon: '💬', path: '/messages' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  useEffect(() => {
    loadDoctors();
  }, []);

  const loadDoctors = async () => {
    try {
      setLoading(true);
      
      // Mock doctors data
      const mockDoctors = [
        {
          id: 'doctor-1',
          name: 'Dr. Sarah Johnson',
          specialization: 'Neurologist',
          experience: '15 years',
          rating: 4.9,
          reviewsCount: 127,
          clinic: 'NeuroCare Medical Center',
          phone: '+33 1 23 45 67 89',
          email: '<EMAIL>',
          status: 'available',
          languages: ['English', 'French'],
          education: 'Harvard Medical School',
          certifications: ['Board Certified Neurologist', 'Alzheimer\'s Specialist'],
          nextAvailableSlot: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
          nextAppointment: {
            date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
            type: 'Follow-up consultation'
          },
          totalConsultations: 45,
          lastConsultation: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          averageResponseTime: '2 hours'
        },
        {
          id: 'doctor-2',
          name: 'Dr. Michel Rousseau',
          specialization: 'Geriatrician',
          experience: '20 years',
          rating: 4.8,
          reviewsCount: 89,
          clinic: 'Senior Care Institute',
          phone: '+33 1 98 76 54 32',
          email: '<EMAIL>',
          status: 'busy',
          languages: ['French', 'Spanish'],
          education: 'Sorbonne University',
          certifications: ['Geriatric Medicine', 'Dementia Care Specialist'],
          nextAvailableSlot: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
          nextAppointment: null,
          totalConsultations: 23,
          lastConsultation: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
          averageResponseTime: '4 hours'
        },
        {
          id: 'doctor-3',
          name: 'Dr. Claire Rousseau',
          specialization: 'Psychiatrist',
          experience: '12 years',
          rating: 4.7,
          reviewsCount: 156,
          clinic: 'Mental Health Center',
          phone: '+33 1 11 22 33 44',
          email: '<EMAIL>',
          status: 'offline',
          languages: ['French', 'English', 'Italian'],
          education: 'University of Lyon',
          certifications: ['Board Certified Psychiatrist', 'Cognitive Behavioral Therapy'],
          nextAvailableSlot: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          nextAppointment: {
            date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000),
            type: 'Therapy session'
          },
          totalConsultations: 12,
          lastConsultation: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          averageResponseTime: '1 hour'
        }
      ];
      
      setDoctors(mockDoctors);
      
    } catch (error) {
      console.error('Error loading doctors:', error);
      toast.error('Failed to load doctors');
    } finally {
      setLoading(false);
    }
  };

  const handleDoctorClick = (doctor) => {
    setSelectedDoctor(doctor);
    setShowDoctorModal(true);
  };

  const handleBookAppointment = (doctor) => {
    navigate(`/appointments/new?doctorId=${doctor.id}`);
  };

  const handleSendMessage = (doctor) => {
    navigate(`/chat/doctor-${doctor.id}`);
  };

  const handleVideoCall = (doctor) => {
    navigate(`/video-call/doctor-${doctor.id}`);
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'available': return 'Available';
      case 'busy': return 'Busy';
      case 'offline': return 'Offline';
      default: return 'Unknown';
    }
  };

  const filteredDoctors = doctors.filter(doctor =>
    doctor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    doctor.specialization.toLowerCase().includes(searchQuery.toLowerCase()) ||
    doctor.clinic.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <DashboardLayout
        title="My Doctors"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <DoctorsContainer>
          <Card theme={theme}>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              Loading doctors...
            </div>
          </Card>
        </DoctorsContainer>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="My Doctors"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <DoctorsContainer>
        <Header>
          <Title theme={theme}>My Healthcare Team</Title>
          <SearchContainer>
            <Input
              placeholder="Search doctors..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{ width: '300px' }}
              icon="🔍"
            />
            <Button
              variant="outline"
              onClick={loadDoctors}
              loading={loading}
              icon="🔄"
            >
              Refresh
            </Button>
          </SearchContainer>
        </Header>

        {filteredDoctors.length === 0 ? (
          <EmptyState theme={theme}>
            <EmptyIcon>👨‍⚕️</EmptyIcon>
            <h3>No doctors found</h3>
            <p>
              {searchQuery 
                ? 'No doctors match your search criteria.'
                : 'You don\'t have any doctors assigned yet.'
              }
            </p>
            <Button 
              variant="primary" 
              onClick={() => navigate('/doctors/find')}
              style={{ marginTop: '16px' }}
            >
              Find Doctors
            </Button>
          </EmptyState>
        ) : (
          <DoctorsGrid>
            {filteredDoctors.map(doctor => (
              <DoctorCard
                key={doctor.id}
                onClick={() => handleDoctorClick(doctor)}
                theme={theme}
              >
                <DoctorHeader>
                  <DoctorAvatar theme={theme}>
                    {doctor.name.split(' ').map(n => n.charAt(0)).join('')}
                  </DoctorAvatar>
                  <DoctorInfo>
                    <DoctorName theme={theme}>{doctor.name}</DoctorName>
                    <DoctorMeta theme={theme}>
                      <span>{doctor.specialization}</span>
                      <span>{doctor.experience} experience</span>
                      <span>⭐ {doctor.rating} ({doctor.reviewsCount} reviews)</span>
                      <span>🏥 {doctor.clinic}</span>
                    </DoctorMeta>
                    <StatusIndicator>
                      <StatusDot status={doctor.status} />
                      <StatusText theme={theme}>
                        {getStatusText(doctor.status)}
                      </StatusText>
                    </StatusIndicator>
                  </DoctorInfo>
                </DoctorHeader>

                <DoctorStats>
                  <StatItem theme={theme}>
                    <StatValue theme={theme}>{doctor.totalConsultations}</StatValue>
                    <StatLabel theme={theme}>Consultations</StatLabel>
                  </StatItem>
                  <StatItem theme={theme}>
                    <StatValue theme={theme}>{doctor.averageResponseTime}</StatValue>
                    <StatLabel theme={theme}>Response Time</StatLabel>
                  </StatItem>
                  <StatItem theme={theme}>
                    <StatValue theme={theme}>{doctor.rating}</StatValue>
                    <StatLabel theme={theme}>Rating</StatLabel>
                  </StatItem>
                </DoctorStats>

                {doctor.nextAppointment && (
                  <NextAppointment theme={theme}>
                    <AppointmentTitle theme={theme}>Next Appointment</AppointmentTitle>
                    <AppointmentDate theme={theme}>
                      {formatDate(doctor.nextAppointment.date, 'datetime')} - {doctor.nextAppointment.type}
                    </AppointmentDate>
                  </NextAppointment>
                )}

                <DoctorActions>
                  <Button
                    variant="primary"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleBookAppointment(doctor);
                    }}
                    icon="📅"
                  >
                    Book Appointment
                  </Button>
                  <Button
                    variant="outline"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSendMessage(doctor);
                    }}
                    icon="💬"
                  >
                    Message
                  </Button>
                  {doctor.status === 'available' && (
                    <Button
                      variant="outline"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleVideoCall(doctor);
                      }}
                      icon="📹"
                    >
                      Video Call
                    </Button>
                  )}
                </DoctorActions>
              </DoctorCard>
            ))}
          </DoctorsGrid>
        )}

        {/* Doctor Details Modal */}
        <Modal
          isOpen={showDoctorModal}
          onClose={() => setShowDoctorModal(false)}
          title="Doctor Details"
          maxWidth="700px"
        >
          {selectedDoctor && (
            <div>
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: '20px', 
                marginBottom: '24px' 
              }}>
                <DoctorAvatar theme={theme}>
                  {selectedDoctor.name.split(' ').map(n => n.charAt(0)).join('')}
                </DoctorAvatar>
                <div>
                  <h3 style={{ margin: '0 0 8px 0', color: theme.colors.text }}>
                    {selectedDoctor.name}
                  </h3>
                  <p style={{ margin: '0', color: theme.colors.textSecondary }}>
                    {selectedDoctor.specialization} • {selectedDoctor.experience}
                  </p>
                </div>
              </div>

              <div style={{ 
                display: 'grid', 
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
                gap: '16px',
                marginBottom: '24px'
              }}>
                <div>
                  <strong>Education:</strong><br />
                  {selectedDoctor.education}
                </div>
                <div>
                  <strong>Languages:</strong><br />
                  {selectedDoctor.languages.join(', ')}
                </div>
                <div>
                  <strong>Phone:</strong><br />
                  {selectedDoctor.phone}
                </div>
                <div>
                  <strong>Email:</strong><br />
                  {selectedDoctor.email}
                </div>
              </div>

              <div style={{ marginBottom: '24px' }}>
                <strong>Certifications:</strong><br />
                {selectedDoctor.certifications.map((cert, index) => (
                  <span key={index} style={{ 
                    display: 'inline-block',
                    background: theme.colors.lightGray,
                    padding: '4px 8px',
                    borderRadius: '4px',
                    margin: '4px 4px 4px 0',
                    fontSize: '12px'
                  }}>
                    {cert}
                  </span>
                ))}
              </div>

              <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
                <Button
                  variant="outline"
                  onClick={() => setShowDoctorModal(false)}
                >
                  Close
                </Button>
                <Button
                  variant="primary"
                  onClick={() => {
                    handleBookAppointment(selectedDoctor);
                    setShowDoctorModal(false);
                  }}
                >
                  Book Appointment
                </Button>
              </div>
            </div>
          )}
        </Modal>
      </DoctorsContainer>
    </DashboardLayout>
  );
};

export default MyDoctorsScreen;
