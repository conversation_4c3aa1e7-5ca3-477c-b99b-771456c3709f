import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { <PERSON>ton, Card, Select } from '../common';
import { formatDate } from '../../utils/dateUtils';
import { 
  VITAL_SIGN_DISPLAY_NAMES, 
  VITAL_SIGN_UNITS,
  VITAL_SIGN_NORMAL_RANGES 
} from '../../utils/constants';

const MonitoringContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const PatientInfo = styled(Card)`
  margin-bottom: 24px;
`;

const PatientHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
`;

const PatientAvatar = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary} 0%, ${props => props.theme.colors.primaryLight} 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  font-weight: 700;
  box-shadow: ${props => props.theme.shadows.md};
`;

const PatientDetails = styled.div`
  flex: 1;
`;

const PatientName = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const PatientMeta = styled.div`
  display: flex;
  gap: 24px;
  color: ${props => props.theme.colors.textSecondary};
  font-size: 14px;
`;

const StatusBadge = styled.span`
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  
  ${props => {
    switch (props.status) {
      case 'stable':
        return `background: #E8F5E8; color: #2E7D32;`;
      case 'attention':
        return `background: #FFF3E0; color: #F57C00;`;
      case 'critical':
        return `background: #FFEBEE; color: #C62828;`;
      default:
        return `background: #F5F5F5; color: #757575;`;
    }
  }}
`;

const VitalsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
`;

const VitalCard = styled(Card)`
  text-align: center;
`;

const VitalIcon = styled.div`
  font-size: 32px;
  margin-bottom: 12px;
`;

const VitalTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const VitalValue = styled.div`
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
  color: ${props => {
    switch (props.status) {
      case 'normal': return '#4CAF50';
      case 'warning': return '#FF9800';
      case 'danger': return '#F44336';
      default: return props.theme.colors.text;
    }
  }};
`;

const VitalUnit = styled.span`
  font-size: 16px;
  font-weight: 400;
  color: ${props => props.theme.colors.textSecondary};
  margin-left: 4px;
`;

const VitalTrend = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
  margin-top: 4px;
`;

const AlertsSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 16px 0;
`;

const AlertsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const AlertItem = styled(Card)`
  padding: 16px;
  border-left: 4px solid ${props => {
    switch (props.priority) {
      case 'high': return '#F44336';
      case 'medium': return '#FF9800';
      case 'low': return '#4CAF50';
      default: return props.theme.colors.border;
    }
  }};
`;

const AlertHeader = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 8px;
`;

const AlertTitle = styled.h4`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0;
  flex: 1;
`;

const AlertTime = styled.span`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
`;

const AlertMessage = styled.p`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
  line-height: 1.4;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 16px;
  margin-top: 24px;
  flex-wrap: wrap;
`;

const PatientHealthMonitoringScreen = () => {
  const { patientId } = useParams();
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();

  const [patient, setPatient] = useState(null);
  const [vitals, setVitals] = useState([]);
  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('24h');

  const roleColors = getRoleColors(user?.role || 'supervisor');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Patients', icon: '👥', path: '/patients' },
    { label: 'Health Monitor', icon: '❤️', path: '/health-monitor' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  useEffect(() => {
    if (patientId) {
      loadPatientData();
    }
  }, [patientId, timeRange]);

  const loadPatientData = async () => {
    try {
      setLoading(true);
      
      // Mock patient data
      const mockPatient = {
        id: patientId,
        name: 'John Smith',
        age: 65,
        condition: 'Alzheimer\'s Disease',
        status: 'stable',
        lastUpdate: new Date(),
        caregiver: 'Marie Dubois',
        emergencyContact: '+33 1 23 45 67 89'
      };

      // Mock vital signs data
      const mockVitals = [
        {
          type: 'blood_pressure_systolic',
          value: 135,
          unit: 'mmHg',
          status: 'warning',
          trend: 'up',
          lastUpdate: new Date(Date.now() - 30 * 60 * 1000)
        },
        {
          type: 'heart_rate',
          value: 78,
          unit: 'bpm',
          status: 'normal',
          trend: 'stable',
          lastUpdate: new Date(Date.now() - 15 * 60 * 1000)
        },
        {
          type: 'temperature',
          value: 36.8,
          unit: '°C',
          status: 'normal',
          trend: 'stable',
          lastUpdate: new Date(Date.now() - 45 * 60 * 1000)
        },
        {
          type: 'oxygen_saturation',
          value: 97,
          unit: '%',
          status: 'normal',
          trend: 'stable',
          lastUpdate: new Date(Date.now() - 20 * 60 * 1000)
        }
      ];

      // Mock alerts
      const mockAlerts = [
        {
          id: '1',
          priority: 'medium',
          title: 'Blood Pressure Elevated',
          message: 'Patient\'s blood pressure has been consistently above normal range for the past 2 hours.',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          resolved: false
        },
        {
          id: '2',
          priority: 'low',
          title: 'Medication Reminder',
          message: 'Patient has not taken evening medication yet.',
          timestamp: new Date(Date.now() - 30 * 60 * 1000),
          resolved: false
        }
      ];

      setPatient(mockPatient);
      setVitals(mockVitals);
      setAlerts(mockAlerts);
      
    } catch (error) {
      console.error('Error loading patient data:', error);
      toast.error('Failed to load patient data');
    } finally {
      setLoading(false);
    }
  };

  const getVitalIcon = (type) => {
    switch (type) {
      case 'blood_pressure_systolic': return '🩸';
      case 'heart_rate': return '❤️';
      case 'temperature': return '🌡️';
      case 'oxygen_saturation': return '🫁';
      default: return '📊';
    }
  };

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up': return '📈';
      case 'down': return '📉';
      case 'stable': return '➡️';
      default: return '➡️';
    }
  };

  const handleEmergencyCall = () => {
    navigate(`/video-call/emergency-${patientId}`);
  };

  const handleContactCaregiver = () => {
    toast.success('Caregiver has been notified');
  };

  const handleViewHistory = () => {
    navigate(`/patients/${patientId}/history`);
  };

  if (loading) {
    return (
      <DashboardLayout
        title="Health Monitoring"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <MonitoringContainer>
          <Card theme={theme}>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              Loading patient data...
            </div>
          </Card>
        </MonitoringContainer>
      </DashboardLayout>
    );
  }

  if (!patient) {
    return (
      <DashboardLayout
        title="Health Monitoring"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <MonitoringContainer>
          <Card theme={theme}>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              Patient not found
            </div>
          </Card>
        </MonitoringContainer>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Health Monitoring"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <MonitoringContainer>
        <Header>
          <Title theme={theme}>Patient Health Monitoring</Title>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            options={[
              { value: '1h', label: 'Last Hour' },
              { value: '24h', label: 'Last 24 Hours' },
              { value: '7d', label: 'Last 7 Days' },
              { value: '30d', label: 'Last 30 Days' }
            ]}
          />
        </Header>

        <PatientInfo theme={theme}>
          <PatientHeader>
            <PatientAvatar theme={theme}>
              {patient.name.charAt(0)}
            </PatientAvatar>
            <PatientDetails>
              <PatientName theme={theme}>{patient.name}</PatientName>
              <PatientMeta theme={theme}>
                <span>Age: {patient.age}</span>
                <span>Condition: {patient.condition}</span>
                <span>Caregiver: {patient.caregiver}</span>
              </PatientMeta>
            </PatientDetails>
            <StatusBadge status={patient.status}>
              {patient.status}
            </StatusBadge>
          </PatientHeader>

          <ActionButtons>
            <Button
              variant="danger"
              onClick={handleEmergencyCall}
              icon="🚨"
            >
              Emergency Call
            </Button>
            <Button
              variant="outline"
              onClick={handleContactCaregiver}
              icon="👨‍⚕️"
            >
              Contact Caregiver
            </Button>
            <Button
              variant="outline"
              onClick={handleViewHistory}
              icon="📊"
            >
              View History
            </Button>
          </ActionButtons>
        </PatientInfo>

        <VitalsGrid>
          {vitals.map((vital, index) => (
            <VitalCard key={index} theme={theme}>
              <VitalIcon>{getVitalIcon(vital.type)}</VitalIcon>
              <VitalTitle theme={theme}>
                {VITAL_SIGN_DISPLAY_NAMES[vital.type] || vital.type}
              </VitalTitle>
              <VitalValue status={vital.status} theme={theme}>
                {vital.value}
                <VitalUnit theme={theme}>{vital.unit}</VitalUnit>
              </VitalValue>
              <VitalTrend theme={theme}>
                {getTrendIcon(vital.trend)} Last updated: {formatDate(vital.lastUpdate, 'time')}
              </VitalTrend>
            </VitalCard>
          ))}
        </VitalsGrid>

        <AlertsSection>
          <SectionTitle theme={theme}>Recent Alerts</SectionTitle>
          <AlertsList>
            {alerts.map(alert => (
              <AlertItem key={alert.id} priority={alert.priority} theme={theme}>
                <AlertHeader>
                  <AlertTitle theme={theme}>{alert.title}</AlertTitle>
                  <AlertTime theme={theme}>
                    {formatDate(alert.timestamp, 'datetime')}
                  </AlertTime>
                </AlertHeader>
                <AlertMessage theme={theme}>
                  {alert.message}
                </AlertMessage>
              </AlertItem>
            ))}
          </AlertsList>
        </AlertsSection>
      </MonitoringContainer>
    </DashboardLayout>
  );
};

export default PatientHealthMonitoringScreen;
