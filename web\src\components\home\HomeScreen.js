import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { useTheme } from '../../contexts/ThemeContext';

const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary} 0%, ${props => props.theme.colors.primaryLight} 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }
`;

const ContentWrapper = styled.div`
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 600px;
  width: 100%;
`;

const Logo = styled.div`
  margin-bottom: 32px;
`;

const LogoIcon = styled.div`
  width: 80px;
  height: 80px;
  background-color: white;
  border-radius: 20px;
  margin: 0 auto 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  
  &::before {
    content: '🧠';
    font-size: 40px;
  }
`;

const Title = styled.h1`
  font-size: 48px;
  font-weight: 700;
  color: white;
  margin: 0 0 16px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  @media (max-width: 768px) {
    font-size: 36px;
  }
`;

const Subtitle = styled.p`
  font-size: 20px;
  color: white;
  margin: 0 0 48px 0;
  opacity: 0.9;
  line-height: 1.6;

  @media (max-width: 768px) {
    font-size: 18px;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 32px;

  @media (min-width: 480px) {
    flex-direction: row;
    justify-content: center;
  }
`;

const Button = styled(Link)`
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
  min-width: 140px;
  
  ${props => props.variant === 'primary' ? `
    background-color: white;
    color: ${props.theme.colors.primary};
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }
  ` : `
    background-color: transparent;
    color: white;
    border: 2px solid white;
    
    &:hover {
      background-color: white;
      color: ${props.theme.colors.primary};
    }
  `}
`;

const FeaturesList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-top: 48px;
`;

const FeatureCard = styled.div`
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const FeatureIcon = styled.div`
  font-size: 32px;
  margin-bottom: 16px;
`;

const FeatureTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin: 0 0 8px 0;
`;

const FeatureDescription = styled.p`
  font-size: 14px;
  color: white;
  opacity: 0.8;
  margin: 0;
  line-height: 1.5;
`;

const HomeScreen = () => {
  const { theme } = useTheme();

  const features = [
    {
      icon: '👨‍⚕️',
      title: 'Multi-Role Dashboard',
      description: 'Specialized interfaces for patients, doctors, caregivers, and supervisors'
    },
    {
      icon: '📊',
      title: 'Health Monitoring',
      description: 'Track vital signs, medications, and health progress in real-time'
    },
    {
      icon: '💬',
      title: 'Secure Communication',
      description: 'HIPAA-compliant messaging and video consultations'
    },
    {
      icon: '📱',
      title: 'Mobile & Web',
      description: 'Access your healthcare data anywhere, anytime'
    }
  ];

  return (
    <Container theme={theme}>
      <ContentWrapper>
        <Logo>
          <LogoIcon />
          <Title>NeuroCare</Title>
          <Subtitle>
            Comprehensive Healthcare Management System
            <br />
            Connecting patients, doctors, and caregivers
          </Subtitle>
        </Logo>

        <ButtonGroup>
          <Button to="/login" variant="primary" theme={theme}>
            Sign In
          </Button>
          <Button to="/signup" variant="secondary" theme={theme}>
            Get Started
          </Button>
        </ButtonGroup>

        <FeaturesList>
          {features.map((feature, index) => (
            <FeatureCard key={index}>
              <FeatureIcon>{feature.icon}</FeatureIcon>
              <FeatureTitle>{feature.title}</FeatureTitle>
              <FeatureDescription>{feature.description}</FeatureDescription>
            </FeatureCard>
          ))}
        </FeaturesList>
      </ContentWrapper>
    </Container>
  );
};

export default HomeScreen;
