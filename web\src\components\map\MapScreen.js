import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Input, Select } from '../common';

const MapContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const MapControls = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
  align-items: center;
`;

const MapCard = styled(Card)`
  height: 600px;
  padding: 0;
  overflow: hidden;
`;

const MapArea = styled.div`
  width: 100%;
  height: 100%;
  background: ${props => props.theme.colors.lightGray};
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const MapPlaceholder = styled.div`
  text-align: center;
  color: ${props => props.theme.colors.textSecondary};
`;

const MapIcon = styled.div`
  font-size: 64px;
  margin-bottom: 16px;
`;

const PatientsList = styled.div`
  position: absolute;
  top: 20px;
  left: 20px;
  width: 300px;
  background: white;
  border-radius: 12px;
  box-shadow: ${props => props.theme.shadows.lg};
  max-height: 400px;
  overflow-y: auto;
  z-index: 10;
`;

const PatientsHeader = styled.div`
  padding: 16px;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.primary};
  color: white;
  border-radius: 12px 12px 0 0;
`;

const PatientsTitle = styled.h3`
  margin: 0;
  font-size: 16px;
  font-weight: 600;
`;

const PatientItem = styled.div`
  padding: 12px 16px;
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
  cursor: pointer;
  transition: background-color 0.3s ease;
  
  &:hover {
    background: ${props => props.theme.colors.lightGray};
  }
  
  &:last-child {
    border-bottom: none;
    border-radius: 0 0 12px 12px;
  }
`;

const PatientInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const PatientAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: ${props => props.color || props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
`;

const PatientDetails = styled.div`
  flex: 1;
`;

const PatientName = styled.div`
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  font-size: 14px;
`;

const PatientStatus = styled.div`
  font-size: 12px;
  color: ${props => {
    switch (props.status) {
      case 'online': return '#4CAF50';
      case 'offline': return '#757575';
      case 'emergency': return '#F44336';
      default: return props.theme.colors.textSecondary;
    }
  }};
  margin-top: 2px;
`;

const LocationInfo = styled.div`
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: ${props => props.theme.shadows.lg};
  padding: 20px;
  z-index: 10;
`;

const LocationTitle = styled.h3`
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const LocationActions = styled.div`
  display: flex;
  gap: 12px;
  margin-top: 16px;
  flex-wrap: wrap;
`;

const MapScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();

  const [selectedPatient, setSelectedPatient] = useState(null);
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [mapView, setMapView] = useState('overview');
  const [searchQuery, setSearchQuery] = useState('');

  const roleColors = getRoleColors(user?.role || 'supervisor');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Map', icon: '🗺️', path: '/map' },
    { label: 'Patients', icon: '👥', path: '/patients' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  useEffect(() => {
    loadPatients();
  }, []);

  const loadPatients = async () => {
    try {
      setLoading(true);
      
      // Mock patient data with locations
      const mockPatients = [
        {
          id: 'patient-1',
          name: 'John Smith',
          status: 'online',
          location: {
            lat: 48.8566,
            lng: 2.3522,
            address: '123 Main St, Paris, France',
            lastUpdate: new Date(Date.now() - 5 * 60 * 1000) // 5 minutes ago
          },
          emergency: false
        },
        {
          id: 'patient-2',
          name: 'Marie Dubois',
          status: 'offline',
          location: {
            lat: 48.8606,
            lng: 2.3376,
            address: '456 Avenue des Champs, Paris, France',
            lastUpdate: new Date(Date.now() - 30 * 60 * 1000) // 30 minutes ago
          },
          emergency: false
        },
        {
          id: 'patient-3',
          name: 'Pierre Martin',
          status: 'emergency',
          location: {
            lat: 48.8534,
            lng: 2.3488,
            address: '789 Rue de la Paix, Paris, France',
            lastUpdate: new Date(Date.now() - 2 * 60 * 1000) // 2 minutes ago
          },
          emergency: true
        }
      ];
      
      setPatients(mockPatients);
      
    } catch (error) {
      console.error('Error loading patients:', error);
      toast.error('Failed to load patient locations');
    } finally {
      setLoading(false);
    }
  };

  const handlePatientSelect = (patient) => {
    setSelectedPatient(patient);
  };

  const handleSendRoute = () => {
    if (!selectedPatient) return;
    
    toast.success(`Route sent to ${selectedPatient.name}`);
  };

  const handleEmergencyAlert = () => {
    if (!selectedPatient) return;
    
    toast.success(`Emergency alert sent for ${selectedPatient.name}`);
  };

  const handleCallPatient = () => {
    if (!selectedPatient) return;
    
    // Navigate to video call
    navigate(`/video-call/emergency-${selectedPatient.id}`);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'online': return '🟢';
      case 'offline': return '⚫';
      case 'emergency': return '🔴';
      default: return '⚪';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'online': return 'Online';
      case 'offline': return 'Offline';
      case 'emergency': return 'Emergency';
      default: return 'Unknown';
    }
  };

  const filteredPatients = patients.filter(patient =>
    patient.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <DashboardLayout
      title="Patient Tracking Map"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <MapContainer>
        <MapControls>
          <Input
            placeholder="Search patients..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            style={{ width: '300px' }}
            icon="🔍"
          />
          
          <Select
            value={mapView}
            onChange={setMapView}
            options={[
              { value: 'overview', label: 'Overview' },
              { value: 'emergency', label: 'Emergency Only' },
              { value: 'online', label: 'Online Only' }
            ]}
            style={{ width: '200px' }}
          />
          
          <Button
            variant="outline"
            onClick={loadPatients}
            loading={loading}
            icon="🔄"
          >
            Refresh
          </Button>
        </MapControls>

        <MapCard theme={theme}>
          <MapArea theme={theme}>
            <MapPlaceholder theme={theme}>
              <MapIcon>🗺️</MapIcon>
              <h3>Interactive Map</h3>
              <p>Real-time patient location tracking would be displayed here.</p>
              <p style={{ fontSize: '14px', marginTop: '16px' }}>
                In a production environment, this would integrate with:
                <br />• Google Maps or Mapbox
                <br />• Real-time GPS tracking
                <br />• Geofencing alerts
                <br />• Route optimization
              </p>
            </MapPlaceholder>

            {/* Patients List Overlay */}
            <PatientsList theme={theme}>
              <PatientsHeader theme={theme}>
                <PatientsTitle>Patients ({filteredPatients.length})</PatientsTitle>
              </PatientsHeader>
              
              {filteredPatients.map(patient => (
                <PatientItem
                  key={patient.id}
                  onClick={() => handlePatientSelect(patient)}
                  theme={theme}
                >
                  <PatientInfo>
                    <PatientAvatar
                      color={patient.emergency ? '#F44336' : roleColors.primary}
                      theme={theme}
                    >
                      {patient.name.charAt(0)}
                    </PatientAvatar>
                    <PatientDetails>
                      <PatientName theme={theme}>
                        {getStatusIcon(patient.status)} {patient.name}
                      </PatientName>
                      <PatientStatus status={patient.status} theme={theme}>
                        {getStatusText(patient.status)} • {
                          patient.location.lastUpdate.toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit'
                          })
                        }
                      </PatientStatus>
                    </PatientDetails>
                  </PatientInfo>
                </PatientItem>
              ))}
            </PatientsList>

            {/* Selected Patient Info */}
            {selectedPatient && (
              <LocationInfo theme={theme}>
                <LocationTitle theme={theme}>
                  {selectedPatient.name} - Current Location
                </LocationTitle>
                
                <div style={{ 
                  fontSize: '14px', 
                  color: theme.colors.textSecondary,
                  marginBottom: '8px'
                }}>
                  📍 {selectedPatient.location.address}
                </div>
                
                <div style={{ 
                  fontSize: '12px', 
                  color: theme.colors.textSecondary 
                }}>
                  Last updated: {selectedPatient.location.lastUpdate.toLocaleString()}
                </div>

                <LocationActions>
                  <Button
                    variant="primary"
                    size="small"
                    onClick={handleSendRoute}
                    icon="🧭"
                  >
                    Send Route
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="small"
                    onClick={handleCallPatient}
                    icon="📞"
                  >
                    Call Patient
                  </Button>
                  
                  {selectedPatient.emergency && (
                    <Button
                      variant="danger"
                      size="small"
                      onClick={handleEmergencyAlert}
                      icon="🚨"
                    >
                      Emergency Alert
                    </Button>
                  )}
                </LocationActions>
              </LocationInfo>
            )}
          </MapArea>
        </MapCard>
      </MapContainer>
    </DashboardLayout>
  );
};

export default MapScreen;
