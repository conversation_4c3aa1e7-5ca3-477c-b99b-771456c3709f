import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { But<PERSON>, Card, Input } from '../common';
import { formatDate } from '../../utils/dateUtils';

const MessagesContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const SearchContainer = styled.div`
  display: flex;
  gap: 16px;
  align-items: center;
`;

const ConversationsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const ConversationCard = styled(Card)`
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid ${props => props.unread ? props.theme.colors.primary : 'transparent'};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.lg};
  }
`;

const ConversationHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
`;

const ParticipantAvatars = styled.div`
  display: flex;
  gap: -8px;
`;

const Avatar = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: ${props => props.color || props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 18px;
  border: 3px solid white;
  box-shadow: ${props => props.theme.shadows.sm};
  
  &:not(:first-child) {
    margin-left: -8px;
  }
`;

const ConversationInfo = styled.div`
  flex: 1;
`;

const ConversationTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 4px 0;
`;

const ConversationMeta = styled.div`
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
`;

const ConversationPreview = styled.div`
  margin-bottom: 12px;
`;

const LastMessage = styled.p`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const ConversationFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const LastMessageTime = styled.span`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
`;

const UnreadBadge = styled.span`
  background: ${props => props.theme.colors.primary};
  color: white;
  border-radius: 12px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
`;

const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
`;

const StatusDot = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => {
    switch (props.status) {
      case 'online': return '#4CAF50';
      case 'away': return '#FF9800';
      case 'offline': return '#757575';
      default: return '#757575';
    }
  }};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: ${props => props.theme.shadows.md};
`;

const EmptyIcon = styled.div`
  font-size: 64px;
  margin-bottom: 16px;
`;

const MessagesScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();

  const [conversations, setConversations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Messages', icon: '💬', path: '/messages' },
    { label: 'Appointments', icon: '📅', path: '/appointments' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  useEffect(() => {
    loadConversations();
  }, []);

  const loadConversations = async () => {
    try {
      setLoading(true);
      
      // Mock conversations data
      const mockConversations = [
        {
          id: 'conv-1',
          type: 'medical_consultation',
          title: 'Medical Consultation',
          participants: [
            {
              id: 'doctor-1',
              name: 'Dr. Sarah Johnson',
              role: 'doctor',
              avatar: 'SJ',
              status: 'online'
            },
            {
              id: user.uid,
              name: user.displayName,
              role: user.role,
              avatar: user.displayName?.charAt(0) || 'U',
              status: 'online'
            }
          ],
          lastMessage: {
            text: 'Thank you for the consultation. Please remember to take your medication as prescribed.',
            senderId: 'doctor-1',
            senderName: 'Dr. Sarah Johnson',
            timestamp: new Date(Date.now() - 30 * 60 * 1000)
          },
          unreadCount: 2,
          updatedAt: new Date(Date.now() - 30 * 60 * 1000)
        },
        {
          id: 'conv-2',
          type: 'caregiver_support',
          title: 'Caregiver Support',
          participants: [
            {
              id: 'caregiver-1',
              name: 'Marie Dubois',
              role: 'caregiver',
              avatar: 'MD',
              status: 'away'
            },
            {
              id: user.uid,
              name: user.displayName,
              role: user.role,
              avatar: user.displayName?.charAt(0) || 'U',
              status: 'online'
            }
          ],
          lastMessage: {
            text: 'I\'ve completed the morning activities. Everything went well today.',
            senderId: 'caregiver-1',
            senderName: 'Marie Dubois',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
          },
          unreadCount: 0,
          updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
        },
        {
          id: 'conv-3',
          type: 'family_group',
          title: 'Family Updates',
          participants: [
            {
              id: 'family-1',
              name: 'Jean Martin',
              role: 'family',
              avatar: 'JM',
              status: 'offline'
            },
            {
              id: 'family-2',
              name: 'Claire Martin',
              role: 'family',
              avatar: 'CM',
              status: 'online'
            },
            {
              id: user.uid,
              name: user.displayName,
              role: user.role,
              avatar: user.displayName?.charAt(0) || 'U',
              status: 'online'
            }
          ],
          lastMessage: {
            text: 'How was the appointment today? We\'re thinking of you.',
            senderId: 'family-2',
            senderName: 'Claire Martin',
            timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000)
          },
          unreadCount: 1,
          updatedAt: new Date(Date.now() - 4 * 60 * 60 * 1000)
        },
        {
          id: 'conv-4',
          type: 'emergency_contact',
          title: 'Emergency Support',
          participants: [
            {
              id: 'supervisor-1',
              name: 'Dr. Michel Rousseau',
              role: 'supervisor',
              avatar: 'MR',
              status: 'online'
            },
            {
              id: user.uid,
              name: user.displayName,
              role: user.role,
              avatar: user.displayName?.charAt(0) || 'U',
              status: 'online'
            }
          ],
          lastMessage: {
            text: 'Emergency protocol has been updated. Please review the new procedures.',
            senderId: 'supervisor-1',
            senderName: 'Dr. Michel Rousseau',
            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000)
          },
          unreadCount: 0,
          updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000)
        }
      ];
      
      setConversations(mockConversations);
      
    } catch (error) {
      console.error('Error loading conversations:', error);
      toast.error('Failed to load conversations');
    } finally {
      setLoading(false);
    }
  };

  const handleConversationClick = (conversation) => {
    navigate(`/chat/${conversation.id}`);
  };

  const handleNewMessage = () => {
    navigate('/chat/new');
  };

  const getRoleColor = (role) => {
    const colors = {
      doctor: '#2196F3',
      caregiver: '#FF9800',
      supervisor: '#9C27B0',
      patient: '#4CAF50',
      family: '#607D8B'
    };
    return colors[role] || theme.colors.primary;
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'online': return 'Online';
      case 'away': return 'Away';
      case 'offline': return 'Offline';
      default: return 'Unknown';
    }
  };

  const filteredConversations = conversations.filter(conversation =>
    conversation.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conversation.participants.some(p => 
      p.name.toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  if (loading) {
    return (
      <DashboardLayout
        title="Messages"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <MessagesContainer>
          <Card theme={theme}>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              Loading conversations...
            </div>
          </Card>
        </MessagesContainer>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Messages"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <MessagesContainer>
        <Header>
          <Title theme={theme}>Messages</Title>
          <SearchContainer>
            <Input
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{ width: '300px' }}
              icon="🔍"
            />
            <Button
              variant="primary"
              onClick={handleNewMessage}
              icon="+"
            >
              New Message
            </Button>
          </SearchContainer>
        </Header>

        {filteredConversations.length === 0 ? (
          <EmptyState theme={theme}>
            <EmptyIcon>💬</EmptyIcon>
            <h3>No conversations found</h3>
            <p>
              {searchQuery 
                ? 'No conversations match your search.'
                : 'You don\'t have any conversations yet.'
              }
            </p>
            <Button 
              variant="primary" 
              onClick={handleNewMessage}
              style={{ marginTop: '16px' }}
            >
              Start New Conversation
            </Button>
          </EmptyState>
        ) : (
          <ConversationsList>
            {filteredConversations.map(conversation => (
              <ConversationCard
                key={conversation.id}
                onClick={() => handleConversationClick(conversation)}
                unread={conversation.unreadCount > 0}
                theme={theme}
              >
                <ConversationHeader>
                  <ParticipantAvatars>
                    {conversation.participants
                      .filter(p => p.id !== user.uid)
                      .slice(0, 2)
                      .map(participant => (
                        <Avatar
                          key={participant.id}
                          color={getRoleColor(participant.role)}
                          theme={theme}
                        >
                          {participant.avatar}
                        </Avatar>
                      ))}
                  </ParticipantAvatars>
                  <ConversationInfo>
                    <ConversationTitle theme={theme}>
                      {conversation.title}
                    </ConversationTitle>
                    <ConversationMeta theme={theme}>
                      <span>
                        {conversation.participants
                          .filter(p => p.id !== user.uid)
                          .map(p => p.name)
                          .join(', ')
                        }
                      </span>
                      <StatusIndicator theme={theme}>
                        <StatusDot status={conversation.participants.find(p => p.id !== user.uid)?.status} />
                        {getStatusText(conversation.participants.find(p => p.id !== user.uid)?.status)}
                      </StatusIndicator>
                    </ConversationMeta>
                  </ConversationInfo>
                </ConversationHeader>

                <ConversationPreview>
                  <LastMessage theme={theme}>
                    <strong>{conversation.lastMessage.senderName}:</strong> {conversation.lastMessage.text}
                  </LastMessage>
                </ConversationPreview>

                <ConversationFooter>
                  <LastMessageTime theme={theme}>
                    {formatDate(conversation.lastMessage.timestamp, 'datetime')}
                  </LastMessageTime>
                  {conversation.unreadCount > 0 && (
                    <UnreadBadge theme={theme}>
                      {conversation.unreadCount}
                    </UnreadBadge>
                  )}
                </ConversationFooter>
              </ConversationCard>
            ))}
          </ConversationsList>
        )}
      </MessagesContainer>
    </DashboardLayout>
  );
};

export default MessagesScreen;
