import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';

// Auth Components
import Login from '../auth/Login';
import SignUp from '../auth/SignUp';
import ForgotPassword from '../auth/ForgotPassword';
import HomeScreen from '../home/<USER>';

// Dashboard Components
import Dashboard from '../dashboards/Dashboard';

// Profile Components
import ProfileScreen from '../profile/ProfileScreen';
import ProfileCompletion from '../profile/ProfileCompletion';
import UserQRCodeScreen from '../profile/UserQRCodeScreen';

// Other Screens
import NotificationsScreen from '../notifications/NotificationsScreen';
import SettingsScreen from '../settings/SettingsScreen';
import PrescriptionsScreen from '../prescriptions/PrescriptionsScreen';
import AppointmentsScreen from '../appointments/AppointmentsScreen';
import NewAppointmentScreen from '../appointments/NewAppointmentScreen';
import AppointmentDetailsScreen from '../appointments/AppointmentDetailsScreen';
import VitalsScreen from '../vitals/VitalsScreen';
import RecordVitalsScreen from '../vitals/RecordVitalsScreen';
import ChatScreen from '../chat/ChatScreen';
import VideoCallScreen from '../video/VideoCallScreen';
import QRScannerScreen from '../scanner/QRScannerScreen';
import MapScreen from '../map/MapScreen';
import PatientHealthMonitoringScreen from '../health/PatientHealthMonitoringScreen';
import CaregiverPatientsScreen from '../caregiver/CaregiverPatientsScreen';
import CaregiverPatientDetailScreen from '../caregiver/CaregiverPatientDetailScreen';
import CaregiverRecordActivityScreen from '../caregiver/CaregiverRecordActivityScreen';
import SupervisorCaregiversScreen from '../supervisor/SupervisorCaregiversScreen';
import AddPatientScreen from '../patients/AddPatientScreen';
import NewPrescriptionScreen from '../prescriptions/NewPrescriptionScreen';
import MessagesScreen from '../messages/MessagesScreen';
import PatientConsultationScreen from '../patient/PatientConsultationScreen';
import PatientGuidanceScreen from '../patient/PatientGuidanceScreen';
import ChangePasswordScreen from '../settings/ChangePasswordScreen';
import SupervisorAppointmentsScreen from '../supervisor/SupervisorAppointmentsScreen';
import SupervisorAssignCaregiverScreen from '../supervisor/SupervisorAssignCaregiverScreen';
import MyDoctorsScreen from '../doctor/MyDoctorsScreen';
import VideoCallComponent from '../video/VideoCallComponent';
import ChatRoomScreen from '../chat/ChatRoomScreen';

// Loading Component
import LoadingSpinner from '../common/LoadingSpinner';

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  return children;
};

// Public Route Component (redirect to dashboard if already logged in)
const PublicRoute = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return <LoadingSpinner />;
  }

  if (user) {
    return <Navigate to="/dashboard" replace />;
  }

  return children;
};

const AppRouter = () => {
  const { user, loading } = useAuth();
  const { updateThemeForRole } = useTheme();

  // Update theme when user role changes
  React.useEffect(() => {
    if (user && user.role) {
      updateThemeForRole(user.role);
    }
  }, [user, updateThemeForRole]);

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <Routes>
      {/* Public Routes */}
      <Route
        path="/"
        element={
          <PublicRoute>
            <HomeScreen />
          </PublicRoute>
        }
      />
      <Route
        path="/login"
        element={
          <PublicRoute>
            <Login />
          </PublicRoute>
        }
      />
      <Route
        path="/signup"
        element={
          <PublicRoute>
            <SignUp />
          </PublicRoute>
        }
      />
      <Route
        path="/forgot-password"
        element={
          <PublicRoute>
            <ForgotPassword />
          </PublicRoute>
        }
      />

      {/* Protected Routes */}
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        }
      />
      <Route
        path="/profile"
        element={
          <ProtectedRoute>
            <ProfileScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/profile-completion"
        element={
          <ProtectedRoute>
            <ProfileCompletion />
          </ProtectedRoute>
        }
      />
      <Route
        path="/qr-code"
        element={
          <ProtectedRoute>
            <UserQRCodeScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/notifications"
        element={
          <ProtectedRoute>
            <NotificationsScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/settings"
        element={
          <ProtectedRoute>
            <SettingsScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/prescriptions"
        element={
          <ProtectedRoute>
            <PrescriptionsScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/appointments"
        element={
          <ProtectedRoute>
            <AppointmentsScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/appointments/new"
        element={
          <ProtectedRoute>
            <NewAppointmentScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/appointments/:appointmentId"
        element={
          <ProtectedRoute>
            <AppointmentDetailsScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/vitals"
        element={
          <ProtectedRoute>
            <VitalsScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/vitals/record"
        element={
          <ProtectedRoute>
            <RecordVitalsScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/chat"
        element={
          <ProtectedRoute>
            <ChatScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/video-call"
        element={
          <ProtectedRoute>
            <VideoCallScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/video-call/:appointmentId"
        element={
          <ProtectedRoute>
            <VideoCallScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/scanner"
        element={
          <ProtectedRoute>
            <QRScannerScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/map"
        element={
          <ProtectedRoute>
            <MapScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/health-monitor/:patientId"
        element={
          <ProtectedRoute>
            <PatientHealthMonitoringScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/patients/:patientId/health"
        element={
          <ProtectedRoute>
            <PatientHealthMonitoringScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/patients/add"
        element={
          <ProtectedRoute>
            <AddPatientScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/prescriptions/new"
        element={
          <ProtectedRoute>
            <NewPrescriptionScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/caregiver/patients"
        element={
          <ProtectedRoute>
            <CaregiverPatientsScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/supervisor/caregivers"
        element={
          <ProtectedRoute>
            <SupervisorCaregiversScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/caregiver/patients/:patientId"
        element={
          <ProtectedRoute>
            <CaregiverPatientDetailScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/caregiver/patients/:patientId/record-activity"
        element={
          <ProtectedRoute>
            <CaregiverRecordActivityScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/messages"
        element={
          <ProtectedRoute>
            <MessagesScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/consultation/:consultationId"
        element={
          <ProtectedRoute>
            <PatientConsultationScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/settings/change-password"
        element={
          <ProtectedRoute>
            <ChangePasswordScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/patient/guidance/:destinationId"
        element={
          <ProtectedRoute>
            <PatientGuidanceScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/supervisor/appointments"
        element={
          <ProtectedRoute>
            <SupervisorAppointmentsScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/supervisor/assign-caregiver/:patientId?"
        element={
          <ProtectedRoute>
            <SupervisorAssignCaregiverScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/my-doctors"
        element={
          <ProtectedRoute>
            <MyDoctorsScreen />
          </ProtectedRoute>
        }
      />
      <Route
        path="/video-call-component/:callId"
        element={
          <ProtectedRoute>
            <VideoCallComponent />
          </ProtectedRoute>
        }
      />
      <Route
        path="/chat/:conversationId"
        element={
          <ProtectedRoute>
            <ChatRoomScreen />
          </ProtectedRoute>
        }
      />

      {/* Catch all route - redirect to dashboard if logged in, otherwise to home */}
      <Route
        path="*"
        element={
          user ? <Navigate to="/dashboard" replace /> : <Navigate to="/" replace />
        }
      />
    </Routes>
  );
};

export default AppRouter;
