import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { <PERSON>ton, Card, Select, Modal } from '../common';
import { formatDate } from '../../utils/dateUtils';
import { NOTIFICATION_TYPES } from '../../config/constants';

const NotificationsContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const NotificationsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const Title = styled.h1`
  font-size: 28px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const FilterButtons = styled.div`
  display: flex;
  gap: 8px;
`;

const FilterButton = styled.button`
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid ${props => props.theme.colors.border};

  ${props => props.active ? `
    background: ${props.theme.colors.primary};
    color: white;
    border-color: ${props.theme.colors.primary};
  ` : `
    background: white;
    color: ${props.theme.colors.textSecondary};

    &:hover {
      background: ${props.theme.colors.lightGray};
    }
  `}
`;

const NotificationsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const NotificationCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: ${props => props.theme.shadows.sm};
  border-left: 4px solid ${props => {
    switch (props.type) {
      case 'appointment': return '#2196F3';
      case 'medication': return '#4CAF50';
      case 'alert': return '#FF9800';
      case 'system': return '#9C27B0';
      default: return props.theme.colors.primary;
    }
  }};
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.md};
  }

  ${props => !props.read && `
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.05) 0%, rgba(76, 175, 80, 0.02) 100%);
  `}
`;

const NotificationHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
`;

const NotificationIcon = styled.div`
  font-size: 24px;
  margin-right: 12px;
`;

const NotificationContent = styled.div`
  flex: 1;
`;

const NotificationTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 4px 0;
`;

const NotificationMessage = styled.p`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0 0 8px 0;
  line-height: 1.5;
`;

const NotificationMeta = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
`;

const NotificationTime = styled.span`
  font-weight: 500;
`;

const NotificationBadge = styled.span`
  background: ${props => props.theme.colors.primary};
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: ${props => props.theme.colors.textSecondary};
`;

const EmptyIcon = styled.div`
  font-size: 64px;
  margin-bottom: 16px;
`;

const EmptyTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
`;

const EmptyMessage = styled.p`
  font-size: 14px;
  margin: 0;
`;

const NotificationsScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const [filter, setFilter] = useState('all');

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', screen: 'dashboard' },
    { label: 'Notifications', icon: '🔔', screen: 'notifications' },
    { label: 'Profile', icon: '👤', screen: 'profile' },
    { label: 'Settings', icon: '⚙️', screen: 'settings' }
  ];

  // Mock notifications data
  const notifications = [
    {
      id: 1,
      type: 'appointment',
      icon: '📅',
      title: 'Upcoming Appointment',
      message: 'You have an appointment with Dr. Smith tomorrow at 2:00 PM',
      time: '2 hours ago',
      read: false
    },
    {
      id: 2,
      type: 'medication',
      icon: '💊',
      title: 'Medication Reminder',
      message: 'Time to take your evening medication - Metformin 500mg',
      time: '4 hours ago',
      read: false
    },
    {
      id: 3,
      type: 'alert',
      icon: '⚠️',
      title: 'Vitals Alert',
      message: 'Your blood pressure reading was higher than normal. Please consult your doctor.',
      time: '1 day ago',
      read: true
    },
    {
      id: 4,
      type: 'system',
      icon: '🔔',
      title: 'Profile Updated',
      message: 'Your profile information has been successfully updated',
      time: '2 days ago',
      read: true
    },
    {
      id: 5,
      type: 'appointment',
      icon: '📅',
      title: 'Appointment Confirmed',
      message: 'Your appointment with Dr. Johnson has been confirmed for next week',
      time: '3 days ago',
      read: true
    }
  ];

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'all') return true;
    if (filter === 'unread') return !notification.read;
    return notification.type === filter;
  });

  const unreadCount = notifications.filter(n => !n.read).length;

  const filters = [
    { key: 'all', label: 'All', count: notifications.length },
    { key: 'unread', label: 'Unread', count: unreadCount },
    { key: 'appointment', label: 'Appointments', count: notifications.filter(n => n.type === 'appointment').length },
    { key: 'medication', label: 'Medications', count: notifications.filter(n => n.type === 'medication').length },
    { key: 'alert', label: 'Alerts', count: notifications.filter(n => n.type === 'alert').length }
  ];

  const handleNotificationClick = (notification) => {
    // Mark as read and handle navigation based on type
    console.log('Notification clicked:', notification);
  };

  return (
    <DashboardLayout
      title="Notifications"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <NotificationsContainer>
        <NotificationsHeader>
          <Title theme={theme}>
            Notifications
            {unreadCount > 0 && (
              <span style={{
                marginLeft: '12px',
                background: roleColors.primary,
                color: 'white',
                padding: '4px 12px',
                borderRadius: '12px',
                fontSize: '14px',
                fontWeight: '600'
              }}>
                {unreadCount} new
              </span>
            )}
          </Title>

          <FilterButtons>
            {filters.map(filterOption => (
              <FilterButton
                key={filterOption.key}
                active={filter === filterOption.key}
                onClick={() => setFilter(filterOption.key)}
                theme={theme}
              >
                {filterOption.label} ({filterOption.count})
              </FilterButton>
            ))}
          </FilterButtons>
        </NotificationsHeader>

        {filteredNotifications.length > 0 ? (
          <NotificationsList>
            {filteredNotifications.map(notification => (
              <NotificationCard
                key={notification.id}
                type={notification.type}
                read={notification.read}
                onClick={() => handleNotificationClick(notification)}
                theme={theme}
              >
                <NotificationHeader>
                  <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                    <NotificationIcon>{notification.icon}</NotificationIcon>
                    <NotificationContent>
                      <NotificationTitle theme={theme}>
                        {notification.title}
                      </NotificationTitle>
                      <NotificationMessage theme={theme}>
                        {notification.message}
                      </NotificationMessage>
                    </NotificationContent>
                  </div>
                  {!notification.read && (
                    <NotificationBadge theme={theme}>New</NotificationBadge>
                  )}
                </NotificationHeader>

                <NotificationMeta theme={theme}>
                  <NotificationTime>{notification.time}</NotificationTime>
                  <span style={{ textTransform: 'capitalize' }}>
                    {notification.type}
                  </span>
                </NotificationMeta>
              </NotificationCard>
            ))}
          </NotificationsList>
        ) : (
          <EmptyState theme={theme}>
            <EmptyIcon>🔔</EmptyIcon>
            <EmptyTitle>No notifications found</EmptyTitle>
            <EmptyMessage>
              {filter === 'all'
                ? "You don't have any notifications yet"
                : `No ${filter} notifications found`
              }
            </EmptyMessage>
          </EmptyState>
        )}
      </NotificationsContainer>
    </DashboardLayout>
  );
};

export default NotificationsScreen;
