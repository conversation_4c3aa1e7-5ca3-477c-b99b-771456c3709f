import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';

const NotificationBanner = styled.div`
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
  color: white;
  padding: 16px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
`;

const NotificationContent = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const NotificationIcon = styled.div`
  font-size: 24px;
  flex-shrink: 0;
`;

const NotificationText = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const NotificationTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  margin: 0;
`;

const NotificationMessage = styled.p`
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
`;

const NotificationActions = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const ActionButton = styled.button`
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
  }

  &.primary {
    background: white;
    color: #FF9800;
    border-color: white;

    &:hover {
      background: rgba(255, 255, 255, 0.9);
    }
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const ProfileNotification = () => {
  const [isVisible, setIsVisible] = useState(true);
  const { user } = useAuth();
  const { theme } = useTheme();
  const navigate = useNavigate();

  if (!isVisible || !user || user.profileComplete) {
    return null;
  }

  const handleCompleteProfile = () => {
    navigate('/profile-completion');
    setIsVisible(false);
  };

  const handleDismiss = () => {
    setIsVisible(false);
    // Store dismissal in localStorage to prevent showing again for this session
    localStorage.setItem('profileNotificationDismissed', 'true');
  };

  // Check if notification was already dismissed this session
  React.useEffect(() => {
    const dismissed = localStorage.getItem('profileNotificationDismissed');
    if (dismissed) {
      setIsVisible(false);
    }
  }, []);

  return (
    <NotificationBanner>
      <NotificationContent>
        <NotificationIcon>⚠️</NotificationIcon>
        <NotificationText>
          <NotificationTitle>Complete Your Profile</NotificationTitle>
          <NotificationMessage>
            Please complete your profile to access all NeuroCare features and ensure proper healthcare coordination.
          </NotificationMessage>
        </NotificationText>
      </NotificationContent>

      <NotificationActions>
        <ActionButton onClick={handleDismiss}>
          Later
        </ActionButton>
        <ActionButton className="primary" onClick={handleCompleteProfile}>
          Complete Now
        </ActionButton>
        <CloseButton onClick={handleDismiss}>
          ×
        </CloseButton>
      </NotificationActions>
    </NotificationBanner>
  );
};

export default ProfileNotification;
