import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Modal } from '../common';
import { formatDate } from '../../utils/dateUtils';

const ConsultationContainer = styled.div`
  max-width: 1000px;
  margin: 0 auto;
`;

const ConsultationHeader = styled(Card)`
  margin-bottom: 24px;
`;

const DoctorInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;
`;

const DoctorAvatar = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #2196F3 0%, #64B5F6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  font-weight: 700;
  box-shadow: ${props => props.theme.shadows.lg};
`;

const DoctorDetails = styled.div`
  flex: 1;
`;

const DoctorName = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const DoctorMeta = styled.div`
  display: flex;
  gap: 24px;
  color: ${props => props.theme.colors.textSecondary};
  font-size: 14px;
  margin-bottom: 8px;
`;

const ConsultationStatus = styled.span`
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  
  ${props => {
    switch (props.status) {
      case 'scheduled':
        return `background: #E3F2FD; color: #1976D2;`;
      case 'in_progress':
        return `background: #E8F5E8; color: #2E7D32;`;
      case 'completed':
        return `background: #F3E5F5; color: #7B1FA2;`;
      case 'cancelled':
        return `background: #FFEBEE; color: #C62828;`;
      default:
        return `background: #F5F5F5; color: #757575;`;
    }
  }}
`;

const ConsultationActions = styled.div`
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
`;

const ConsultationContent = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const SectionCard = styled(Card)`
  height: fit-content;
`;

const SectionTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid ${props => props.theme.colors.borderLight};
`;

const InfoList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const InfoItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};

  &:last-child {
    border-bottom: none;
  }
`;

const InfoLabel = styled.span`
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const InfoValue = styled.span`
  color: ${props => props.theme.colors.textSecondary};
  text-align: right;
`;

const PreparationList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const PreparationItem = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 8px;
`;

const PreparationIcon = styled.div`
  font-size: 20px;
  margin-top: 2px;
`;

const PreparationText = styled.div`
  flex: 1;
`;

const PreparationTitle = styled.h4`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 4px 0;
`;

const PreparationDescription = styled.p`
  font-size: 13px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
  line-height: 1.4;
`;

const VideoCallCard = styled(Card)`
  text-align: center;
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  color: white;
  margin-bottom: 24px;
`;

const VideoCallIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const VideoCallTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
`;

const VideoCallDescription = styled.p`
  margin: 0 0 20px 0;
  opacity: 0.9;
`;

const PatientConsultationScreen = () => {
  const { consultationId } = useParams();
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();

  const [consultation, setConsultation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showCancelModal, setShowCancelModal] = useState(false);

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Appointments', icon: '📅', path: '/appointments' },
    { label: 'Messages', icon: '💬', path: '/messages' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  useEffect(() => {
    if (consultationId) {
      loadConsultationDetails();
    }
  }, [consultationId]);

  const loadConsultationDetails = async () => {
    try {
      setLoading(true);
      
      // Mock consultation data
      const mockConsultation = {
        id: consultationId,
        type: 'video_consultation',
        status: 'scheduled',
        doctor: {
          id: 'doctor-1',
          name: 'Dr. Sarah Johnson',
          specialization: 'Neurologist',
          experience: '15 years',
          rating: 4.9,
          clinic: 'NeuroCare Medical Center'
        },
        patient: {
          id: user.uid,
          name: user.displayName
        },
        scheduledDate: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
        duration: 30,
        reason: 'Follow-up consultation for Alzheimer\'s treatment',
        notes: 'Regular check-up to monitor medication effectiveness and cognitive function.',
        preparation: [
          {
            title: 'Prepare Your Questions',
            description: 'Write down any questions or concerns you want to discuss with the doctor.',
            icon: '❓'
          },
          {
            title: 'Medication List',
            description: 'Have your current medications ready to review with the doctor.',
            icon: '💊'
          },
          {
            title: 'Symptom Journal',
            description: 'Prepare notes about any symptoms or changes since your last visit.',
            icon: '📝'
          },
          {
            title: 'Test Your Technology',
            description: 'Ensure your camera and microphone are working properly.',
            icon: '📹'
          },
          {
            title: 'Quiet Environment',
            description: 'Find a quiet, well-lit space for your video consultation.',
            icon: '🏠'
          }
        ],
        meetingLink: 'https://meet.neurocare.com/consultation-123',
        canJoin: false // Will be true 15 minutes before appointment
      };
      
      setConsultation(mockConsultation);
      
    } catch (error) {
      console.error('Error loading consultation details:', error);
      toast.error('Failed to load consultation details');
    } finally {
      setLoading(false);
    }
  };

  const handleJoinCall = () => {
    if (consultation?.meetingLink) {
      navigate(`/video-call/${consultation.id}`);
    }
  };

  const handleReschedule = () => {
    navigate(`/appointments/${consultation.id}/reschedule`);
  };

  const handleCancel = () => {
    setShowCancelModal(true);
  };

  const confirmCancel = async () => {
    try {
      // In a real implementation, you would cancel the appointment
      console.log('Cancelling consultation:', consultation.id);
      
      toast.success('Consultation cancelled successfully');
      setShowCancelModal(false);
      navigate('/appointments');
      
    } catch (error) {
      console.error('Error cancelling consultation:', error);
      toast.error('Failed to cancel consultation');
    }
  };

  const handleSendMessage = () => {
    navigate(`/chat/doctor-${consultation.doctor.id}`);
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'scheduled': return 'Scheduled';
      case 'in_progress': return 'In Progress';
      case 'completed': return 'Completed';
      case 'cancelled': return 'Cancelled';
      default: return status;
    }
  };

  const isJoinable = () => {
    if (!consultation) return false;
    const now = new Date();
    const appointmentTime = new Date(consultation.scheduledDate);
    const timeDiff = appointmentTime.getTime() - now.getTime();
    return timeDiff <= 15 * 60 * 1000 && timeDiff >= -30 * 60 * 1000; // 15 min before to 30 min after
  };

  if (loading) {
    return (
      <DashboardLayout
        title="Consultation"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <ConsultationContainer>
          <Card theme={theme}>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              Loading consultation details...
            </div>
          </Card>
        </ConsultationContainer>
      </DashboardLayout>
    );
  }

  if (!consultation) {
    return (
      <DashboardLayout
        title="Consultation"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <ConsultationContainer>
          <Card theme={theme}>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              Consultation not found
            </div>
          </Card>
        </ConsultationContainer>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Video Consultation"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <ConsultationContainer>
        {isJoinable() && (
          <VideoCallCard theme={theme}>
            <VideoCallIcon>📹</VideoCallIcon>
            <VideoCallTitle>Your consultation is ready!</VideoCallTitle>
            <VideoCallDescription>
              You can now join your video consultation with {consultation.doctor.name}
            </VideoCallDescription>
            <Button
              variant="light"
              size="large"
              onClick={handleJoinCall}
              icon="📹"
            >
              Join Video Call
            </Button>
          </VideoCallCard>
        )}

        <ConsultationHeader theme={theme}>
          <DoctorInfo>
            <DoctorAvatar theme={theme}>
              {consultation.doctor.name.split(' ').map(n => n.charAt(0)).join('')}
            </DoctorAvatar>
            <DoctorDetails>
              <DoctorName theme={theme}>{consultation.doctor.name}</DoctorName>
              <DoctorMeta theme={theme}>
                <span>{consultation.doctor.specialization}</span>
                <span>{consultation.doctor.experience} experience</span>
                <span>⭐ {consultation.doctor.rating}</span>
              </DoctorMeta>
              <ConsultationStatus status={consultation.status}>
                {getStatusText(consultation.status)}
              </ConsultationStatus>
            </DoctorDetails>
          </DoctorInfo>

          <ConsultationActions>
            {!isJoinable() && consultation.status === 'scheduled' && (
              <Button
                variant="primary"
                onClick={handleJoinCall}
                disabled={!isJoinable()}
                icon="📹"
              >
                {isJoinable() ? 'Join Call' : 'Call Available Soon'}
              </Button>
            )}
            <Button
              variant="outline"
              onClick={handleSendMessage}
              icon="💬"
            >
              Message Doctor
            </Button>
            <Button
              variant="outline"
              onClick={handleReschedule}
              icon="📅"
            >
              Reschedule
            </Button>
            <Button
              variant="danger"
              onClick={handleCancel}
              icon="❌"
            >
              Cancel
            </Button>
          </ConsultationActions>
        </ConsultationHeader>

        <ConsultationContent>
          <SectionCard title="Consultation Details" theme={theme}>
            <SectionTitle theme={theme}>Consultation Details</SectionTitle>
            <InfoList>
              <InfoItem theme={theme}>
                <InfoLabel theme={theme}>Date & Time</InfoLabel>
                <InfoValue theme={theme}>
                  {formatDate(consultation.scheduledDate, 'datetime')}
                </InfoValue>
              </InfoItem>
              <InfoItem theme={theme}>
                <InfoLabel theme={theme}>Duration</InfoLabel>
                <InfoValue theme={theme}>{consultation.duration} minutes</InfoValue>
              </InfoItem>
              <InfoItem theme={theme}>
                <InfoLabel theme={theme}>Type</InfoLabel>
                <InfoValue theme={theme}>Video Consultation</InfoValue>
              </InfoItem>
              <InfoItem theme={theme}>
                <InfoLabel theme={theme}>Reason</InfoLabel>
                <InfoValue theme={theme}>{consultation.reason}</InfoValue>
              </InfoItem>
              <InfoItem theme={theme}>
                <InfoLabel theme={theme}>Clinic</InfoLabel>
                <InfoValue theme={theme}>{consultation.doctor.clinic}</InfoValue>
              </InfoItem>
            </InfoList>
          </SectionCard>

          <SectionCard title="Preparation Checklist" theme={theme}>
            <SectionTitle theme={theme}>Preparation Checklist</SectionTitle>
            <PreparationList>
              {consultation.preparation.map((item, index) => (
                <PreparationItem key={index} theme={theme}>
                  <PreparationIcon>{item.icon}</PreparationIcon>
                  <PreparationText>
                    <PreparationTitle theme={theme}>{item.title}</PreparationTitle>
                    <PreparationDescription theme={theme}>
                      {item.description}
                    </PreparationDescription>
                  </PreparationText>
                </PreparationItem>
              ))}
            </PreparationList>
          </SectionCard>
        </ConsultationContent>

        {/* Cancel Confirmation Modal */}
        <Modal
          isOpen={showCancelModal}
          onClose={() => setShowCancelModal(false)}
          title="Cancel Consultation"
          maxWidth="500px"
        >
          <div>
            <p style={{ marginBottom: '24px', color: theme.colors.text }}>
              Are you sure you want to cancel this consultation with {consultation.doctor.name}?
            </p>
            <p style={{ marginBottom: '24px', color: theme.colors.textSecondary, fontSize: '14px' }}>
              Scheduled for: {formatDate(consultation.scheduledDate, 'datetime')}
            </p>
            
            <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
              <Button
                variant="outline"
                onClick={() => setShowCancelModal(false)}
              >
                Keep Consultation
              </Button>
              <Button
                variant="danger"
                onClick={confirmCancel}
              >
                Cancel Consultation
              </Button>
            </div>
          </div>
        </Modal>
      </ConsultationContainer>
    </DashboardLayout>
  );
};

export default PatientConsultationScreen;
