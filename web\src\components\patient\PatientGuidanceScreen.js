import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { <PERSON>ton, Card, Modal } from '../common';
import locationService from '../../services/locationService';

const GuidanceContainer = styled.div`
  max-width: 1000px;
  margin: 0 auto;
`;

const MapCard = styled(Card)`
  height: 500px;
  padding: 0;
  overflow: hidden;
  margin-bottom: 24px;
`;

const MapArea = styled.div`
  width: 100%;
  height: 100%;
  background: ${props => props.theme.colors.lightGray};
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const MapPlaceholder = styled.div`
  text-align: center;
  color: ${props => props.theme.colors.textSecondary};
`;

const MapIcon = styled.div`
  font-size: 64px;
  margin-bottom: 16px;
`;

const NavigationOverlay = styled.div`
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: ${props => props.theme.shadows.lg};
  padding: 20px;
  z-index: 10;
`;

const DestinationInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
`;

const DestinationIcon = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: ${props => props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
`;

const DestinationDetails = styled.div`
  flex: 1;
`;

const DestinationName = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 4px 0;
`;

const DestinationAddress = styled.p`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
`;

const NavigationStats = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 16px;
`;

const StatItem = styled.div`
  text-align: center;
  padding: 12px;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 8px;
`;

const StatValue = styled.div`
  font-size: 20px;
  font-weight: 700;
  color: ${props => props.theme.colors.primary};
  margin-bottom: 4px;
`;

const StatLabel = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
`;

const NavigationActions = styled.div`
  display: flex;
  gap: 12px;
  justify-content: center;
`;

const InstructionsCard = styled(Card)`
  margin-bottom: 24px;
`;

const InstructionsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const InstructionItem = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: ${props => props.current ? props.theme.colors.primary + '20' : props.theme.colors.lightGray};
  border-radius: 12px;
  border-left: 4px solid ${props => props.current ? props.theme.colors.primary : 'transparent'};
`;

const InstructionIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: ${props => props.current ? props.theme.colors.primary : props.theme.colors.border};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.current ? 'white' : props.theme.colors.textSecondary};
  font-size: 18px;
`;

const InstructionText = styled.div`
  flex: 1;
`;

const InstructionDirection = styled.h4`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 4px 0;
`;

const InstructionDistance = styled.p`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
`;

const EmergencyCard = styled(Card)`
  background: linear-gradient(135deg, #F44336 0%, #E57373 100%);
  color: white;
  text-align: center;
`;

const EmergencyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const EmergencyTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
`;

const EmergencyDescription = styled.p`
  margin: 0 0 20px 0;
  opacity: 0.9;
`;

const PatientGuidanceScreen = () => {
  const { destinationId } = useParams();
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();

  const [destination, setDestination] = useState(null);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [instructions, setInstructions] = useState([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [navigationStats, setNavigationStats] = useState({
    distance: '0 km',
    duration: '0 min',
    arrival: '--:--'
  });
  const [isNavigating, setIsNavigating] = useState(false);
  const [showEmergencyModal, setShowEmergencyModal] = useState(false);
  const [loading, setLoading] = useState(true);

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Navigation', icon: '🧭', path: '/navigation' },
    { label: 'Appointments', icon: '📅', path: '/appointments' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  useEffect(() => {
    if (destinationId) {
      loadDestination();
      getCurrentLocation();
    }
  }, [destinationId]);

  useEffect(() => {
    if (isNavigating) {
      const interval = setInterval(() => {
        updateNavigation();
      }, 5000); // Update every 5 seconds

      return () => clearInterval(interval);
    }
  }, [isNavigating]);

  const loadDestination = async () => {
    try {
      setLoading(true);
      
      // Mock destination data
      const mockDestination = {
        id: destinationId,
        name: 'NeuroCare Medical Center',
        address: '123 Avenue des Champs-Élysées, 75008 Paris',
        type: 'medical_center',
        coordinates: {
          latitude: 48.8698,
          longitude: 2.3076
        },
        phone: '+33 1 23 45 67 89',
        description: 'Your appointment with Dr. Sarah Johnson'
      };
      
      setDestination(mockDestination);
      
      // Mock navigation instructions
      const mockInstructions = [
        {
          id: 1,
          direction: 'Head north on Rue de la Paix',
          distance: '200 m',
          icon: '⬆️',
          current: true
        },
        {
          id: 2,
          direction: 'Turn right onto Boulevard Haussmann',
          distance: '500 m',
          icon: '➡️',
          current: false
        },
        {
          id: 3,
          direction: 'Continue straight for 1.2 km',
          distance: '1.2 km',
          icon: '⬆️',
          current: false
        },
        {
          id: 4,
          direction: 'Turn left onto Avenue des Champs-Élysées',
          distance: '300 m',
          icon: '⬅️',
          current: false
        },
        {
          id: 5,
          direction: 'Destination will be on your right',
          distance: '50 m',
          icon: '🏁',
          current: false
        }
      ];
      
      setInstructions(mockInstructions);
      setNavigationStats({
        distance: '2.3 km',
        duration: '8 min',
        arrival: new Date(Date.now() + 8 * 60 * 1000).toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit'
        })
      });
      
    } catch (error) {
      console.error('Error loading destination:', error);
      toast.error('Failed to load destination');
    } finally {
      setLoading(false);
    }
  };

  const getCurrentLocation = async () => {
    try {
      const position = await locationService.getCurrentPosition();
      setCurrentLocation(position);
    } catch (error) {
      console.error('Error getting current location:', error);
      toast.error('Unable to get your current location');
    }
  };

  const updateNavigation = () => {
    // Simulate navigation progress
    setCurrentStep(prev => {
      const nextStep = prev + 1;
      if (nextStep < instructions.length) {
        setInstructions(prevInstructions => 
          prevInstructions.map((instruction, index) => ({
            ...instruction,
            current: index === nextStep
          }))
        );
        return nextStep;
      } else {
        // Navigation completed
        setIsNavigating(false);
        toast.success('You have arrived at your destination!');
        return prev;
      }
    });
  };

  const handleStartNavigation = () => {
    setIsNavigating(true);
    toast.success('Navigation started');
  };

  const handleStopNavigation = () => {
    setIsNavigating(false);
    toast.info('Navigation stopped');
  };

  const handleEmergencyCall = () => {
    setShowEmergencyModal(true);
  };

  const handleCall911 = () => {
    window.open('tel:911');
    setShowEmergencyModal(false);
  };

  const handleCallCaregiver = () => {
    // Mock caregiver phone
    window.open('tel:+33123456789');
    setShowEmergencyModal(false);
  };

  const getDirectionIcon = (direction) => {
    if (direction.includes('north')) return '⬆️';
    if (direction.includes('south')) return '⬇️';
    if (direction.includes('east')) return '➡️';
    if (direction.includes('west')) return '⬅️';
    if (direction.includes('right')) return '↗️';
    if (direction.includes('left')) return '↖️';
    if (direction.includes('straight')) return '⬆️';
    if (direction.includes('Destination')) return '🏁';
    return '📍';
  };

  if (loading) {
    return (
      <DashboardLayout
        title="Navigation"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <GuidanceContainer>
          <Card theme={theme}>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              Loading navigation...
            </div>
          </Card>
        </GuidanceContainer>
      </DashboardLayout>
    );
  }

  if (!destination) {
    return (
      <DashboardLayout
        title="Navigation"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <GuidanceContainer>
          <Card theme={theme}>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              Destination not found
            </div>
          </Card>
        </GuidanceContainer>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Navigation"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <GuidanceContainer>
        <MapCard theme={theme}>
          <MapArea theme={theme}>
            <MapPlaceholder theme={theme}>
              <MapIcon>🗺️</MapIcon>
              <h3>GPS Navigation Map</h3>
              <p>Real-time navigation would be displayed here</p>
              <p style={{ fontSize: '14px', marginTop: '16px' }}>
                Integration with Google Maps or Mapbox for:
                <br />• Turn-by-turn navigation
                <br />• Real-time traffic updates
                <br />• Voice guidance
                <br />• Offline maps support
              </p>
            </MapPlaceholder>

            <NavigationOverlay theme={theme}>
              <DestinationInfo>
                <DestinationIcon theme={theme}>🏥</DestinationIcon>
                <DestinationDetails>
                  <DestinationName theme={theme}>{destination.name}</DestinationName>
                  <DestinationAddress theme={theme}>{destination.address}</DestinationAddress>
                </DestinationDetails>
              </DestinationInfo>

              <NavigationStats>
                <StatItem theme={theme}>
                  <StatValue theme={theme}>{navigationStats.distance}</StatValue>
                  <StatLabel theme={theme}>Distance</StatLabel>
                </StatItem>
                <StatItem theme={theme}>
                  <StatValue theme={theme}>{navigationStats.duration}</StatValue>
                  <StatLabel theme={theme}>Duration</StatLabel>
                </StatItem>
                <StatItem theme={theme}>
                  <StatValue theme={theme}>{navigationStats.arrival}</StatValue>
                  <StatLabel theme={theme}>Arrival</StatLabel>
                </StatItem>
              </NavigationStats>

              <NavigationActions>
                {!isNavigating ? (
                  <Button
                    variant="primary"
                    onClick={handleStartNavigation}
                    icon="🧭"
                  >
                    Start Navigation
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    onClick={handleStopNavigation}
                    icon="⏹️"
                  >
                    Stop Navigation
                  </Button>
                )}
                <Button
                  variant="outline"
                  onClick={() => window.open(`tel:${destination.phone}`)}
                  icon="📞"
                >
                  Call Destination
                </Button>
              </NavigationActions>
            </NavigationOverlay>
          </MapArea>
        </MapCard>

        <InstructionsCard title="Turn-by-Turn Directions" theme={theme}>
          <InstructionsList>
            {instructions.map((instruction, index) => (
              <InstructionItem
                key={instruction.id}
                current={instruction.current}
                theme={theme}
              >
                <InstructionIcon current={instruction.current} theme={theme}>
                  {getDirectionIcon(instruction.direction)}
                </InstructionIcon>
                <InstructionText>
                  <InstructionDirection theme={theme}>
                    {instruction.direction}
                  </InstructionDirection>
                  <InstructionDistance theme={theme}>
                    {instruction.distance}
                  </InstructionDistance>
                </InstructionText>
              </InstructionItem>
            ))}
          </InstructionsList>
        </InstructionsCard>

        <EmergencyCard theme={theme}>
          <EmergencyIcon>🚨</EmergencyIcon>
          <EmergencyTitle>Need Help?</EmergencyTitle>
          <EmergencyDescription>
            If you're lost or need assistance, tap the button below for emergency help.
          </EmergencyDescription>
          <Button
            variant="light"
            onClick={handleEmergencyCall}
            icon="📞"
          >
            Emergency Help
          </Button>
        </EmergencyCard>

        {/* Emergency Modal */}
        <Modal
          isOpen={showEmergencyModal}
          onClose={() => setShowEmergencyModal(false)}
          title="Emergency Help"
          maxWidth="500px"
        >
          <div>
            <p style={{ marginBottom: '24px', color: theme.colors.text }}>
              Choose who you'd like to contact for help:
            </p>
            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <Button
                variant="danger"
                onClick={handleCall911}
                icon="🚨"
                style={{ justifyContent: 'flex-start' }}
              >
                Call Emergency Services (911)
              </Button>
              
              <Button
                variant="outline"
                onClick={handleCallCaregiver}
                icon="👨‍⚕️"
                style={{ justifyContent: 'flex-start' }}
              >
                Call Your Caregiver
              </Button>
              
              <Button
                variant="outline"
                onClick={() => window.open(`tel:${destination.phone}`)}
                icon="🏥"
                style={{ justifyContent: 'flex-start' }}
              >
                Call Destination ({destination.name})
              </Button>
            </div>
            
            <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '24px' }}>
              <Button
                variant="outline"
                onClick={() => setShowEmergencyModal(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        </Modal>
      </GuidanceContainer>
    </DashboardLayout>
  );
};

export default PatientGuidanceScreen;
