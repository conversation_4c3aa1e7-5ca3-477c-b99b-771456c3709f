import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Input, Select } from '../common';
import { useForm } from '../../hooks/useForm';
import { validateRequired, validateEmail, validatePhone } from '../../utils/validation';

const AddPatientContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const BackButton = styled(Button)`
  margin-bottom: 16px;
`;

const FormCard = styled(Card)`
  margin-bottom: 24px;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid ${props => props.theme.colors.borderLight};
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
`;

const FullWidthField = styled.div`
  grid-column: 1 / -1;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 32px;
`;

const MethodSelector = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 32px;
  padding: 20px;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 12px;
`;

const MethodOption = styled.div`
  flex: 1;
  padding: 20px;
  border: 2px solid ${props => props.selected ? props.theme.colors.primary : props.theme.colors.border};
  border-radius: 12px;
  background: ${props => props.selected ? props.theme.colors.primary + '10' : 'white'};
  cursor: pointer;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    border-color: ${props => props.theme.colors.primary};
  }
`;

const MethodIcon = styled.div`
  font-size: 32px;
  margin-bottom: 8px;
`;

const MethodTitle = styled.h4`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 4px 0;
`;

const MethodDescription = styled.p`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
`;

const AddPatientScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);
  const [addMethod, setAddMethod] = useState('manual'); // 'manual' or 'scan'

  const roleColors = getRoleColors(user?.role || 'supervisor');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Patients', icon: '👥', path: '/patients' },
    { label: 'Add Patient', icon: '+', path: '/patients/add' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  const initialValues = {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    gender: '',
    address: '',
    city: '',
    postalCode: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelation: '',
    medicalCondition: '',
    medications: '',
    allergies: '',
    notes: ''
  };

  const validators = {
    firstName: (value) => validateRequired(value, 'First name'),
    lastName: (value) => validateRequired(value, 'Last name'),
    email: validateEmail,
    phone: validatePhone,
    dateOfBirth: (value) => validateRequired(value, 'Date of birth'),
    gender: (value) => validateRequired(value, 'Gender'),
    emergencyContactName: (value) => validateRequired(value, 'Emergency contact name'),
    emergencyContactPhone: validatePhone,
    emergencyContactRelation: (value) => validateRequired(value, 'Emergency contact relation')
  };

  const {
    values,
    errors,
    handleChange,
    handleSubmit,
    getFieldProps
  } = useForm(
    initialValues,
    validators,
    async (formData) => {
      setLoading(true);
      try {
        // Generate user code for the patient
        const userCode = `PAT${Math.random().toString(36).substr(2, 5).toUpperCase()}`;
        
        const patientData = {
          ...formData,
          userCode,
          role: 'patient',
          profileComplete: true,
          createdBy: user.uid,
          createdAt: new Date().toISOString(),
          status: 'active'
        };

        // In a real implementation, you would save this to Firebase
        console.log('Creating patient:', patientData);
        
        toast.success(`Patient ${formData.firstName} ${formData.lastName} added successfully!`);
        navigate('/patients');
        
      } catch (error) {
        console.error('Error adding patient:', error);
        toast.error('Failed to add patient. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  );

  const handleScanQR = () => {
    navigate('/scanner?type=patient');
  };

  if (addMethod === 'scan') {
    return (
      <DashboardLayout
        title="Add Patient"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <AddPatientContainer>
          <BackButton
            variant="ghost"
            onClick={() => navigate('/patients')}
            icon="←"
          >
            Back to Patients
          </BackButton>

          <FormCard title="Add Patient by QR Code" theme={theme}>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <div style={{ fontSize: '64px', marginBottom: '24px' }}>📱</div>
              <h3>Scan Patient QR Code</h3>
              <p style={{ marginBottom: '32px', color: theme.colors.textSecondary }}>
                Ask the patient to show their QR code from their profile, then scan it to add them to your care.
              </p>
              <div style={{ display: 'flex', gap: '16px', justifyContent: 'center' }}>
                <Button
                  variant="primary"
                  onClick={handleScanQR}
                  icon="📷"
                >
                  Open Scanner
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setAddMethod('manual')}
                >
                  Manual Entry Instead
                </Button>
              </div>
            </div>
          </FormCard>
        </AddPatientContainer>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Add Patient"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <AddPatientContainer>
        <BackButton
          variant="ghost"
          onClick={() => navigate('/patients')}
          icon="←"
        >
          Back to Patients
        </BackButton>

        <FormCard title="Add New Patient" theme={theme}>
          <MethodSelector theme={theme}>
            <MethodOption
              selected={addMethod === 'scan'}
              onClick={() => setAddMethod('scan')}
              theme={theme}
            >
              <MethodIcon>📱</MethodIcon>
              <MethodTitle theme={theme}>Scan QR Code</MethodTitle>
              <MethodDescription theme={theme}>
                Quick and easy - scan patient's QR code
              </MethodDescription>
            </MethodOption>
            
            <MethodOption
              selected={addMethod === 'manual'}
              onClick={() => setAddMethod('manual')}
              theme={theme}
            >
              <MethodIcon>✍️</MethodIcon>
              <MethodTitle theme={theme}>Manual Entry</MethodTitle>
              <MethodDescription theme={theme}>
                Enter patient information manually
              </MethodDescription>
            </MethodOption>
          </MethodSelector>

          <form onSubmit={handleSubmit}>
            <FormSection>
              <SectionTitle theme={theme}>Personal Information</SectionTitle>
              <FormGrid>
                <Input
                  {...getFieldProps('firstName')}
                  label="First Name *"
                  placeholder="Enter first name"
                />
                <Input
                  {...getFieldProps('lastName')}
                  label="Last Name *"
                  placeholder="Enter last name"
                />
                <Input
                  {...getFieldProps('email')}
                  label="Email *"
                  type="email"
                  placeholder="<EMAIL>"
                />
                <Input
                  {...getFieldProps('phone')}
                  label="Phone *"
                  type="tel"
                  placeholder="+33 1 23 45 67 89"
                />
                <Input
                  {...getFieldProps('dateOfBirth')}
                  label="Date of Birth *"
                  type="date"
                />
                <Select
                  {...getFieldProps('gender')}
                  label="Gender *"
                  options={[
                    { value: '', label: 'Select gender' },
                    { value: 'male', label: 'Male' },
                    { value: 'female', label: 'Female' },
                    { value: 'other', label: 'Other' }
                  ]}
                />
              </FormGrid>
            </FormSection>

            <FormSection>
              <SectionTitle theme={theme}>Address</SectionTitle>
              <FormGrid>
                <FullWidthField>
                  <Input
                    {...getFieldProps('address')}
                    label="Address"
                    placeholder="Street address"
                  />
                </FullWidthField>
                <Input
                  {...getFieldProps('city')}
                  label="City"
                  placeholder="City"
                />
                <Input
                  {...getFieldProps('postalCode')}
                  label="Postal Code"
                  placeholder="75001"
                />
              </FormGrid>
            </FormSection>

            <FormSection>
              <SectionTitle theme={theme}>Emergency Contact</SectionTitle>
              <FormGrid>
                <Input
                  {...getFieldProps('emergencyContactName')}
                  label="Contact Name *"
                  placeholder="Full name"
                />
                <Input
                  {...getFieldProps('emergencyContactPhone')}
                  label="Contact Phone *"
                  type="tel"
                  placeholder="+33 1 23 45 67 89"
                />
                <Select
                  {...getFieldProps('emergencyContactRelation')}
                  label="Relationship *"
                  options={[
                    { value: '', label: 'Select relationship' },
                    { value: 'spouse', label: 'Spouse' },
                    { value: 'child', label: 'Child' },
                    { value: 'parent', label: 'Parent' },
                    { value: 'sibling', label: 'Sibling' },
                    { value: 'friend', label: 'Friend' },
                    { value: 'other', label: 'Other' }
                  ]}
                />
              </FormGrid>
            </FormSection>

            <FormSection>
              <SectionTitle theme={theme}>Medical Information</SectionTitle>
              <FormGrid>
                <FullWidthField>
                  <Input
                    {...getFieldProps('medicalCondition')}
                    label="Medical Condition"
                    placeholder="Primary medical condition or diagnosis"
                  />
                </FullWidthField>
                <FullWidthField>
                  <Input
                    {...getFieldProps('medications')}
                    label="Current Medications"
                    placeholder="List current medications"
                    multiline
                    rows={3}
                  />
                </FullWidthField>
                <FullWidthField>
                  <Input
                    {...getFieldProps('allergies')}
                    label="Allergies"
                    placeholder="Known allergies or adverse reactions"
                  />
                </FullWidthField>
                <FullWidthField>
                  <Input
                    {...getFieldProps('notes')}
                    label="Additional Notes"
                    placeholder="Any additional information about the patient"
                    multiline
                    rows={3}
                  />
                </FullWidthField>
              </FormGrid>
            </FormSection>

            <ButtonGroup>
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/patients')}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={loading}
              >
                Add Patient
              </Button>
            </ButtonGroup>
          </form>
        </FormCard>
      </AddPatientContainer>
    </DashboardLayout>
  );
};

export default AddPatientScreen;
