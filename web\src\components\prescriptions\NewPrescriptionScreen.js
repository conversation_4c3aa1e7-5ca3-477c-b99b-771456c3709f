import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useMedications } from '../../contexts/MedicationContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Input, Select, Modal } from '../common';
import { useForm } from '../../hooks/useForm';
import { validateRequired } from '../../utils/validation';

const PrescriptionContainer = styled.div`
  max-width: 900px;
  margin: 0 auto;
`;

const BackButton = styled(Button)`
  margin-bottom: 16px;
`;

const FormCard = styled(Card)`
  margin-bottom: 24px;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid ${props => props.theme.colors.borderLight};
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
`;

const FullWidthField = styled.div`
  grid-column: 1 / -1;
`;

const MedicationsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
`;

const MedicationItem = styled.div`
  background: ${props => props.theme.colors.lightGray};
  border-radius: 12px;
  padding: 20px;
  border: 2px solid ${props => props.theme.colors.border};
`;

const MedicationHeader = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 16px;
`;

const MedicationTitle = styled.h4`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0;
  flex: 1;
`;

const RemoveButton = styled(Button)`
  padding: 4px 8px;
  min-width: auto;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 32px;
`;

const PatientSelector = styled.div`
  background: ${props => props.theme.colors.lightGray};
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
`;

const SelectedPatient = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const PatientAvatar = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: ${props => props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 18px;
`;

const PatientInfo = styled.div`
  flex: 1;
`;

const PatientName = styled.h4`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 4px 0;
`;

const PatientMeta = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
`;

const NewPrescriptionScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const { createPrescription } = useMedications();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const [loading, setLoading] = useState(false);
  const [patients, setPatients] = useState([]);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [medications, setMedications] = useState([]);
  const [showPatientModal, setShowPatientModal] = useState(false);

  const roleColors = getRoleColors(user?.role || 'doctor');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Patients', icon: '👥', path: '/patients' },
    { label: 'Prescriptions', icon: '📋', path: '/prescriptions' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  const initialValues = {
    notes: '',
    clinic: 'NeuroCare Clinic',
    followUpDate: ''
  };

  const validators = {
    notes: (value) => medications.length === 0 ? { isValid: false, message: 'Please add at least one medication' } : { isValid: true, message: '' }
  };

  const {
    values,
    errors,
    handleChange,
    handleSubmit,
    getFieldProps
  } = useForm(
    initialValues,
    validators,
    async (formData) => {
      if (!selectedPatient) {
        toast.error('Please select a patient');
        return;
      }

      if (medications.length === 0) {
        toast.error('Please add at least one medication');
        return;
      }

      setLoading(true);
      try {
        const prescriptionData = {
          patientId: selectedPatient.id,
          patientName: selectedPatient.name,
          doctorId: user.uid,
          doctorName: user.displayName,
          medications: medications,
          notes: formData.notes,
          clinic: formData.clinic,
          followUpDate: formData.followUpDate,
          status: 'sent',
          prescribedDate: new Date().toISOString()
        };

        await createPrescription(prescriptionData);
        toast.success('Prescription created successfully!');
        navigate('/prescriptions');
        
      } catch (error) {
        console.error('Error creating prescription:', error);
        toast.error('Failed to create prescription. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  );

  useEffect(() => {
    loadPatients();
    
    // Check if patient ID is provided in URL params
    const patientId = searchParams.get('patientId');
    if (patientId) {
      // Auto-select patient if provided
      const patient = patients.find(p => p.id === patientId);
      if (patient) {
        setSelectedPatient(patient);
      }
    }
  }, [searchParams]);

  const loadPatients = async () => {
    try {
      // Mock patients data for doctor
      const mockPatients = [
        {
          id: 'patient-1',
          name: 'Marie Dubois',
          age: 72,
          condition: 'Alzheimer\'s Disease',
          email: '<EMAIL>'
        },
        {
          id: 'patient-2',
          name: 'Pierre Martin',
          age: 68,
          condition: 'Parkinson\'s Disease',
          email: '<EMAIL>'
        },
        {
          id: 'patient-3',
          name: 'Jean Dupont',
          age: 75,
          condition: 'Dementia',
          email: '<EMAIL>'
        }
      ];
      
      setPatients(mockPatients);
    } catch (error) {
      console.error('Error loading patients:', error);
    }
  };

  const addMedication = () => {
    const newMedication = {
      id: Date.now().toString(),
      name: '',
      dosage: '',
      frequency: '',
      duration: '',
      instructions: ''
    };
    setMedications([...medications, newMedication]);
  };

  const updateMedication = (id, field, value) => {
    setMedications(medications.map(med => 
      med.id === id ? { ...med, [field]: value } : med
    ));
  };

  const removeMedication = (id) => {
    setMedications(medications.filter(med => med.id !== id));
  };

  const handleSelectPatient = (patient) => {
    setSelectedPatient(patient);
    setShowPatientModal(false);
  };

  return (
    <DashboardLayout
      title="New Prescription"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <PrescriptionContainer>
        <BackButton
          variant="ghost"
          onClick={() => navigate('/prescriptions')}
          icon="←"
        >
          Back to Prescriptions
        </BackButton>

        <FormCard title="Create New Prescription" theme={theme}>
          <FormSection>
            <SectionTitle theme={theme}>Patient Selection</SectionTitle>
            <PatientSelector theme={theme}>
              {selectedPatient ? (
                <SelectedPatient>
                  <PatientAvatar theme={theme}>
                    {selectedPatient.name.charAt(0)}
                  </PatientAvatar>
                  <PatientInfo>
                    <PatientName theme={theme}>{selectedPatient.name}</PatientName>
                    <PatientMeta theme={theme}>
                      Age: {selectedPatient.age} • {selectedPatient.condition}
                    </PatientMeta>
                  </PatientInfo>
                  <Button
                    variant="outline"
                    size="small"
                    onClick={() => setShowPatientModal(true)}
                  >
                    Change Patient
                  </Button>
                </SelectedPatient>
              ) : (
                <div style={{ textAlign: 'center' }}>
                  <p style={{ margin: '0 0 16px 0', color: theme.colors.textSecondary }}>
                    No patient selected
                  </p>
                  <Button
                    variant="primary"
                    onClick={() => setShowPatientModal(true)}
                  >
                    Select Patient
                  </Button>
                </div>
              )}
            </PatientSelector>
          </FormSection>

          <FormSection>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
              <SectionTitle theme={theme} style={{ margin: 0 }}>
                Medications ({medications.length})
              </SectionTitle>
              <Button
                variant="primary"
                size="small"
                onClick={addMedication}
                icon="+"
              >
                Add Medication
              </Button>
            </div>

            {medications.length === 0 ? (
              <div style={{ 
                textAlign: 'center', 
                padding: '40px',
                background: theme.colors.lightGray,
                borderRadius: '12px',
                color: theme.colors.textSecondary
              }}>
                <div style={{ fontSize: '48px', marginBottom: '16px' }}>💊</div>
                <p>No medications added yet</p>
                <Button variant="primary" onClick={addMedication}>
                  Add First Medication
                </Button>
              </div>
            ) : (
              <MedicationsList>
                {medications.map((medication, index) => (
                  <MedicationItem key={medication.id} theme={theme}>
                    <MedicationHeader>
                      <MedicationTitle theme={theme}>
                        Medication {index + 1}
                      </MedicationTitle>
                      <RemoveButton
                        variant="danger"
                        size="small"
                        onClick={() => removeMedication(medication.id)}
                        icon="×"
                      >
                        Remove
                      </RemoveButton>
                    </MedicationHeader>
                    
                    <FormGrid>
                      <Input
                        label="Medication Name *"
                        placeholder="e.g., Aspirin"
                        value={medication.name}
                        onChange={(e) => updateMedication(medication.id, 'name', e.target.value)}
                      />
                      <Input
                        label="Dosage *"
                        placeholder="e.g., 100mg"
                        value={medication.dosage}
                        onChange={(e) => updateMedication(medication.id, 'dosage', e.target.value)}
                      />
                      <Input
                        label="Frequency *"
                        placeholder="e.g., Once daily"
                        value={medication.frequency}
                        onChange={(e) => updateMedication(medication.id, 'frequency', e.target.value)}
                      />
                      <Input
                        label="Duration"
                        placeholder="e.g., 30 days"
                        value={medication.duration}
                        onChange={(e) => updateMedication(medication.id, 'duration', e.target.value)}
                      />
                      <FullWidthField>
                        <Input
                          label="Instructions"
                          placeholder="Special instructions for taking this medication"
                          value={medication.instructions}
                          onChange={(e) => updateMedication(medication.id, 'instructions', e.target.value)}
                          multiline
                          rows={2}
                        />
                      </FullWidthField>
                    </FormGrid>
                  </MedicationItem>
                ))}
              </MedicationsList>
            )}
          </FormSection>

          <form onSubmit={handleSubmit}>
            <FormSection>
              <SectionTitle theme={theme}>Prescription Details</SectionTitle>
              <FormGrid>
                <Input
                  {...getFieldProps('clinic')}
                  label="Clinic/Hospital"
                  placeholder="Clinic name"
                />
                <Input
                  {...getFieldProps('followUpDate')}
                  label="Follow-up Date"
                  type="date"
                />
                <FullWidthField>
                  <Input
                    {...getFieldProps('notes')}
                    label="Notes"
                    placeholder="Additional notes or instructions for the patient"
                    multiline
                    rows={3}
                  />
                </FullWidthField>
              </FormGrid>
            </FormSection>

            <ButtonGroup>
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/prescriptions')}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={loading}
                disabled={!selectedPatient || medications.length === 0}
              >
                Create Prescription
              </Button>
            </ButtonGroup>
          </form>
        </FormCard>

        {/* Patient Selection Modal */}
        <Modal
          isOpen={showPatientModal}
          onClose={() => setShowPatientModal(false)}
          title="Select Patient"
          maxWidth="600px"
        >
          <div>
            <p style={{ marginBottom: '24px', color: theme.colors.textSecondary }}>
              Choose a patient for this prescription:
            </p>
            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {patients.map(patient => (
                <div
                  key={patient.id}
                  onClick={() => handleSelectPatient(patient)}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '16px',
                    padding: '16px',
                    border: `1px solid ${theme.colors.border}`,
                    borderRadius: '8px',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = theme.colors.lightGray;
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = 'transparent';
                  }}
                >
                  <PatientAvatar theme={theme}>
                    {patient.name.charAt(0)}
                  </PatientAvatar>
                  <PatientInfo>
                    <PatientName theme={theme}>{patient.name}</PatientName>
                    <PatientMeta theme={theme}>
                      Age: {patient.age} • {patient.condition}
                    </PatientMeta>
                  </PatientInfo>
                </div>
              ))}
            </div>
            
            <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '24px' }}>
              <Button
                variant="outline"
                onClick={() => setShowPatientModal(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        </Modal>
      </PrescriptionContainer>
    </DashboardLayout>
  );
};

export default NewPrescriptionScreen;
