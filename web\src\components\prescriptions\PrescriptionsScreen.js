import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useMedications } from '../../contexts/MedicationContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { <PERSON>ton, Card, Modal } from '../common';
import { formatDate, getRelativeTime } from '../../utils/dateUtils';
import { PRESCRIPTION_STATUS, MEDICATION_STATUS } from '../../utils/constants';

const PrescriptionsContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const FilterTabs = styled.div`
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  flex-wrap: wrap;
`;

const FilterTab = styled.button`
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid ${props => props.theme.colors.border};

  ${props => props.active ? `
    background: ${props.theme.colors.primary};
    color: white;
    border-color: ${props.theme.colors.primary};
  ` : `
    background: white;
    color: ${props.theme.colors.textSecondary};

    &:hover {
      background: ${props.theme.colors.lightGray};
    }
  `}
`;

const PrescriptionsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const PrescriptionCard = styled(Card)`
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.lg};
  }
`;

const PrescriptionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
`;

const PrescriptionInfo = styled.div`
  flex: 1;
`;

const MedicationName = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const PrescriptionDetails = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const PrescriptionDetail = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  display: flex;
  align-items: center;
  gap: 8px;
`;

const StatusBadge = styled.span`
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;

  ${props => {
    switch (props.status) {
      case 'sent':
        return `background: #E3F2FD; color: #1976D2;`;
      case 'filled':
        return `background: #E8F5E8; color: #2E7D32;`;
      case 'expired':
        return `background: #FFF3E0; color: #F57C00;`;
      case 'cancelled':
        return `background: #FFEBEE; color: #C62828;`;
      default:
        return `background: #F5F5F5; color: #757575;`;
    }
  }}
`;

const MedicationGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
`;

const MedicationItem = styled.div`
  background: ${props => props.theme.colors.lightGray};
  border-radius: 8px;
  padding: 16px;
`;

const MedicationItemName = styled.h4`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const MedicationItemDetails = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.4;
`;

const PrescriptionActions = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 16px;
  flex-wrap: wrap;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: ${props => props.theme.shadows.md};
`;

const EmptyIcon = styled.div`
  font-size: 64px;
  margin-bottom: 16px;
`;

const EmptyTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const EmptyMessage = styled.p`
  font-size: 16px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0 0 24px 0;
`;

const PrescriptionsScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const {
    prescriptions,
    medications,
    loading,
    getPrescriptionsByPatient,
    updatePrescriptionStatus,
    addMedicationFromPrescription
  } = useMedications();
  const navigate = useNavigate();

  const [filter, setFilter] = useState('all');
  const [selectedPrescription, setSelectedPrescription] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', screen: 'dashboard' },
    { label: 'Prescriptions', icon: '💊', screen: 'prescriptions' },
    { label: 'Medications', icon: '🏥', screen: 'medications' },
    { label: 'Profile', icon: '👤', screen: 'profile' }
  ];

  // Filter prescriptions based on selected filter
  const filteredPrescriptions = prescriptions.filter(prescription => {
    switch (filter) {
      case 'active':
        return prescription.status === PRESCRIPTION_STATUS.SENT || prescription.status === PRESCRIPTION_STATUS.FILLED;
      case 'sent':
        return prescription.status === PRESCRIPTION_STATUS.SENT;
      case 'filled':
        return prescription.status === PRESCRIPTION_STATUS.FILLED;
      case 'expired':
        return prescription.status === PRESCRIPTION_STATUS.EXPIRED;
      default:
        return true;
    }
  });

  const handlePrescriptionClick = (prescription) => {
    setSelectedPrescription(prescription);
    setShowDetailsModal(true);
  };

  const handleAddToMedications = async (prescription) => {
    try {
      await addMedicationFromPrescription(prescription);
      toast.success('Medication added to your active medications');
      setShowDetailsModal(false);
    } catch (error) {
      toast.error('Failed to add medication');
    }
  };

  const handleMarkAsFilled = async (prescription) => {
    try {
      await updatePrescriptionStatus(prescription.id, PRESCRIPTION_STATUS.FILLED);
      toast.success('Prescription marked as filled');
      setShowDetailsModal(false);
    } catch (error) {
      toast.error('Failed to update prescription status');
    }
  };

  const getStatusDisplay = (status) => {
    switch (status) {
      case PRESCRIPTION_STATUS.SENT:
        return 'Sent to Pharmacy';
      case PRESCRIPTION_STATUS.FILLED:
        return 'Filled';
      case PRESCRIPTION_STATUS.EXPIRED:
        return 'Expired';
      case PRESCRIPTION_STATUS.CANCELLED:
        return 'Cancelled';
      default:
        return status;
    }
  };

  const filters = [
    { key: 'all', label: 'All', count: prescriptions.length },
    { key: 'active', label: 'Active', count: prescriptions.filter(p => p.status === PRESCRIPTION_STATUS.SENT || p.status === PRESCRIPTION_STATUS.FILLED).length },
    { key: 'sent', label: 'Sent', count: prescriptions.filter(p => p.status === PRESCRIPTION_STATUS.SENT).length },
    { key: 'filled', label: 'Filled', count: prescriptions.filter(p => p.status === PRESCRIPTION_STATUS.FILLED).length }
  ];

  return (
    <DashboardLayout
      title="Prescriptions"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <PrescriptionsContainer>
        <Header>
          <Title theme={theme}>My Prescriptions</Title>
          {user?.role === 'doctor' && (
            <Button
              variant="primary"
              onClick={() => navigate('/prescriptions/new')}
              icon="+"
            >
              New Prescription
            </Button>
          )}
        </Header>

        <FilterTabs>
          {filters.map(filterOption => (
            <FilterTab
              key={filterOption.key}
              active={filter === filterOption.key}
              onClick={() => setFilter(filterOption.key)}
              theme={theme}
            >
              {filterOption.label} ({filterOption.count})
            </FilterTab>
          ))}
        </FilterTabs>

        {loading ? (
          <Card>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <div>Loading prescriptions...</div>
            </div>
          </Card>
        ) : filteredPrescriptions.length > 0 ? (
          <PrescriptionsList>
            {filteredPrescriptions.map(prescription => (
              <PrescriptionCard
                key={prescription.id}
                onClick={() => handlePrescriptionClick(prescription)}
                theme={theme}
              >
                <PrescriptionHeader>
                  <PrescriptionInfo>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                      <span style={{ fontSize: '24px' }}>💊</span>
                      <div>
                        <h3 style={{
                          margin: 0,
                          fontSize: '18px',
                          fontWeight: '600',
                          color: theme.colors.text
                        }}>
                          Prescription from Dr. {prescription.doctorName}
                        </h3>
                        <p style={{
                          margin: '4px 0 0 0',
                          fontSize: '14px',
                          color: theme.colors.textSecondary
                        }}>
                          {prescription.medications?.length || 0} medication(s)
                        </p>
                      </div>
                    </div>
                    <PrescriptionDetails>
                      <PrescriptionDetail theme={theme}>
                        📅 Prescribed: {formatDate(prescription.prescribedDate, 'short')}
                      </PrescriptionDetail>
                      <PrescriptionDetail theme={theme}>
                        🏥 {prescription.clinic || 'NeuroCare Clinic'}
                      </PrescriptionDetail>
                      {prescription.notes && (
                        <PrescriptionDetail theme={theme}>
                          📝 {prescription.notes}
                        </PrescriptionDetail>
                      )}
                    </PrescriptionDetails>
                  </PrescriptionInfo>
                  <StatusBadge status={prescription.status}>
                    {getStatusDisplay(prescription.status)}
                  </StatusBadge>
                </PrescriptionHeader>

                {prescription.medications && prescription.medications.length > 0 && (
                  <MedicationGrid>
                    {prescription.medications.slice(0, 3).map((medication, index) => (
                      <MedicationItem key={index} theme={theme}>
                        <MedicationItemName theme={theme}>
                          {medication.name}
                        </MedicationItemName>
                        <MedicationItemDetails theme={theme}>
                          <div>Dosage: {medication.dosage}</div>
                          <div>Frequency: {medication.frequency}</div>
                          {medication.duration && (
                            <div>Duration: {medication.duration}</div>
                          )}
                        </MedicationItemDetails>
                      </MedicationItem>
                    ))}
                    {prescription.medications.length > 3 && (
                      <MedicationItem theme={theme}>
                        <MedicationItemName theme={theme}>
                          +{prescription.medications.length - 3} more
                        </MedicationItemName>
                        <MedicationItemDetails theme={theme}>
                          Click to view all medications
                        </MedicationItemDetails>
                      </MedicationItem>
                    )}
                  </MedicationGrid>
                )}

                <PrescriptionActions>
                  <Button
                    variant="outline"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handlePrescriptionClick(prescription);
                    }}
                  >
                    View Details
                  </Button>

                  {prescription.status === PRESCRIPTION_STATUS.SENT && (
                    <>
                      <Button
                        variant="primary"
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMarkAsFilled(prescription);
                        }}
                      >
                        Mark as Filled
                      </Button>
                      <Button
                        variant="success"
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAddToMedications(prescription);
                        }}
                      >
                        Add to Medications
                      </Button>
                    </>
                  )}
                </PrescriptionActions>
              </PrescriptionCard>
            ))}
          </PrescriptionsList>
        ) : (
          <EmptyState theme={theme}>
            <EmptyIcon>💊</EmptyIcon>
            <EmptyTitle theme={theme}>
              {filter === 'all' ? 'No prescriptions yet' : `No ${filter} prescriptions`}
            </EmptyTitle>
            <EmptyMessage theme={theme}>
              {filter === 'all'
                ? "You don't have any prescriptions yet."
                : `No ${filter} prescriptions found.`
              }
            </EmptyMessage>
            {user?.role === 'patient' && (
              <Button
                variant="primary"
                onClick={() => navigate('/appointments/new')}
              >
                Schedule an Appointment
              </Button>
            )}
          </EmptyState>
        )}

        {/* Prescription Details Modal */}
        <Modal
          isOpen={showDetailsModal}
          onClose={() => setShowDetailsModal(false)}
          title="Prescription Details"
          maxWidth="600px"
        >
          {selectedPrescription && (
            <div>
              <div style={{ marginBottom: '24px' }}>
                <h3 style={{ margin: '0 0 8px 0', color: theme.colors.text }}>
                  Dr. {selectedPrescription.doctorName}
                </h3>
                <p style={{ margin: '0', color: theme.colors.textSecondary }}>
                  Prescribed on {formatDate(selectedPrescription.prescribedDate, 'long')}
                </p>
                <StatusBadge status={selectedPrescription.status} style={{ marginTop: '8px' }}>
                  {getStatusDisplay(selectedPrescription.status)}
                </StatusBadge>
              </div>

              {selectedPrescription.medications && (
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ margin: '0 0 16px 0', color: theme.colors.text }}>
                    Medications ({selectedPrescription.medications.length})
                  </h4>
                  {selectedPrescription.medications.map((medication, index) => (
                    <div
                      key={index}
                      style={{
                        background: theme.colors.lightGray,
                        borderRadius: '8px',
                        padding: '16px',
                        marginBottom: '12px'
                      }}
                    >
                      <h5 style={{ margin: '0 0 8px 0', color: theme.colors.text }}>
                        {medication.name}
                      </h5>
                      <div style={{ fontSize: '14px', color: theme.colors.textSecondary }}>
                        <div>Dosage: {medication.dosage}</div>
                        <div>Frequency: {medication.frequency}</div>
                        {medication.duration && <div>Duration: {medication.duration}</div>}
                        {medication.instructions && <div>Instructions: {medication.instructions}</div>}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {selectedPrescription.notes && (
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ margin: '0 0 8px 0', color: theme.colors.text }}>Notes</h4>
                  <p style={{
                    margin: '0',
                    color: theme.colors.textSecondary,
                    background: theme.colors.lightGray,
                    padding: '12px',
                    borderRadius: '8px'
                  }}>
                    {selectedPrescription.notes}
                  </p>
                </div>
              )}

              <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
                {selectedPrescription.status === PRESCRIPTION_STATUS.SENT && (
                  <>
                    <Button
                      variant="primary"
                      onClick={() => handleMarkAsFilled(selectedPrescription)}
                    >
                      Mark as Filled
                    </Button>
                    <Button
                      variant="success"
                      onClick={() => handleAddToMedications(selectedPrescription)}
                    >
                      Add to Medications
                    </Button>
                  </>
                )}
                <Button
                  variant="outline"
                  onClick={() => setShowDetailsModal(false)}
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </Modal>
      </PrescriptionsContainer>
    </DashboardLayout>
  );
};

export default PrescriptionsScreen;
