import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';

const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary} 0%, ${props => props.theme.colors.primaryLight} 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
`;

const CompletionCard = styled.div`
  background: white;
  border-radius: 20px;
  padding: 40px;
  width: 100%;
  max-width: 600px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 32px;
`;

const Logo = styled.div`
  width: 60px;
  height: 60px;
  background-color: ${props => props.theme.colors.primary};
  border-radius: 15px;
  margin: 0 auto 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &::before {
    content: '👤';
    font-size: 30px;
  }
`;

const Title = styled.h1`
  font-size: 28px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const Subtitle = styled.p`
  font-size: 16px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
  line-height: 1.5;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background-color: ${props => props.theme.colors.lightGray};
  border-radius: 4px;
  margin: 24px 0;
  overflow: hidden;
`;

const ProgressFill = styled.div`
  height: 100%;
  background: linear-gradient(90deg, ${props => props.theme.colors.primary} 0%, ${props => props.theme.colors.primaryLight} 100%);
  width: ${props => props.percentage}%;
  transition: width 0.3s ease;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const Input = styled.input`
  padding: 12px 16px;
  border: 2px solid ${props => props.theme.colors.border};
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const Select = styled.select`
  padding: 12px 16px;
  border: 2px solid ${props => props.theme.colors.border};
  border-radius: 8px;
  font-size: 16px;
  background-color: white;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const TextArea = styled.textarea`
  padding: 12px 16px;
  border: 2px solid ${props => props.theme.colors.border};
  border-radius: 8px;
  font-size: 16px;
  min-height: 80px;
  resize: vertical;
  font-family: inherit;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 16px;
  justify-content: space-between;
  margin-top: 24px;
`;

const Button = styled.button`
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  flex: 1;

  &.primary {
    background: linear-gradient(135deg, ${props => props.theme.colors.primary} 0%, ${props => props.theme.colors.primaryDark} 100%);
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(76, 175, 80, 0.3);
    }
  }

  &.secondary {
    background: transparent;
    color: ${props => props.theme.colors.textSecondary};
    border: 2px solid ${props => props.theme.colors.border};

    &:hover {
      background: ${props => props.theme.colors.lightGray};
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const ProfileCompletion = () => {
  const { user, updateUserProfile } = useAuth();
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);
  const totalSteps = user?.role === 'patient' ? 3 : 2;

  const [formData, setFormData] = useState({
    phone: '',
    dateOfBirth: '',
    gender: '',
    address: '',
    emergencyContact: '',
    medicalHistory: '',
    allergies: '',
    currentMedications: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNext = () => {
    if (step < totalSteps) {
      setStep(step + 1);
    }
  };

  const handlePrevious = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      await updateUserProfile({
        ...formData,
        profileComplete: true
      });
      toast.success('Profile completed successfully!');
      navigate('/dashboard');
    } catch (error) {
      console.error('Profile completion error:', error);
      toast.error('Failed to complete profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderStep = () => {
    switch (step) {
      case 1:
        return (
          <>
            <FormRow>
              <FormGroup>
                <Label theme={theme}>Phone Number *</Label>
                <Input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  theme={theme}
                  placeholder="Enter your phone number"
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label theme={theme}>Date of Birth *</Label>
                <Input
                  type="date"
                  name="dateOfBirth"
                  value={formData.dateOfBirth}
                  onChange={handleInputChange}
                  theme={theme}
                  required
                />
              </FormGroup>
            </FormRow>

            <FormRow>
              <FormGroup>
                <Label theme={theme}>Gender</Label>
                <Select
                  name="gender"
                  value={formData.gender}
                  onChange={handleInputChange}
                  theme={theme}
                >
                  <option value="">Select Gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                  <option value="prefer-not-to-say">Prefer not to say</option>
                </Select>
              </FormGroup>

              <FormGroup>
                <Label theme={theme}>Emergency Contact *</Label>
                <Input
                  type="text"
                  name="emergencyContact"
                  value={formData.emergencyContact}
                  onChange={handleInputChange}
                  theme={theme}
                  placeholder="Name and phone number"
                  required
                />
              </FormGroup>
            </FormRow>
          </>
        );

      case 2:
        return (
          <FormGroup>
            <Label theme={theme}>Address *</Label>
            <TextArea
              name="address"
              value={formData.address}
              onChange={handleInputChange}
              theme={theme}
              placeholder="Enter your full address"
              required
            />
          </FormGroup>
        );

      case 3:
        return user?.role === 'patient' ? (
          <>
            <FormGroup>
              <Label theme={theme}>Medical History</Label>
              <TextArea
                name="medicalHistory"
                value={formData.medicalHistory}
                onChange={handleInputChange}
                theme={theme}
                placeholder="Brief medical history and conditions (optional)"
              />
            </FormGroup>

            <FormRow>
              <FormGroup>
                <Label theme={theme}>Allergies</Label>
                <TextArea
                  name="allergies"
                  value={formData.allergies}
                  onChange={handleInputChange}
                  theme={theme}
                  placeholder="List any known allergies (optional)"
                />
              </FormGroup>

              <FormGroup>
                <Label theme={theme}>Current Medications</Label>
                <TextArea
                  name="currentMedications"
                  value={formData.currentMedications}
                  onChange={handleInputChange}
                  theme={theme}
                  placeholder="List current medications (optional)"
                />
              </FormGroup>
            </FormRow>
          </>
        ) : null;

      default:
        return null;
    }
  };

  const getStepTitle = () => {
    switch (step) {
      case 1:
        return 'Personal Information';
      case 2:
        return 'Contact Details';
      case 3:
        return 'Medical Information';
      default:
        return 'Profile Setup';
    }
  };

  const isStepValid = () => {
    switch (step) {
      case 1:
        return formData.phone && formData.dateOfBirth && formData.emergencyContact;
      case 2:
        return formData.address;
      case 3:
        return true; // Medical info is optional
      default:
        return false;
    }
  };

  return (
    <Container theme={theme}>
      <CompletionCard>
        <Header>
          <Logo theme={theme} />
          <Title theme={theme}>Complete Your Profile</Title>
          <Subtitle theme={theme}>
            Step {step} of {totalSteps}: {getStepTitle()}
          </Subtitle>
        </Header>

        <ProgressBar theme={theme}>
          <ProgressFill 
            percentage={(step / totalSteps) * 100} 
            theme={theme}
          />
        </ProgressBar>

        <Form onSubmit={step === totalSteps ? handleSubmit : (e) => { e.preventDefault(); handleNext(); }}>
          {renderStep()}

          <ButtonGroup>
            {step > 1 && (
              <Button 
                type="button" 
                className="secondary" 
                theme={theme}
                onClick={handlePrevious}
              >
                Previous
              </Button>
            )}
            
            {step < totalSteps ? (
              <Button 
                type="submit" 
                className="primary" 
                theme={theme}
                disabled={!isStepValid()}
              >
                Next
              </Button>
            ) : (
              <Button 
                type="submit" 
                className="primary" 
                theme={theme}
                disabled={loading || !isStepValid()}
              >
                {loading ? 'Completing...' : 'Complete Profile'}
              </Button>
            )}
          </ButtonGroup>
        </Form>
      </CompletionCard>
    </Container>
  );
};

export default ProfileCompletion;
