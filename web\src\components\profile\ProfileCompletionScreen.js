import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Input, Select } from '../common';

const ProfileCompletionContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 32px;
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const Subtitle = styled.p`
  font-size: 16px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
`;

const FormCard = styled(Card)`
  padding: 32px;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid ${props => props.theme.colors.borderLight};
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const FullWidthField = styled.div`
  grid-column: 1 / -1;
`;

const RequiredIndicator = styled.span`
  color: ${props => props.theme.colors.error};
  margin-left: 4px;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 32px;
`;

const ProgressIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 24px;
  padding: 16px;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 12px;
`;

const ProgressBar = styled.div`
  flex: 1;
  height: 8px;
  background: ${props => props.theme.colors.border};
  border-radius: 4px;
  overflow: hidden;
`;

const ProgressFill = styled.div`
  height: 100%;
  background: ${props => props.theme.colors.primary};
  width: ${props => props.progress}%;
  transition: width 0.3s ease;
`;

const ProfileCompletionScreen = () => {
  const { user, updateUserProfile } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    phone: user?.phone || '',
    dateOfBirth: user?.dateOfBirth || '',
    gender: user?.gender || '',
    address: user?.address || '',
    city: user?.city || '',
    postalCode: user?.postalCode || '',
    country: user?.country || 'France',
    emergencyContactName: user?.emergencyContactName || '',
    emergencyContactPhone: user?.emergencyContactPhone || '',
    emergencyContactRelation: user?.emergencyContactRelation || '',
    medicalConditions: user?.medicalConditions || '',
    allergies: user?.allergies || '',
    medications: user?.medications || '',
    preferredLanguage: user?.preferredLanguage || 'French'
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  const requiredFields = [
    'firstName', 'lastName', 'email', 'phone', 'dateOfBirth', 
    'gender', 'emergencyContactName', 'emergencyContactPhone'
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    requiredFields.forEach(field => {
      if (!formData[field] || formData[field].trim() === '') {
        newErrors[field] = 'This field is required';
      }
    });

    // Email validation
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Phone validation
    if (formData.phone && !/^\+?[\d\s\-\(\)]{10,}$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    // Emergency contact phone validation
    if (formData.emergencyContactPhone && !/^\+?[\d\s\-\(\)]{10,}$/.test(formData.emergencyContactPhone)) {
      newErrors.emergencyContactPhone = 'Please enter a valid phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fill in all required fields correctly');
      return;
    }

    try {
      setLoading(true);

      await updateUserProfile({
        ...formData,
        profileCompleted: true,
        updatedAt: new Date().toISOString()
      });

      toast.success('Profile completed successfully!');
      
      setTimeout(() => {
        navigate('/dashboard');
      }, 1000);

    } catch (error) {
      console.error('Error completing profile:', error);
      toast.error(error.message || 'Failed to complete profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const calculateProgress = () => {
    const filledFields = requiredFields.filter(field => 
      formData[field] && formData[field].trim() !== ''
    ).length;
    return Math.round((filledFields / requiredFields.length) * 100);
  };

  const progress = calculateProgress();

  return (
    <DashboardLayout
      title="Complete Profile"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <ProfileCompletionContainer>
        <Header>
          <Title theme={theme}>Complete Your Profile</Title>
          <Subtitle theme={theme}>
            Please provide the following information to complete your profile and access all features.
          </Subtitle>
        </Header>

        <FormCard theme={theme}>
          <ProgressIndicator theme={theme}>
            <span style={{ fontSize: '14px', fontWeight: '600' }}>
              Profile Completion: {progress}%
            </span>
            <ProgressBar theme={theme}>
              <ProgressFill progress={progress} theme={theme} />
            </ProgressBar>
          </ProgressIndicator>

          <form onSubmit={handleSubmit}>
            <FormSection>
              <SectionTitle theme={theme}>Personal Information</SectionTitle>
              <FormRow>
                <Input
                  label="First Name"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  error={errors.firstName}
                  required
                  disabled
                />
                <Input
                  label="Last Name"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  error={errors.lastName}
                  required
                  disabled
                />
              </FormRow>
              <FormRow>
                <Input
                  label="Email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  error={errors.email}
                  required
                  disabled
                />
                <Input
                  label="Phone Number"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  error={errors.phone}
                  placeholder="+33 1 23 45 67 89"
                  required
                />
              </FormRow>
              <FormRow>
                <Input
                  label="Date of Birth"
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                  error={errors.dateOfBirth}
                  required
                />
                <Select
                  label="Gender"
                  value={formData.gender}
                  onChange={(value) => handleInputChange('gender', value)}
                  error={errors.gender}
                  options={[
                    { value: '', label: 'Select Gender' },
                    { value: 'male', label: 'Male' },
                    { value: 'female', label: 'Female' },
                    { value: 'other', label: 'Other' },
                    { value: 'prefer_not_to_say', label: 'Prefer not to say' }
                  ]}
                  required
                />
              </FormRow>
            </FormSection>

            <FormSection>
              <SectionTitle theme={theme}>Address Information</SectionTitle>
              <FullWidthField>
                <Input
                  label="Address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  error={errors.address}
                  placeholder="Street address"
                />
              </FullWidthField>
              <FormRow>
                <Input
                  label="City"
                  value={formData.city}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  error={errors.city}
                />
                <Input
                  label="Postal Code"
                  value={formData.postalCode}
                  onChange={(e) => handleInputChange('postalCode', e.target.value)}
                  error={errors.postalCode}
                />
              </FormRow>
              <Input
                label="Country"
                value={formData.country}
                onChange={(e) => handleInputChange('country', e.target.value)}
                error={errors.country}
              />
            </FormSection>

            <FormSection>
              <SectionTitle theme={theme}>Emergency Contact <RequiredIndicator>*</RequiredIndicator></SectionTitle>
              <FormRow>
                <Input
                  label="Contact Name"
                  value={formData.emergencyContactName}
                  onChange={(e) => handleInputChange('emergencyContactName', e.target.value)}
                  error={errors.emergencyContactName}
                  required
                />
                <Input
                  label="Contact Phone"
                  value={formData.emergencyContactPhone}
                  onChange={(e) => handleInputChange('emergencyContactPhone', e.target.value)}
                  error={errors.emergencyContactPhone}
                  placeholder="+33 1 23 45 67 89"
                  required
                />
              </FormRow>
              <Select
                label="Relationship"
                value={formData.emergencyContactRelation}
                onChange={(value) => handleInputChange('emergencyContactRelation', value)}
                error={errors.emergencyContactRelation}
                options={[
                  { value: '', label: 'Select Relationship' },
                  { value: 'spouse', label: 'Spouse' },
                  { value: 'parent', label: 'Parent' },
                  { value: 'child', label: 'Child' },
                  { value: 'sibling', label: 'Sibling' },
                  { value: 'friend', label: 'Friend' },
                  { value: 'other', label: 'Other' }
                ]}
              />
            </FormSection>

            <FormSection>
              <SectionTitle theme={theme}>Medical Information (Optional)</SectionTitle>
              <Input
                label="Medical Conditions"
                value={formData.medicalConditions}
                onChange={(e) => handleInputChange('medicalConditions', e.target.value)}
                error={errors.medicalConditions}
                multiline
                rows={3}
                placeholder="List any medical conditions..."
              />
              <Input
                label="Allergies"
                value={formData.allergies}
                onChange={(e) => handleInputChange('allergies', e.target.value)}
                error={errors.allergies}
                multiline
                rows={2}
                placeholder="List any allergies..."
              />
              <Input
                label="Current Medications"
                value={formData.medications}
                onChange={(e) => handleInputChange('medications', e.target.value)}
                error={errors.medications}
                multiline
                rows={3}
                placeholder="List current medications..."
              />
            </FormSection>

            <FormSection>
              <SectionTitle theme={theme}>Preferences</SectionTitle>
              <Select
                label="Preferred Language"
                value={formData.preferredLanguage}
                onChange={(value) => handleInputChange('preferredLanguage', value)}
                options={[
                  { value: 'French', label: 'Français' },
                  { value: 'English', label: 'English' },
                  { value: 'Spanish', label: 'Español' },
                  { value: 'German', label: 'Deutsch' },
                  { value: 'Italian', label: 'Italiano' }
                ]}
              />
            </FormSection>

            <ButtonGroup>
              <Button
                variant="outline"
                onClick={() => navigate('/dashboard')}
                disabled={loading}
              >
                Skip for Now
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={loading}
                disabled={progress < 80}
              >
                Complete Profile
              </Button>
            </ButtonGroup>
          </form>
        </FormCard>
      </ProfileCompletionContainer>
    </DashboardLayout>
  );
};

export default ProfileCompletionScreen;
