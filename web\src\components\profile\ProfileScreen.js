import React, { useState } from 'react';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';

const ProfileContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const ProfileCard = styled.div`
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: ${props => props.theme.shadows.md};
  margin-bottom: 24px;
`;

const ProfileHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const Avatar = styled.div`
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary} 0%, ${props => props.theme.colors.primaryLight} 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 36px;
  font-weight: 700;
  position: relative;
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
`;

const AvatarUpload = styled.input`
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
`;

const UserInfo = styled.div`
  flex: 1;
`;

const UserName = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const UserRole = styled.p`
  font-size: 18px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0 0 16px 0;
  text-transform: capitalize;
`;

const UserCode = styled.div`
  background: ${props => props.theme.colors.lightGray};
  padding: 8px 16px;
  border-radius: 8px;
  display: inline-block;
  font-family: monospace;
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const Form = styled.form`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const Input = styled.input`
  padding: 12px 16px;
  border: 2px solid ${props => props.theme.colors.border};
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }

  &:disabled {
    background-color: ${props => props.theme.colors.lightGray};
    cursor: not-allowed;
  }
`;

const Select = styled.select`
  padding: 12px 16px;
  border: 2px solid ${props => props.theme.colors.border};
  border-radius: 8px;
  font-size: 16px;
  background-color: white;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const TextArea = styled.textarea`
  padding: 12px 16px;
  border: 2px solid ${props => props.theme.colors.border};
  border-radius: 8px;
  font-size: 16px;
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 32px;
`;

const Button = styled.button`
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;

  &.primary {
    background: ${props => props.theme.colors.primary};
    color: white;

    &:hover:not(:disabled) {
      background: ${props => props.theme.colors.primaryDark};
      transform: translateY(-2px);
    }
  }

  &.secondary {
    background: transparent;
    color: ${props => props.theme.colors.textSecondary};
    border: 2px solid ${props => props.theme.colors.border};

    &:hover {
      background: ${props => props.theme.colors.lightGray};
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const ProfileScreen = () => {
  const { user, updateUserProfile } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    displayName: user?.displayName || '',
    email: user?.email || '',
    phone: user?.phone || '',
    dateOfBirth: user?.dateOfBirth || '',
    gender: user?.gender || '',
    address: user?.address || '',
    emergencyContact: user?.emergencyContact || '',
    medicalHistory: user?.medicalHistory || '',
    allergies: user?.allergies || '',
    currentMedications: user?.currentMedications || ''
  });

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', screen: 'dashboard' },
    { label: 'Profile', icon: '👤', screen: 'profile' },
    { label: 'Settings', icon: '⚙️', screen: 'settings' },
    { label: 'QR Code', icon: '📱', screen: 'qr-code' }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      await updateUserProfile({
        ...formData,
        profileComplete: true
      });
      toast.success('Profile updated successfully!');
    } catch (error) {
      console.error('Profile update error:', error);
      toast.error('Failed to update profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getUserInitials = () => {
    if (user?.displayName) {
      return user.displayName
        .split(' ')
        .map(name => name[0])
        .join('')
        .toUpperCase()
        .slice(0, 2);
    }
    return user?.email?.[0]?.toUpperCase() || 'U';
  };

  return (
    <DashboardLayout
      title="My Profile"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <ProfileContainer>
        <ProfileCard theme={theme}>
          <ProfileHeader theme={theme}>
            <Avatar theme={theme}>
              {getUserInitials()}
              <AvatarUpload 
                type="file" 
                accept="image/*"
                title="Click to upload profile picture"
              />
            </Avatar>
            <UserInfo>
              <UserName theme={theme}>{user?.displayName || 'User'}</UserName>
              <UserRole theme={theme}>{user?.role || 'Patient'}</UserRole>
              <div>
                <Label theme={theme}>User Code:</Label>
                <UserCode theme={theme}>{user?.userCode || 'N/A'}</UserCode>
              </div>
            </UserInfo>
          </ProfileHeader>

          <Form onSubmit={handleSubmit}>
            <FormGroup>
              <Label theme={theme}>Full Name</Label>
              <Input
                type="text"
                name="displayName"
                value={formData.displayName}
                onChange={handleInputChange}
                theme={theme}
                required
              />
            </FormGroup>

            <FormGroup>
              <Label theme={theme}>Email Address</Label>
              <Input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                theme={theme}
                disabled
              />
            </FormGroup>

            <FormGroup>
              <Label theme={theme}>Phone Number</Label>
              <Input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                theme={theme}
                placeholder="Enter your phone number"
              />
            </FormGroup>

            <FormGroup>
              <Label theme={theme}>Date of Birth</Label>
              <Input
                type="date"
                name="dateOfBirth"
                value={formData.dateOfBirth}
                onChange={handleInputChange}
                theme={theme}
              />
            </FormGroup>

            <FormGroup>
              <Label theme={theme}>Gender</Label>
              <Select
                name="gender"
                value={formData.gender}
                onChange={handleInputChange}
                theme={theme}
              >
                <option value="">Select Gender</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="other">Other</option>
                <option value="prefer-not-to-say">Prefer not to say</option>
              </Select>
            </FormGroup>

            <FormGroup>
              <Label theme={theme}>Emergency Contact</Label>
              <Input
                type="text"
                name="emergencyContact"
                value={formData.emergencyContact}
                onChange={handleInputChange}
                theme={theme}
                placeholder="Name and phone number"
              />
            </FormGroup>

            <FormGroup style={{ gridColumn: '1 / -1' }}>
              <Label theme={theme}>Address</Label>
              <TextArea
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                theme={theme}
                placeholder="Enter your full address"
              />
            </FormGroup>

            {user?.role === 'patient' && (
              <>
                <FormGroup style={{ gridColumn: '1 / -1' }}>
                  <Label theme={theme}>Medical History</Label>
                  <TextArea
                    name="medicalHistory"
                    value={formData.medicalHistory}
                    onChange={handleInputChange}
                    theme={theme}
                    placeholder="Brief medical history and conditions"
                  />
                </FormGroup>

                <FormGroup>
                  <Label theme={theme}>Allergies</Label>
                  <TextArea
                    name="allergies"
                    value={formData.allergies}
                    onChange={handleInputChange}
                    theme={theme}
                    placeholder="List any known allergies"
                  />
                </FormGroup>

                <FormGroup>
                  <Label theme={theme}>Current Medications</Label>
                  <TextArea
                    name="currentMedications"
                    value={formData.currentMedications}
                    onChange={handleInputChange}
                    theme={theme}
                    placeholder="List current medications and dosages"
                  />
                </FormGroup>
              </>
            )}

            <ButtonGroup>
              <Button 
                type="button" 
                className="secondary" 
                theme={theme}
                onClick={() => window.history.back()}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                className="primary" 
                theme={theme}
                disabled={loading}
              >
                {loading ? 'Saving...' : 'Save Profile'}
              </Button>
            </ButtonGroup>
          </Form>
        </ProfileCard>
      </ProfileContainer>
    </DashboardLayout>
  );
};

export default ProfileScreen;
