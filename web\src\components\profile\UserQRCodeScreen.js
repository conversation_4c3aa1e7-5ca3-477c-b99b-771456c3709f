import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import QRCode from 'qrcode';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';

const QRContainer = styled.div`
  max-width: 600px;
  margin: 0 auto;
`;

const QRCard = styled.div`
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: ${props => props.theme.shadows.md};
  text-align: center;
`;

const QRHeader = styled.div`
  margin-bottom: 32px;
`;

const QRTitle = styled.h1`
  font-size: 28px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const QRSubtitle = styled.p`
  font-size: 16px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
  line-height: 1.5;
`;

const QRCodeContainer = styled.div`
  background: ${props => props.theme.colors.lightGray};
  border-radius: 16px;
  padding: 32px;
  margin: 32px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
`;

const QRCodeImage = styled.img`
  width: 256px;
  height: 256px;
  border-radius: 12px;
  background: white;
  padding: 16px;
  box-shadow: ${props => props.theme.shadows.sm};
`;

const UserInfo = styled.div`
  text-align: center;
`;

const UserName = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const UserDetails = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const UserDetail = styled.p`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
`;

const UserCode = styled.div`
  background: ${props => props.theme.colors.primary};
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-family: monospace;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 2px;
  margin: 16px 0;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
`;

const ActionButton = styled.button`
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;

  &.primary {
    background: ${props => props.theme.colors.primary};
    color: white;

    &:hover {
      background: ${props => props.theme.colors.primaryDark};
      transform: translateY(-2px);
    }
  }

  &.secondary {
    background: transparent;
    color: ${props => props.theme.colors.textSecondary};
    border: 2px solid ${props => props.theme.colors.border};

    &:hover {
      background: ${props => props.theme.colors.lightGray};
    }
  }
`;

const InstructionsCard = styled.div`
  background: ${props => props.theme.colors.lightGray};
  border-radius: 12px;
  padding: 24px;
  margin-top: 32px;
  text-align: left;
`;

const InstructionsTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 16px 0;
`;

const InstructionsList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const InstructionItem = styled.li`
  display: flex;
  align-items: flex-start;
  gap: 12px;
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.5;
`;

const InstructionIcon = styled.span`
  font-size: 16px;
  flex-shrink: 0;
  margin-top: 2px;
`;

const UserQRCodeScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [loading, setLoading] = useState(true);

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', screen: 'dashboard' },
    { label: 'Profile', icon: '👤', screen: 'profile' },
    { label: 'QR Code', icon: '📱', screen: 'qr-code' },
    { label: 'Settings', icon: '⚙️', screen: 'settings' }
  ];

  useEffect(() => {
    generateQRCode();
  }, [user]);

  const generateQRCode = async () => {
    if (!user) return;

    try {
      setLoading(true);
      
      const qrData = {
        userCode: user.userCode,
        name: user.displayName,
        email: user.email,
        role: user.role,
        id: user.uid
      };

      const qrCodeDataUrl = await QRCode.toDataURL(JSON.stringify(qrData), {
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });

      setQrCodeUrl(qrCodeDataUrl);
    } catch (error) {
      console.error('Error generating QR code:', error);
      toast.error('Failed to generate QR code');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = () => {
    if (!qrCodeUrl) return;

    const link = document.createElement('a');
    link.download = `neurocare-qr-${user?.userCode || 'user'}.png`;
    link.href = qrCodeUrl;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast.success('QR Code downloaded successfully!');
  };

  const handleShare = async () => {
    if (!navigator.share) {
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(`NeuroCare User Code: ${user?.userCode}`);
        toast.success('User code copied to clipboard!');
      } catch (error) {
        toast.error('Failed to copy user code');
      }
      return;
    }

    try {
      await navigator.share({
        title: 'My NeuroCare QR Code',
        text: `Connect with me on NeuroCare using code: ${user?.userCode}`,
        url: window.location.href
      });
    } catch (error) {
      if (error.name !== 'AbortError') {
        toast.error('Failed to share QR code');
      }
    }
  };

  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>NeuroCare QR Code - ${user?.displayName}</title>
          <style>
            body { 
              font-family: Arial, sans-serif; 
              text-align: center; 
              padding: 40px;
              background: white;
            }
            .qr-container {
              max-width: 400px;
              margin: 0 auto;
              border: 2px solid #ddd;
              border-radius: 12px;
              padding: 32px;
            }
            .qr-image {
              width: 256px;
              height: 256px;
              margin: 20px 0;
            }
            .user-code {
              background: ${roleColors.primary};
              color: white;
              padding: 12px 24px;
              border-radius: 8px;
              font-family: monospace;
              font-size: 18px;
              font-weight: bold;
              letter-spacing: 2px;
              margin: 16px 0;
              display: inline-block;
            }
          </style>
        </head>
        <body>
          <div class="qr-container">
            <h1>NeuroCare QR Code</h1>
            <h2>${user?.displayName || 'User'}</h2>
            <p>${user?.role?.toUpperCase() || 'USER'}</p>
            <img src="${qrCodeUrl}" alt="QR Code" class="qr-image" />
            <div class="user-code">${user?.userCode || 'N/A'}</div>
            <p>Scan this QR code to connect on NeuroCare</p>
          </div>
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  return (
    <DashboardLayout
      title="My QR Code"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <QRContainer>
        <QRCard theme={theme}>
          <QRHeader>
            <QRTitle theme={theme}>Your NeuroCare QR Code</QRTitle>
            <QRSubtitle theme={theme}>
              Share this QR code to connect with healthcare providers, caregivers, or supervisors
            </QRSubtitle>
          </QRHeader>

          <QRCodeContainer theme={theme}>
            {loading ? (
              <div style={{ 
                width: '256px', 
                height: '256px', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                background: 'white',
                borderRadius: '12px'
              }}>
                <div>Generating QR Code...</div>
              </div>
            ) : (
              <QRCodeImage src={qrCodeUrl} alt="User QR Code" theme={theme} />
            )}

            <UserInfo>
              <UserName theme={theme}>{user?.displayName || 'User'}</UserName>
              <UserDetails>
                <UserDetail theme={theme}>
                  Role: {user?.role?.toUpperCase() || 'USER'}
                </UserDetail>
                <UserDetail theme={theme}>
                  Email: {user?.email || 'N/A'}
                </UserDetail>
              </UserDetails>
              <UserCode theme={theme}>
                {user?.userCode || 'N/A'}
              </UserCode>
            </UserInfo>
          </QRCodeContainer>

          <ActionButtons>
            <ActionButton 
              className="primary" 
              onClick={handleDownload}
              theme={theme}
              disabled={loading}
            >
              📥 Download
            </ActionButton>
            <ActionButton 
              className="secondary" 
              onClick={handleShare}
              theme={theme}
            >
              📤 Share
            </ActionButton>
            <ActionButton 
              className="secondary" 
              onClick={handlePrint}
              theme={theme}
              disabled={loading}
            >
              🖨️ Print
            </ActionButton>
          </ActionButtons>

          <InstructionsCard theme={theme}>
            <InstructionsTitle theme={theme}>How to use your QR Code</InstructionsTitle>
            <InstructionsList>
              <InstructionItem theme={theme}>
                <InstructionIcon>📱</InstructionIcon>
                <span>Show this QR code to healthcare providers to quickly share your information</span>
              </InstructionItem>
              <InstructionItem theme={theme}>
                <InstructionIcon>🤝</InstructionIcon>
                <span>Caregivers and supervisors can scan this code to connect with you</span>
              </InstructionItem>
              <InstructionItem theme={theme}>
                <InstructionIcon>🔒</InstructionIcon>
                <span>Your QR code contains only basic identification information - medical data remains secure</span>
              </InstructionItem>
              <InstructionItem theme={theme}>
                <InstructionIcon>💾</InstructionIcon>
                <span>Save or print this QR code for offline use when needed</span>
              </InstructionItem>
            </InstructionsList>
          </InstructionsCard>
        </QRCard>
      </QRContainer>
    </DashboardLayout>
  );
};

export default UserQRCodeScreen;
