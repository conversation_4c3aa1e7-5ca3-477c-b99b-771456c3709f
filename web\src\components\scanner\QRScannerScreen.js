import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Input, Modal } from '../common';
import { userService } from '../../services/firebaseService';

const ScannerContainer = styled.div`
  max-width: 600px;
  margin: 0 auto;
`;

const ScannerCard = styled(Card)`
  text-align: center;
  margin-bottom: 24px;
`;

const ScannerArea = styled.div`
  width: 100%;
  height: 300px;
  background: ${props => props.theme.colors.lightGray};
  border: 2px dashed ${props => props.theme.colors.border};
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 24px 0;
  position: relative;
  overflow: hidden;
`;

const VideoElement = styled.video`
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
`;

const ScannerOverlay = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border: 2px solid ${props => props.theme.colors.primary};
  border-radius: 12px;
  
  &::before,
  &::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border: 3px solid ${props => props.theme.colors.primary};
  }
  
  &::before {
    top: -3px;
    left: -3px;
    border-right: none;
    border-bottom: none;
  }
  
  &::after {
    bottom: -3px;
    right: -3px;
    border-left: none;
    border-top: none;
  }
`;

const ScannerIcon = styled.div`
  font-size: 64px;
  color: ${props => props.theme.colors.textSecondary};
  margin-bottom: 16px;
`;

const ScannerText = styled.p`
  font-size: 16px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
`;

const ManualInputSection = styled.div`
  margin-top: 32px;
`;

const SectionTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 16px 0;
  text-align: center;
`;

const InputGroup = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 24px;
`;

const QRScannerScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();
  const videoRef = useRef(null);
  const canvasRef = useRef(null);

  const [scanning, setScanning] = useState(false);
  const [manualCode, setManualCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [showUserModal, setShowUserModal] = useState(false);
  const [foundUser, setFoundUser] = useState(null);
  const [stream, setStream] = useState(null);

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Scanner', icon: '📱', path: '/scanner' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  useEffect(() => {
    return () => {
      // Cleanup camera stream on unmount
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, [stream]);

  const startCamera = async () => {
    try {
      setScanning(true);
      
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: { 
          facingMode: 'environment', // Use back camera if available
          width: { ideal: 640 },
          height: { ideal: 480 }
        }
      });
      
      setStream(mediaStream);
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        videoRef.current.play();
      }
      
      // Start scanning for QR codes
      startQRDetection();
      
    } catch (error) {
      console.error('Error accessing camera:', error);
      toast.error('Unable to access camera. Please check permissions.');
      setScanning(false);
    }
  };

  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    setScanning(false);
  };

  const startQRDetection = () => {
    // This is a simplified QR detection
    // In a real implementation, you would use a library like jsQR
    const detectQR = () => {
      if (!scanning || !videoRef.current) return;
      
      // Mock QR detection for demo
      // In real implementation, capture video frame and analyze for QR codes
      
      setTimeout(detectQR, 100); // Check every 100ms
    };
    
    detectQR();
  };

  const handleQRCodeDetected = (qrData) => {
    try {
      // Parse QR code data
      const userData = JSON.parse(qrData);
      if (userData.userCode) {
        handleUserCodeSubmit(userData.userCode);
      }
    } catch (error) {
      toast.error('Invalid QR code format');
    }
  };

  const handleUserCodeSubmit = async (userCode = manualCode) => {
    if (!userCode.trim()) {
      toast.error('Please enter a user code');
      return;
    }

    try {
      setLoading(true);
      
      // Search for user by code
      const foundUserData = await userService.getByUserCode(userCode.trim().toUpperCase());
      
      if (foundUserData) {
        setFoundUser(foundUserData);
        setShowUserModal(true);
        stopCamera();
      } else {
        toast.error('User not found with this code');
      }
      
    } catch (error) {
      console.error('Error searching for user:', error);
      toast.error('Error searching for user');
    } finally {
      setLoading(false);
    }
  };

  const handleAddUser = async (relationship) => {
    if (!foundUser) return;

    try {
      setLoading(true);
      
      // Create relationship between users
      const relationshipData = {
        fromUserId: user.uid,
        fromUserRole: user.role,
        toUserId: foundUser.uid,
        toUserRole: foundUser.role,
        relationship: relationship,
        status: 'active',
        createdAt: new Date().toISOString()
      };

      // In a real implementation, you would save this to Firebase
      console.log('Creating relationship:', relationshipData);
      
      toast.success(`${foundUser.displayName} added successfully!`);
      setShowUserModal(false);
      setFoundUser(null);
      setManualCode('');
      
      // Navigate back to dashboard
      navigate('/dashboard');
      
    } catch (error) {
      console.error('Error adding user:', error);
      toast.error('Failed to add user');
    } finally {
      setLoading(false);
    }
  };

  const getRelationshipOptions = () => {
    switch (user.role) {
      case 'doctor':
        return [
          { value: 'doctor-patient', label: 'Add as Patient' }
        ];
      case 'supervisor':
        return [
          { value: 'supervisor-patient', label: 'Add as Patient' },
          { value: 'supervisor-caregiver', label: 'Add as Caregiver' }
        ];
      case 'caregiver':
        return [
          { value: 'caregiver-patient', label: 'Add as Patient' }
        ];
      default:
        return [];
    }
  };

  return (
    <DashboardLayout
      title="QR Scanner"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <ScannerContainer>
        <ScannerCard title="Scan QR Code" theme={theme}>
          <ScannerArea theme={theme}>
            {scanning ? (
              <>
                <VideoElement ref={videoRef} autoPlay playsInline />
                <ScannerOverlay theme={theme} />
              </>
            ) : (
              <>
                <ScannerIcon theme={theme}>📱</ScannerIcon>
                <ScannerText theme={theme}>
                  Click "Start Camera" to scan a QR code
                </ScannerText>
              </>
            )}
          </ScannerArea>

          <ActionButtons>
            {!scanning ? (
              <Button
                variant="primary"
                onClick={startCamera}
                icon="📷"
              >
                Start Camera
              </Button>
            ) : (
              <Button
                variant="outline"
                onClick={stopCamera}
                icon="⏹️"
              >
                Stop Camera
              </Button>
            )}
          </ActionButtons>
        </ScannerCard>

        <Card theme={theme}>
          <ManualInputSection>
            <SectionTitle theme={theme}>
              Or Enter User Code Manually
            </SectionTitle>
            
            <InputGroup>
              <Input
                placeholder="Enter user code (e.g., PAT12345)"
                value={manualCode}
                onChange={(e) => setManualCode(e.target.value.toUpperCase())}
                style={{ flex: 1 }}
              />
              <Button
                variant="primary"
                onClick={() => handleUserCodeSubmit()}
                loading={loading}
                disabled={!manualCode.trim()}
              >
                Search
              </Button>
            </InputGroup>
            
            <p style={{ 
              fontSize: '14px', 
              color: theme.colors.textSecondary,
              textAlign: 'center',
              margin: '16px 0 0 0'
            }}>
              Ask the user to show you their QR code from their profile or provide their user code.
            </p>
          </ManualInputSection>
        </Card>

        {/* User Found Modal */}
        <Modal
          isOpen={showUserModal}
          onClose={() => setShowUserModal(false)}
          title="User Found"
          maxWidth="500px"
        >
          {foundUser && (
            <div>
              <div style={{ 
                textAlign: 'center', 
                marginBottom: '24px',
                padding: '20px',
                background: theme.colors.lightGray,
                borderRadius: '12px'
              }}>
                <div style={{ 
                  width: '80px', 
                  height: '80px', 
                  borderRadius: '50%', 
                  background: roleColors.primary,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto 16px auto',
                  color: 'white',
                  fontSize: '32px',
                  fontWeight: '700'
                }}>
                  {foundUser.displayName?.charAt(0) || '?'}
                </div>
                <h3 style={{ margin: '0 0 8px 0', color: theme.colors.text }}>
                  {foundUser.displayName}
                </h3>
                <p style={{ margin: '0', color: theme.colors.textSecondary }}>
                  {foundUser.role} • {foundUser.userCode}
                </p>
              </div>

              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ margin: '0 0 16px 0', color: theme.colors.text }}>
                  How would you like to add this user?
                </h4>
                
                <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                  {getRelationshipOptions().map(option => (
                    <Button
                      key={option.value}
                      variant="outline"
                      onClick={() => handleAddUser(option.value)}
                      loading={loading}
                      style={{ justifyContent: 'flex-start' }}
                    >
                      {option.label}
                    </Button>
                  ))}
                </div>
              </div>

              <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
                <Button
                  variant="outline"
                  onClick={() => setShowUserModal(false)}
                  disabled={loading}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </Modal>
      </ScannerContainer>
    </DashboardLayout>
  );
};

export default QRScannerScreen;
