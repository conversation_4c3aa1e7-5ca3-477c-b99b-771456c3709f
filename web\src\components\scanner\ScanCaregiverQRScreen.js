import React, { useState, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useUsers } from '../../contexts/UsersContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Input, Modal } from '../common';

const ScannerContainer = styled.div`
  max-width: 600px;
  margin: 0 auto;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 32px;
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const Subtitle = styled.p`
  font-size: 16px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
`;

const ScannerCard = styled(Card)`
  padding: 32px;
  text-align: center;
  margin-bottom: 24px;
`;

const ScannerModeToggle = styled.div`
  display: flex;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 12px;
  padding: 4px;
  margin-bottom: 32px;
`;

const ModeButton = styled.button`
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  ${props => props.active ? `
    background: ${props.theme.colors.primary};
    color: white;
  ` : `
    background: transparent;
    color: ${props.theme.colors.textSecondary};
  `}
`;

const CameraSection = styled.div`
  margin-bottom: 32px;
`;

const CameraContainer = styled.div`
  position: relative;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 16px;
  overflow: hidden;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const CameraPlaceholder = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: ${props => props.theme.colors.textSecondary};
`;

const CameraIcon = styled.div`
  font-size: 64px;
`;

const CameraText = styled.p`
  font-size: 16px;
  margin: 0;
  text-align: center;
`;

const VideoElement = styled.video`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const ScanOverlay = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border: 3px solid ${props => props.theme.colors.primary};
  border-radius: 16px;
  
  &::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border: 3px solid ${props => props.theme.colors.primary};
    border-radius: 16px;
    animation: pulse 2s infinite;
  }
  
  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }
`;

const ManualEntrySection = styled.div`
  margin-bottom: 32px;
`;

const CodeInput = styled(Input)`
  text-align: center;
  font-family: monospace;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 2px;
  text-transform: uppercase;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
`;

const InstructionsCard = styled(Card)`
  padding: 24px;
  margin-top: 24px;
`;

const InstructionsTitle = styled.h4`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const InstructionsList = styled.ul`
  margin: 0;
  padding-left: 20px;
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.6;
`;

const ScanCaregiverQRScreen = () => {
  const { patientId } = useParams();
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const { assignPatientToUser, searchUsers } = useUsers();
  const navigate = useNavigate();

  const [scanMode, setScanMode] = useState('manual'); // 'camera' or 'manual'
  const [caregiverCode, setCaregiverCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [cameraActive, setCameraActive] = useState(false);
  const [foundCaregiver, setFoundCaregiver] = useState(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const videoRef = useRef(null);
  const canvasRef = useRef(null);

  const roleColors = getRoleColors(user?.role || 'supervisor');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Caregivers', icon: '👨‍⚕️', path: '/supervisor/caregivers' },
    { label: 'Patients', icon: '👥', path: '/supervisor/patients' },
    { label: 'Scanner', icon: '📱', path: '/scanner' }
  ];

  const isValidCode = (code) => {
    return /^[A-Z0-9]{8}$/.test(code);
  };

  const handleCodeChange = (e) => {
    const value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    if (value.length <= 8) {
      setCaregiverCode(value);
    }
  };

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' }
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setCameraActive(true);
        toast.success('Camera started. Position QR code in the frame.');
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      toast.error('Unable to access camera. Please use manual entry.');
      setScanMode('manual');
    }
  };

  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = videoRef.current.srcObject.getTracks();
      tracks.forEach(track => track.stop());
      videoRef.current.srcObject = null;
      setCameraActive(false);
    }
  };

  const captureAndScan = async () => {
    if (!videoRef.current || !canvasRef.current) return;

    try {
      const canvas = canvasRef.current;
      const video = videoRef.current;
      
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      const ctx = canvas.getContext('2d');
      ctx.drawImage(video, 0, 0);
      
      // In a real implementation, you would use a QR code library here
      // For now, we'll simulate finding a code
      toast.info('Processing image...');
      
      setTimeout(() => {
        // Simulate finding a valid caregiver code
        const mockCode = 'CG' + Math.random().toString(36).substring(2, 8).toUpperCase();
        setCaregiverCode(mockCode);
        setScanMode('manual');
        stopCamera();
        toast.success('QR code detected! Please verify the code below.');
      }, 2000);
      
    } catch (error) {
      console.error('Error capturing image:', error);
      toast.error('Failed to capture image');
    }
  };

  const searchCaregiverByCode = async (code) => {
    try {
      setLoading(true);
      
      // Search for caregiver with this code
      const caregivers = searchUsers(code, 'caregiver');
      
      // Mock search since we don't have real user codes
      const mockCaregiver = {
        id: 'caregiver-' + code,
        displayName: 'Sarah Martin',
        email: '<EMAIL>',
        role: 'caregiver',
        userCode: code,
        specialization: 'Alzheimer Care',
        experience: '5 years',
        availability: 'Available'
      };
      
      setFoundCaregiver(mockCaregiver);
      setShowConfirmModal(true);
      
    } catch (error) {
      console.error('Error searching caregiver:', error);
      toast.error('Caregiver not found with this code');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    if (!isValidCode(caregiverCode)) {
      toast.error('Please enter a valid 8-character code');
      return;
    }
    
    searchCaregiverByCode(caregiverCode);
  };

  const confirmAssignment = async () => {
    if (!foundCaregiver || !patientId) return;

    try {
      setLoading(true);
      
      const result = await assignPatientToUser(patientId, foundCaregiver.id, 'caregiver');
      
      if (result.success) {
        toast.success(`${foundCaregiver.displayName} has been assigned as caregiver`);
        setShowConfirmModal(false);
        navigate('/supervisor/caregivers');
      } else {
        toast.error(result.error || 'Failed to assign caregiver');
      }
      
    } catch (error) {
      console.error('Error assigning caregiver:', error);
      toast.error('Failed to assign caregiver');
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    if (scanMode === 'camera' && !cameraActive) {
      startCamera();
    } else if (scanMode === 'manual' && cameraActive) {
      stopCamera();
    }
    
    return () => {
      stopCamera();
    };
  }, [scanMode]);

  return (
    <DashboardLayout
      title="Scan Caregiver QR"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <ScannerContainer>
        <Header>
          <Title theme={theme}>Add Caregiver</Title>
          <Subtitle theme={theme}>
            Scan QR code or enter caregiver code manually
          </Subtitle>
        </Header>

        <ScannerCard theme={theme}>
          <ScannerModeToggle theme={theme}>
            <ModeButton
              active={scanMode === 'manual'}
              onClick={() => setScanMode('manual')}
              theme={theme}
            >
              📝 Manual Entry
            </ModeButton>
            <ModeButton
              active={scanMode === 'camera'}
              onClick={() => setScanMode('camera')}
              theme={theme}
            >
              📷 Scan QR Code
            </ModeButton>
          </ScannerModeToggle>

          {scanMode === 'camera' ? (
            <CameraSection>
              <CameraContainer theme={theme}>
                {cameraActive ? (
                  <>
                    <VideoElement
                      ref={videoRef}
                      autoPlay
                      playsInline
                      muted
                    />
                    <ScanOverlay theme={theme} />
                  </>
                ) : (
                  <CameraPlaceholder theme={theme}>
                    <CameraIcon>📷</CameraIcon>
                    <CameraText>Starting camera...</CameraText>
                  </CameraPlaceholder>
                )}
              </CameraContainer>
              
              <canvas ref={canvasRef} style={{ display: 'none' }} />
              
              <ActionButtons>
                <Button
                  variant="primary"
                  onClick={captureAndScan}
                  disabled={!cameraActive}
                  loading={loading}
                >
                  📸 Capture & Scan
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setScanMode('manual')}
                >
                  📝 Enter Manually
                </Button>
              </ActionButtons>
            </CameraSection>
          ) : (
            <ManualEntrySection>
              <CodeInput
                label="Caregiver Code"
                value={caregiverCode}
                onChange={handleCodeChange}
                placeholder="Enter 8-character code"
                maxLength={8}
                style={{ marginBottom: '24px' }}
              />
              
              <ActionButtons>
                <Button
                  variant="primary"
                  onClick={handleSubmit}
                  disabled={!isValidCode(caregiverCode)}
                  loading={loading}
                >
                  🔍 Find Caregiver
                </Button>
                <Button
                  variant="outline"
                  onClick={() => navigate('/supervisor/caregivers')}
                >
                  ❌ Cancel
                </Button>
              </ActionButtons>
            </ManualEntrySection>
          )}
        </ScannerCard>

        <InstructionsCard theme={theme}>
          <InstructionsTitle theme={theme}>
            📋 Instructions
          </InstructionsTitle>
          <InstructionsList theme={theme}>
            <li>Ask the caregiver to show their QR code from their profile</li>
            <li>Use the camera to scan the QR code automatically</li>
            <li>Or manually enter the 8-character code displayed below their QR code</li>
            <li>Verify the caregiver information before confirming assignment</li>
            <li>The caregiver will be notified once assigned to the patient</li>
          </InstructionsList>
        </InstructionsCard>

        {/* Confirmation Modal */}
        <Modal
          isOpen={showConfirmModal}
          onClose={() => setShowConfirmModal(false)}
          title="Confirm Caregiver Assignment"
          maxWidth="500px"
        >
          {foundCaregiver && (
            <div>
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: '16px', 
                marginBottom: '24px',
                padding: '16px',
                background: theme.colors.lightGray,
                borderRadius: '12px'
              }}>
                <div style={{
                  width: '60px',
                  height: '60px',
                  borderRadius: '50%',
                  background: theme.colors.primary,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '24px',
                  fontWeight: '600'
                }}>
                  {foundCaregiver.displayName.charAt(0)}
                </div>
                <div>
                  <h3 style={{ margin: '0 0 4px 0', color: theme.colors.text }}>
                    {foundCaregiver.displayName}
                  </h3>
                  <p style={{ margin: '0 0 4px 0', color: theme.colors.textSecondary }}>
                    {foundCaregiver.specialization} • {foundCaregiver.experience}
                  </p>
                  <p style={{ margin: '0', fontSize: '14px', color: theme.colors.textSecondary }}>
                    Code: {foundCaregiver.userCode}
                  </p>
                </div>
              </div>

              <p style={{ marginBottom: '24px', color: theme.colors.text }}>
                Are you sure you want to assign this caregiver to the patient?
              </p>
              
              <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
                <Button
                  variant="outline"
                  onClick={() => setShowConfirmModal(false)}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={confirmAssignment}
                  loading={loading}
                >
                  Confirm Assignment
                </Button>
              </div>
            </div>
          )}
        </Modal>
      </ScannerContainer>
    </DashboardLayout>
  );
};

export default ScanCaregiverQRScreen;
