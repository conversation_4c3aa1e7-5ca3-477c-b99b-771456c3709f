import React, { useState, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useUsers } from '../../contexts/UsersContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Input, Modal } from '../common';

const ScannerContainer = styled.div`
  max-width: 600px;
  margin: 0 auto;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 32px;
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const Subtitle = styled.p`
  font-size: 16px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
`;

const ScannerCard = styled(Card)`
  padding: 32px;
  text-align: center;
  margin-bottom: 24px;
`;

const ScannerModeToggle = styled.div`
  display: flex;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 12px;
  padding: 4px;
  margin-bottom: 32px;
`;

const ModeButton = styled.button`
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  ${props => props.active ? `
    background: ${props.theme.colors.primary};
    color: white;
  ` : `
    background: transparent;
    color: ${props.theme.colors.textSecondary};
  `}
`;

const CameraSection = styled.div`
  margin-bottom: 32px;
`;

const CameraContainer = styled.div`
  position: relative;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 16px;
  overflow: hidden;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const CameraPlaceholder = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: ${props => props.theme.colors.textSecondary};
`;

const CameraIcon = styled.div`
  font-size: 64px;
`;

const CameraText = styled.p`
  font-size: 16px;
  margin: 0;
  text-align: center;
`;

const VideoElement = styled.video`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const ScanOverlay = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border: 3px solid ${props => props.theme.colors.primary};
  border-radius: 16px;
  
  &::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border: 3px solid ${props => props.theme.colors.primary};
    border-radius: 16px;
    animation: pulse 2s infinite;
  }
  
  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }
`;

const ManualEntrySection = styled.div`
  margin-bottom: 32px;
`;

const CodeInput = styled(Input)`
  text-align: center;
  font-family: monospace;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 2px;
  text-transform: uppercase;
`;

const ValidationText = styled.div`
  margin-top: 8px;
  font-size: 14px;
  font-weight: 600;
  
  ${props => props.valid ? `
    color: ${props.theme.colors.success};
  ` : `
    color: ${props.theme.colors.error};
  `}
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
`;

const InstructionsCard = styled(Card)`
  padding: 24px;
  margin-top: 24px;
`;

const InstructionsTitle = styled.h4`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const InstructionsList = styled.ul`
  margin: 0;
  padding-left: 20px;
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.6;
`;

const ScanPatientQRScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const { assignPatientToUser, searchUsers } = useUsers();
  const navigate = useNavigate();

  const [scanMode, setScanMode] = useState('manual'); // 'camera' or 'manual'
  const [patientCode, setPatientCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [cameraActive, setCameraActive] = useState(false);
  const [foundPatient, setFoundPatient] = useState(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const videoRef = useRef(null);
  const canvasRef = useRef(null);

  const roleColors = getRoleColors(user?.role || 'doctor');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Patients', icon: '👥', path: '/patients' },
    { label: 'Scanner', icon: '📱', path: '/scanner' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  const isValidCode = (code) => {
    return /^[A-Z0-9]{8}$/.test(code);
  };

  const handleCodeChange = (e) => {
    const value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    if (value.length <= 8) {
      setPatientCode(value);
    }
  };

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' }
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setCameraActive(true);
        toast.success('Camera started. Position QR code in the frame.');
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      toast.error('Unable to access camera. Please use manual entry.');
      setScanMode('manual');
    }
  };

  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = videoRef.current.srcObject.getTracks();
      tracks.forEach(track => track.stop());
      videoRef.current.srcObject = null;
      setCameraActive(false);
    }
  };

  const captureAndScan = async () => {
    if (!videoRef.current || !canvasRef.current) return;

    try {
      const canvas = canvasRef.current;
      const video = videoRef.current;
      
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      const ctx = canvas.getContext('2d');
      ctx.drawImage(video, 0, 0);
      
      // In a real implementation, you would use a QR code library here
      // For now, we'll simulate finding a code
      toast.info('Processing image...');
      
      setTimeout(() => {
        // Simulate finding a valid patient code
        const mockCode = 'PT' + Math.random().toString(36).substring(2, 8).toUpperCase();
        setPatientCode(mockCode);
        setScanMode('manual');
        stopCamera();
        toast.success('QR code detected! Please verify the code below.');
      }, 2000);
      
    } catch (error) {
      console.error('Error capturing image:', error);
      toast.error('Failed to capture image');
    }
  };

  const searchPatientByCode = async (code) => {
    try {
      setLoading(true);
      
      // Search for patient with this code
      const patients = searchUsers(code, 'patient');
      
      // Mock search since we don't have real user codes
      const mockPatient = {
        id: 'patient-' + code,
        displayName: 'Marie Dubois',
        email: '<EMAIL>',
        role: 'patient',
        userCode: code,
        age: 72,
        condition: 'Alzheimer\'s Disease',
        severity: 'Moderate',
        emergencyContact: '+33 1 23 45 67 89'
      };
      
      setFoundPatient(mockPatient);
      setShowConfirmModal(true);
      
    } catch (error) {
      console.error('Error searching patient:', error);
      toast.error('Patient not found with this code');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    if (!isValidCode(patientCode)) {
      toast.error('Please enter a valid 8-character code');
      return;
    }
    
    searchPatientByCode(patientCode);
  };

  const confirmConnection = async () => {
    if (!foundPatient) return;

    try {
      setLoading(true);
      
      // Determine relationship type based on user role
      let relationshipType = 'doctor-patient';
      if (user.role === 'caregiver') {
        relationshipType = 'caregiver-patient';
      } else if (user.role === 'supervisor') {
        relationshipType = 'supervisor-patient';
      }
      
      const result = await assignPatientToUser(foundPatient.id, user.uid, user.role);
      
      if (result.success) {
        toast.success(`Connected with patient ${foundPatient.displayName}`);
        setShowConfirmModal(false);
        navigate('/patients');
      } else {
        toast.error(result.error || 'Failed to connect with patient');
      }
      
    } catch (error) {
      console.error('Error connecting with patient:', error);
      toast.error('Failed to connect with patient');
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    if (scanMode === 'camera' && !cameraActive) {
      startCamera();
    } else if (scanMode === 'manual' && cameraActive) {
      stopCamera();
    }
    
    return () => {
      stopCamera();
    };
  }, [scanMode]);

  return (
    <DashboardLayout
      title="Connect Patient"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <ScannerContainer>
        <Header>
          <Title theme={theme}>Connect with Patient</Title>
          <Subtitle theme={theme}>
            Scan QR code or enter patient code manually
          </Subtitle>
        </Header>

        <ScannerCard theme={theme}>
          <ScannerModeToggle theme={theme}>
            <ModeButton
              active={scanMode === 'manual'}
              onClick={() => setScanMode('manual')}
              theme={theme}
            >
              📝 Manual Entry
            </ModeButton>
            <ModeButton
              active={scanMode === 'camera'}
              onClick={() => setScanMode('camera')}
              theme={theme}
            >
              📷 Scan QR Code
            </ModeButton>
          </ScannerModeToggle>

          {scanMode === 'camera' ? (
            <CameraSection>
              <CameraContainer theme={theme}>
                {cameraActive ? (
                  <>
                    <VideoElement
                      ref={videoRef}
                      autoPlay
                      playsInline
                      muted
                    />
                    <ScanOverlay theme={theme} />
                  </>
                ) : (
                  <CameraPlaceholder theme={theme}>
                    <CameraIcon>📷</CameraIcon>
                    <CameraText>Starting camera...</CameraText>
                  </CameraPlaceholder>
                )}
              </CameraContainer>
              
              <canvas ref={canvasRef} style={{ display: 'none' }} />
              
              <ActionButtons>
                <Button
                  variant="primary"
                  onClick={captureAndScan}
                  disabled={!cameraActive}
                  loading={loading}
                >
                  📸 Capture & Scan
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setScanMode('manual')}
                >
                  📝 Enter Manually
                </Button>
              </ActionButtons>
            </CameraSection>
          ) : (
            <ManualEntrySection>
              <CodeInput
                label="Patient Code"
                value={patientCode}
                onChange={handleCodeChange}
                placeholder="Enter 8-character code"
                maxLength={8}
                style={{ marginBottom: '8px' }}
              />
              
              {patientCode.length > 0 && (
                <ValidationText valid={isValidCode(patientCode)} theme={theme}>
                  {isValidCode(patientCode) ? '✓ Valid code format' : '✗ Invalid format (8 characters required)'}
                </ValidationText>
              )}
              
              <div style={{ marginTop: '24px' }}>
                <ActionButtons>
                  <Button
                    variant="primary"
                    onClick={handleSubmit}
                    disabled={!isValidCode(patientCode)}
                    loading={loading}
                  >
                    🔍 Find Patient
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => navigate(-1)}
                  >
                    ❌ Cancel
                  </Button>
                </ActionButtons>
              </div>
            </ManualEntrySection>
          )}
        </ScannerCard>

        <InstructionsCard theme={theme}>
          <InstructionsTitle theme={theme}>
            📋 Instructions
          </InstructionsTitle>
          <InstructionsList theme={theme}>
            <li>Ask the patient to show their QR code from their profile</li>
            <li>Use the camera to scan the QR code automatically</li>
            <li>Or manually enter the 8-character code displayed below their QR code</li>
            <li>Verify the patient information before confirming connection</li>
            <li>Once connected, you'll be able to access their medical information</li>
            <li>The patient will be notified of the new connection</li>
          </InstructionsList>
        </InstructionsCard>

        {/* Confirmation Modal */}
        <Modal
          isOpen={showConfirmModal}
          onClose={() => setShowConfirmModal(false)}
          title="Confirm Patient Connection"
          maxWidth="500px"
        >
          {foundPatient && (
            <div>
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: '16px', 
                marginBottom: '24px',
                padding: '16px',
                background: theme.colors.lightGray,
                borderRadius: '12px'
              }}>
                <div style={{
                  width: '60px',
                  height: '60px',
                  borderRadius: '50%',
                  background: theme.colors.primary,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '24px',
                  fontWeight: '600'
                }}>
                  {foundPatient.displayName.charAt(0)}
                </div>
                <div>
                  <h3 style={{ margin: '0 0 4px 0', color: theme.colors.text }}>
                    {foundPatient.displayName}
                  </h3>
                  <p style={{ margin: '0 0 4px 0', color: theme.colors.textSecondary }}>
                    Age: {foundPatient.age} • {foundPatient.condition}
                  </p>
                  <p style={{ margin: '0', fontSize: '14px', color: theme.colors.textSecondary }}>
                    Code: {foundPatient.userCode}
                  </p>
                </div>
              </div>

              <p style={{ marginBottom: '24px', color: theme.colors.text }}>
                Are you sure you want to connect with this patient? This will allow you to access their medical information and provide care.
              </p>
              
              <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
                <Button
                  variant="outline"
                  onClick={() => setShowConfirmModal(false)}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={confirmConnection}
                  loading={loading}
                >
                  Confirm Connection
                </Button>
              </div>
            </div>
          )}
        </Modal>
      </ScannerContainer>
    </DashboardLayout>
  );
};

export default ScanPatientQRScreen;
