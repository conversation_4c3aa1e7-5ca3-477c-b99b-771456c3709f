import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Input } from '../common';
import { useForm } from '../../hooks/useForm';
import { validateRequired, validatePassword, validatePasswordConfirmation } from '../../utils/validation';

const PasswordContainer = styled.div`
  max-width: 600px;
  margin: 0 auto;
`;

const BackButton = styled(Button)`
  margin-bottom: 16px;
`;

const FormCard = styled(Card)`
  margin-bottom: 24px;
`;

const SecurityInfo = styled.div`
  background: ${props => props.theme.colors.lightGray};
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
`;

const SecurityTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const SecurityList = styled.ul`
  margin: 0;
  padding-left: 20px;
  color: ${props => props.theme.colors.textSecondary};
`;

const SecurityItem = styled.li`
  margin-bottom: 8px;
  line-height: 1.4;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid ${props => props.theme.colors.borderLight};
`;

const PasswordStrength = styled.div`
  margin-top: 8px;
`;

const StrengthBar = styled.div`
  width: 100%;
  height: 4px;
  background: ${props => props.theme.colors.borderLight};
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 8px;
`;

const StrengthFill = styled.div`
  height: 100%;
  width: ${props => props.strength}%;
  background: ${props => {
    if (props.strength < 30) return '#F44336';
    if (props.strength < 60) return '#FF9800';
    if (props.strength < 80) return '#FFC107';
    return '#4CAF50';
  }};
  transition: all 0.3s ease;
`;

const StrengthText = styled.div`
  font-size: 12px;
  color: ${props => {
    if (props.strength < 30) return '#F44336';
    if (props.strength < 60) return '#FF9800';
    if (props.strength < 80) return '#FFC107';
    return '#4CAF50';
  }};
  font-weight: 600;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 32px;
`;

const ChangePasswordScreen = () => {
  const { user, updatePassword } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Profile', icon: '👤', path: '/profile' },
    { label: 'Settings', icon: '⚙️', path: '/settings' },
    { label: 'Security', icon: '🔒', path: '/settings/security' }
  ];

  const initialValues = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  };

  const validators = {
    currentPassword: (value) => validateRequired(value, 'Current password'),
    newPassword: (value) => {
      const result = validatePassword(value);
      if (result.isValid) {
        calculatePasswordStrength(value);
      }
      return result;
    },
    confirmPassword: (value, formValues) => validatePasswordConfirmation(value, formValues.newPassword)
  };

  const {
    values,
    errors,
    handleChange,
    handleSubmit,
    getFieldProps
  } = useForm(
    initialValues,
    validators,
    async (formData) => {
      setLoading(true);
      try {
        await updatePassword(formData.currentPassword, formData.newPassword);
        toast.success('Password updated successfully!');
        navigate('/settings');
        
      } catch (error) {
        console.error('Error updating password:', error);
        
        if (error.code === 'auth/wrong-password') {
          toast.error('Current password is incorrect');
        } else if (error.code === 'auth/weak-password') {
          toast.error('New password is too weak');
        } else {
          toast.error('Failed to update password. Please try again.');
        }
      } finally {
        setLoading(false);
      }
    }
  );

  const calculatePasswordStrength = (password) => {
    let strength = 0;
    
    // Length check
    if (password.length >= 8) strength += 20;
    if (password.length >= 12) strength += 10;
    
    // Character variety checks
    if (/[a-z]/.test(password)) strength += 15;
    if (/[A-Z]/.test(password)) strength += 15;
    if (/[0-9]/.test(password)) strength += 15;
    if (/[^A-Za-z0-9]/.test(password)) strength += 15;
    
    // Additional complexity
    if (password.length >= 16) strength += 10;
    
    setPasswordStrength(Math.min(strength, 100));
  };

  const getStrengthLabel = (strength) => {
    if (strength < 30) return 'Weak';
    if (strength < 60) return 'Fair';
    if (strength < 80) return 'Good';
    return 'Strong';
  };

  return (
    <DashboardLayout
      title="Change Password"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <PasswordContainer>
        <BackButton
          variant="ghost"
          onClick={() => navigate('/settings')}
          icon="←"
        >
          Back to Settings
        </BackButton>

        <FormCard title="Change Password" theme={theme}>
          <SecurityInfo theme={theme}>
            <SecurityTitle theme={theme}>
              🔒 Password Security Guidelines
            </SecurityTitle>
            <SecurityList theme={theme}>
              <SecurityItem>Use at least 8 characters (12+ recommended)</SecurityItem>
              <SecurityItem>Include uppercase and lowercase letters</SecurityItem>
              <SecurityItem>Add numbers and special characters</SecurityItem>
              <SecurityItem>Avoid common words or personal information</SecurityItem>
              <SecurityItem>Don't reuse passwords from other accounts</SecurityItem>
            </SecurityList>
          </SecurityInfo>

          <form onSubmit={handleSubmit}>
            <FormSection>
              <SectionTitle theme={theme}>Current Password</SectionTitle>
              <Input
                {...getFieldProps('currentPassword')}
                label="Current Password *"
                type="password"
                placeholder="Enter your current password"
                autoComplete="current-password"
              />
            </FormSection>

            <FormSection>
              <SectionTitle theme={theme}>New Password</SectionTitle>
              <Input
                {...getFieldProps('newPassword')}
                label="New Password *"
                type="password"
                placeholder="Enter your new password"
                autoComplete="new-password"
              />
              
              {values.newPassword && (
                <PasswordStrength>
                  <StrengthBar theme={theme}>
                    <StrengthFill strength={passwordStrength} />
                  </StrengthBar>
                  <StrengthText strength={passwordStrength}>
                    Password strength: {getStrengthLabel(passwordStrength)}
                  </StrengthText>
                </PasswordStrength>
              )}
              
              <Input
                {...getFieldProps('confirmPassword')}
                label="Confirm New Password *"
                type="password"
                placeholder="Confirm your new password"
                autoComplete="new-password"
                style={{ marginTop: '16px' }}
              />
            </FormSection>

            <ButtonGroup>
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/settings')}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={loading}
                disabled={passwordStrength < 60}
              >
                Update Password
              </Button>
            </ButtonGroup>
          </form>
        </FormCard>

        <Card theme={theme}>
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>🛡️</div>
            <h3 style={{ margin: '0 0 8px 0', color: theme.colors.text }}>
              Keep Your Account Secure
            </h3>
            <p style={{ 
              margin: '0', 
              color: theme.colors.textSecondary,
              fontSize: '14px',
              lineHeight: '1.5'
            }}>
              Regularly updating your password helps protect your medical information and personal data.
              If you suspect your account has been compromised, change your password immediately.
            </p>
          </div>
        </Card>
      </PasswordContainer>
    </DashboardLayout>
  );
};

export default ChangePasswordScreen;
