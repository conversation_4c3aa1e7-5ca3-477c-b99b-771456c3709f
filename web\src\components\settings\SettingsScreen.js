import React from 'react';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';

const SettingsContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const SettingsCard = styled.div`
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: ${props => props.theme.shadows.md};
  margin-bottom: 24px;
`;

const SectionTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 24px 0;
  padding-bottom: 12px;
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const SettingItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};

  &:last-child {
    border-bottom: none;
  }
`;

const SettingInfo = styled.div`
  flex: 1;
`;

const SettingLabel = styled.h3`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 4px 0;
`;

const SettingDescription = styled.p`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
`;

const SettingControl = styled.div`
  margin-left: 16px;
`;

const Toggle = styled.button`
  width: 50px;
  height: 26px;
  border-radius: 13px;
  border: none;
  cursor: pointer;
  position: relative;
  transition: background-color 0.3s ease;
  
  ${props => props.enabled ? `
    background-color: ${props.theme.colors.primary};
  ` : `
    background-color: ${props.theme.colors.border};
  `}

  &::after {
    content: '';
    position: absolute;
    width: 22px;
    height: 22px;
    border-radius: 50%;
    background-color: white;
    top: 2px;
    transition: transform 0.3s ease;
    
    ${props => props.enabled ? `
      transform: translateX(26px);
    ` : `
      transform: translateX(2px);
    `}
  }
`;

const Button = styled.button`
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid ${props => props.theme.colors.border};
  background: white;
  color: ${props => props.theme.colors.textSecondary};

  &:hover {
    background: ${props => props.theme.colors.lightGray};
  }

  &.danger {
    color: #F44336;
    border-color: #F44336;

    &:hover {
      background: #F44336;
      color: white;
    }
  }
`;

const SettingsScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors, isDarkMode, toggleDarkMode } = useTheme();

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', screen: 'dashboard' },
    { label: 'Profile', icon: '👤', screen: 'profile' },
    { label: 'Settings', icon: '⚙️', screen: 'settings' },
    { label: 'Notifications', icon: '🔔', screen: 'notifications' }
  ];

  return (
    <DashboardLayout
      title="Settings"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <SettingsContainer>
        {/* Appearance Settings */}
        <SettingsCard theme={theme}>
          <SectionTitle theme={theme}>Appearance</SectionTitle>
          
          <SettingItem theme={theme}>
            <SettingInfo>
              <SettingLabel theme={theme}>Dark Mode</SettingLabel>
              <SettingDescription theme={theme}>
                Switch between light and dark themes
              </SettingDescription>
            </SettingInfo>
            <SettingControl>
              <Toggle 
                enabled={isDarkMode} 
                onClick={toggleDarkMode}
                theme={theme}
              />
            </SettingControl>
          </SettingItem>
        </SettingsCard>

        {/* Notification Settings */}
        <SettingsCard theme={theme}>
          <SectionTitle theme={theme}>Notifications</SectionTitle>
          
          <SettingItem theme={theme}>
            <SettingInfo>
              <SettingLabel theme={theme}>Push Notifications</SettingLabel>
              <SettingDescription theme={theme}>
                Receive notifications about appointments and medications
              </SettingDescription>
            </SettingInfo>
            <SettingControl>
              <Toggle enabled={true} theme={theme} />
            </SettingControl>
          </SettingItem>

          <SettingItem theme={theme}>
            <SettingInfo>
              <SettingLabel theme={theme}>Email Notifications</SettingLabel>
              <SettingDescription theme={theme}>
                Receive email updates about your health records
              </SettingDescription>
            </SettingInfo>
            <SettingControl>
              <Toggle enabled={false} theme={theme} />
            </SettingControl>
          </SettingItem>
        </SettingsCard>

        {/* Privacy Settings */}
        <SettingsCard theme={theme}>
          <SectionTitle theme={theme}>Privacy & Security</SectionTitle>
          
          <SettingItem theme={theme}>
            <SettingInfo>
              <SettingLabel theme={theme}>Two-Factor Authentication</SettingLabel>
              <SettingDescription theme={theme}>
                Add an extra layer of security to your account
              </SettingDescription>
            </SettingInfo>
            <SettingControl>
              <Button theme={theme}>Enable</Button>
            </SettingControl>
          </SettingItem>

          <SettingItem theme={theme}>
            <SettingInfo>
              <SettingLabel theme={theme}>Change Password</SettingLabel>
              <SettingDescription theme={theme}>
                Update your account password
              </SettingDescription>
            </SettingInfo>
            <SettingControl>
              <Button theme={theme}>Change</Button>
            </SettingControl>
          </SettingItem>
        </SettingsCard>

        {/* Account Settings */}
        <SettingsCard theme={theme}>
          <SectionTitle theme={theme}>Account</SectionTitle>
          
          <SettingItem theme={theme}>
            <SettingInfo>
              <SettingLabel theme={theme}>Export Data</SettingLabel>
              <SettingDescription theme={theme}>
                Download a copy of your health data
              </SettingDescription>
            </SettingInfo>
            <SettingControl>
              <Button theme={theme}>Export</Button>
            </SettingControl>
          </SettingItem>

          <SettingItem theme={theme}>
            <SettingInfo>
              <SettingLabel theme={theme}>Delete Account</SettingLabel>
              <SettingDescription theme={theme}>
                Permanently delete your account and all data
              </SettingDescription>
            </SettingInfo>
            <SettingControl>
              <Button className="danger" theme={theme}>Delete</Button>
            </SettingControl>
          </SettingItem>
        </SettingsCard>
      </SettingsContainer>
    </DashboardLayout>
  );
};

export default SettingsScreen;
