import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Input, Select, Modal } from '../common';
import { formatDate } from '../../utils/dateUtils';

const AppointmentsContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const Controls = styled.div`
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
`;

const StatCard = styled(Card)`
  text-align: center;
  padding: 24px;
`;

const StatValue = styled.div`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.color || props.theme.colors.primary};
  margin-bottom: 8px;
`;

const StatLabel = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  font-weight: 600;
`;

const AppointmentsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const AppointmentCard = styled(Card)`
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid ${props => {
    switch (props.status) {
      case 'scheduled': return '#2196F3';
      case 'in_progress': return '#4CAF50';
      case 'completed': return '#9C27B0';
      case 'cancelled': return '#F44336';
      default: return props.theme.colors.border;
    }
  }};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.lg};
  }
`;

const AppointmentHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
`;

const AppointmentInfo = styled.div`
  flex: 1;
`;

const AppointmentTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const AppointmentMeta = styled.div`
  display: flex;
  gap: 24px;
  color: ${props => props.theme.colors.textSecondary};
  font-size: 14px;
  margin-bottom: 8px;
`;

const StatusBadge = styled.span`
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  
  ${props => {
    switch (props.status) {
      case 'scheduled':
        return `background: #E3F2FD; color: #1976D2;`;
      case 'in_progress':
        return `background: #E8F5E8; color: #2E7D32;`;
      case 'completed':
        return `background: #F3E5F5; color: #7B1FA2;`;
      case 'cancelled':
        return `background: #FFEBEE; color: #C62828;`;
      default:
        return `background: #F5F5F5; color: #757575;`;
    }
  }}
`;

const AppointmentDetails = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const DetailItem = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const DetailIcon = styled.span`
  font-size: 16px;
`;

const DetailText = styled.span`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
`;

const AppointmentActions = styled.div`
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: ${props => props.theme.shadows.md};
`;

const EmptyIcon = styled.div`
  font-size: 64px;
  margin-bottom: 16px;
`;

const SupervisorAppointmentsScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();

  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const roleColors = getRoleColors(user?.role || 'supervisor');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Appointments', icon: '📅', path: '/supervisor/appointments' },
    { label: 'Caregivers', icon: '👨‍⚕️', path: '/supervisor/caregivers' },
    { label: 'Patients', icon: '👥', path: '/supervisor/patients' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  useEffect(() => {
    loadAppointments();
  }, []);

  const loadAppointments = async () => {
    try {
      setLoading(true);
      
      // Mock appointments data for supervisor view
      const mockAppointments = [
        {
          id: 'apt-1',
          patientName: 'Marie Dubois',
          patientId: 'patient-1',
          doctorName: 'Dr. Sarah Johnson',
          doctorId: 'doctor-1',
          caregiverName: 'Sophie Martin',
          caregiverId: 'caregiver-1',
          type: 'consultation',
          status: 'scheduled',
          date: new Date(Date.now() + 2 * 60 * 60 * 1000),
          duration: 30,
          location: 'NeuroCare Medical Center',
          reason: 'Regular check-up',
          notes: 'Follow-up for Alzheimer\'s treatment',
          priority: 'normal'
        },
        {
          id: 'apt-2',
          patientName: 'Pierre Martin',
          patientId: 'patient-2',
          doctorName: 'Dr. Michel Rousseau',
          doctorId: 'doctor-2',
          caregiverName: 'Marc Dubois',
          caregiverId: 'caregiver-2',
          type: 'therapy',
          status: 'in_progress',
          date: new Date(Date.now() - 30 * 60 * 1000),
          duration: 45,
          location: 'Therapy Center',
          reason: 'Physical therapy session',
          notes: 'Parkinson\'s mobility exercises',
          priority: 'high'
        },
        {
          id: 'apt-3',
          patientName: 'Jean Dupont',
          patientId: 'patient-3',
          doctorName: 'Dr. Claire Rousseau',
          doctorId: 'doctor-3',
          caregiverName: 'Claire Rousseau',
          caregiverId: 'caregiver-3',
          type: 'consultation',
          status: 'completed',
          date: new Date(Date.now() - 24 * 60 * 60 * 1000),
          duration: 30,
          location: 'Video Call',
          reason: 'Medication review',
          notes: 'Dementia medication adjustment',
          priority: 'normal'
        },
        {
          id: 'apt-4',
          patientName: 'Anne Moreau',
          patientId: 'patient-4',
          doctorName: 'Dr. Sarah Johnson',
          doctorId: 'doctor-1',
          caregiverName: 'Sophie Martin',
          caregiverId: 'caregiver-1',
          type: 'emergency',
          status: 'cancelled',
          date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          duration: 60,
          location: 'Emergency Room',
          reason: 'Emergency consultation',
          notes: 'Patient condition improved, cancelled',
          priority: 'urgent'
        }
      ];
      
      setAppointments(mockAppointments);
      
    } catch (error) {
      console.error('Error loading appointments:', error);
      toast.error('Failed to load appointments');
    } finally {
      setLoading(false);
    }
  };

  const handleAppointmentClick = (appointment) => {
    setSelectedAppointment(appointment);
    setShowDetailsModal(true);
  };

  const handleViewPatient = (patientId) => {
    navigate(`/supervisor/patients/${patientId}`);
  };

  const handleViewCaregiver = (caregiverId) => {
    navigate(`/supervisor/caregivers/${caregiverId}`);
  };

  const handleReschedule = (appointmentId) => {
    navigate(`/appointments/${appointmentId}/reschedule`);
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'scheduled': return 'Scheduled';
      case 'in_progress': return 'In Progress';
      case 'completed': return 'Completed';
      case 'cancelled': return 'Cancelled';
      default: return status;
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return '#F44336';
      case 'high': return '#FF9800';
      case 'normal': return '#4CAF50';
      case 'low': return '#2196F3';
      default: return theme.colors.textSecondary;
    }
  };

  const getAppointmentStats = () => {
    const total = appointments.length;
    const scheduled = appointments.filter(apt => apt.status === 'scheduled').length;
    const inProgress = appointments.filter(apt => apt.status === 'in_progress').length;
    const completed = appointments.filter(apt => apt.status === 'completed').length;
    
    return { total, scheduled, inProgress, completed };
  };

  const filteredAppointments = appointments.filter(appointment => {
    const matchesSearch = 
      appointment.patientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      appointment.doctorName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      appointment.caregiverName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      appointment.reason.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || appointment.status === statusFilter;
    
    const matchesDate = dateFilter === 'all' || (() => {
      const appointmentDate = new Date(appointment.date);
      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(today.getDate() + 1);
      
      switch (dateFilter) {
        case 'today':
          return appointmentDate.toDateString() === today.toDateString();
        case 'tomorrow':
          return appointmentDate.toDateString() === tomorrow.toDateString();
        case 'week':
          const weekFromNow = new Date(today);
          weekFromNow.setDate(today.getDate() + 7);
          return appointmentDate >= today && appointmentDate <= weekFromNow;
        default:
          return true;
      }
    })();
    
    return matchesSearch && matchesStatus && matchesDate;
  });

  const stats = getAppointmentStats();

  if (loading) {
    return (
      <DashboardLayout
        title="Appointments Management"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <AppointmentsContainer>
          <Card theme={theme}>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              Loading appointments...
            </div>
          </Card>
        </AppointmentsContainer>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Appointments Management"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <AppointmentsContainer>
        <Header>
          <Title theme={theme}>Appointments Overview</Title>
          <Controls>
            <Input
              placeholder="Search appointments..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{ width: '250px' }}
              icon="🔍"
            />
            <Select
              value={statusFilter}
              onChange={setStatusFilter}
              options={[
                { value: 'all', label: 'All Status' },
                { value: 'scheduled', label: 'Scheduled' },
                { value: 'in_progress', label: 'In Progress' },
                { value: 'completed', label: 'Completed' },
                { value: 'cancelled', label: 'Cancelled' }
              ]}
              style={{ width: '150px' }}
            />
            <Select
              value={dateFilter}
              onChange={setDateFilter}
              options={[
                { value: 'all', label: 'All Dates' },
                { value: 'today', label: 'Today' },
                { value: 'tomorrow', label: 'Tomorrow' },
                { value: 'week', label: 'This Week' }
              ]}
              style={{ width: '150px' }}
            />
            <Button
              variant="outline"
              onClick={loadAppointments}
              loading={loading}
              icon="🔄"
            >
              Refresh
            </Button>
          </Controls>
        </Header>

        <StatsGrid>
          <StatCard theme={theme}>
            <StatValue theme={theme}>{stats.total}</StatValue>
            <StatLabel theme={theme}>Total Appointments</StatLabel>
          </StatCard>
          <StatCard theme={theme}>
            <StatValue color="#2196F3" theme={theme}>{stats.scheduled}</StatValue>
            <StatLabel theme={theme}>Scheduled</StatLabel>
          </StatCard>
          <StatCard theme={theme}>
            <StatValue color="#4CAF50" theme={theme}>{stats.inProgress}</StatValue>
            <StatLabel theme={theme}>In Progress</StatLabel>
          </StatCard>
          <StatCard theme={theme}>
            <StatValue color="#9C27B0" theme={theme}>{stats.completed}</StatValue>
            <StatLabel theme={theme}>Completed</StatLabel>
          </StatCard>
        </StatsGrid>

        {filteredAppointments.length === 0 ? (
          <EmptyState theme={theme}>
            <EmptyIcon>📅</EmptyIcon>
            <h3>No appointments found</h3>
            <p>
              {searchQuery || statusFilter !== 'all' || dateFilter !== 'all'
                ? 'No appointments match your current filters.'
                : 'No appointments scheduled yet.'
              }
            </p>
          </EmptyState>
        ) : (
          <AppointmentsList>
            {filteredAppointments.map(appointment => (
              <AppointmentCard
                key={appointment.id}
                onClick={() => handleAppointmentClick(appointment)}
                status={appointment.status}
                theme={theme}
              >
                <AppointmentHeader>
                  <AppointmentInfo>
                    <AppointmentTitle theme={theme}>
                      {appointment.patientName} - {appointment.type}
                    </AppointmentTitle>
                    <AppointmentMeta theme={theme}>
                      <span>📅 {formatDate(appointment.date, 'datetime')}</span>
                      <span>⏱️ {appointment.duration} min</span>
                      <span>📍 {appointment.location}</span>
                      <span style={{ color: getPriorityColor(appointment.priority) }}>
                        🔥 {appointment.priority}
                      </span>
                    </AppointmentMeta>
                  </AppointmentInfo>
                  <StatusBadge status={appointment.status}>
                    {getStatusText(appointment.status)}
                  </StatusBadge>
                </AppointmentHeader>

                <AppointmentDetails>
                  <DetailItem>
                    <DetailIcon>👨‍⚕️</DetailIcon>
                    <DetailText theme={theme}>Dr: {appointment.doctorName}</DetailText>
                  </DetailItem>
                  <DetailItem>
                    <DetailIcon>👨‍⚕️</DetailIcon>
                    <DetailText theme={theme}>Caregiver: {appointment.caregiverName}</DetailText>
                  </DetailItem>
                  <DetailItem>
                    <DetailIcon>📝</DetailIcon>
                    <DetailText theme={theme}>Reason: {appointment.reason}</DetailText>
                  </DetailItem>
                </AppointmentDetails>

                <AppointmentActions>
                  <Button
                    variant="primary"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleViewPatient(appointment.patientId);
                    }}
                    icon="👤"
                  >
                    View Patient
                  </Button>
                  <Button
                    variant="outline"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleViewCaregiver(appointment.caregiverId);
                    }}
                    icon="👨‍⚕️"
                  >
                    View Caregiver
                  </Button>
                  {appointment.status === 'scheduled' && (
                    <Button
                      variant="outline"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleReschedule(appointment.id);
                      }}
                      icon="📅"
                    >
                      Reschedule
                    </Button>
                  )}
                </AppointmentActions>
              </AppointmentCard>
            ))}
          </AppointmentsList>
        )}

        {/* Appointment Details Modal */}
        <Modal
          isOpen={showDetailsModal}
          onClose={() => setShowDetailsModal(false)}
          title="Appointment Details"
          maxWidth="700px"
        >
          {selectedAppointment && (
            <div>
              <div style={{ marginBottom: '24px' }}>
                <h3 style={{ margin: '0 0 16px 0', color: theme.colors.text }}>
                  {selectedAppointment.patientName} - {selectedAppointment.type}
                </h3>
                
                <div style={{ 
                  display: 'grid', 
                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
                  gap: '16px',
                  marginBottom: '16px'
                }}>
                  <div>
                    <strong>Date & Time:</strong><br />
                    {formatDate(selectedAppointment.date, 'datetime')}
                  </div>
                  <div>
                    <strong>Duration:</strong><br />
                    {selectedAppointment.duration} minutes
                  </div>
                  <div>
                    <strong>Location:</strong><br />
                    {selectedAppointment.location}
                  </div>
                  <div>
                    <strong>Priority:</strong><br />
                    <span style={{ color: getPriorityColor(selectedAppointment.priority) }}>
                      {selectedAppointment.priority}
                    </span>
                  </div>
                </div>
                
                <div style={{ marginBottom: '16px' }}>
                  <strong>Doctor:</strong> {selectedAppointment.doctorName}<br />
                  <strong>Caregiver:</strong> {selectedAppointment.caregiverName}<br />
                  <strong>Reason:</strong> {selectedAppointment.reason}
                </div>
                
                {selectedAppointment.notes && (
                  <div>
                    <strong>Notes:</strong><br />
                    {selectedAppointment.notes}
                  </div>
                )}
              </div>
              
              <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
                <Button
                  variant="outline"
                  onClick={() => setShowDetailsModal(false)}
                >
                  Close
                </Button>
                <Button
                  variant="primary"
                  onClick={() => {
                    handleViewPatient(selectedAppointment.patientId);
                    setShowDetailsModal(false);
                  }}
                >
                  View Patient Details
                </Button>
              </div>
            </div>
          )}
        </Modal>
      </AppointmentsContainer>
    </DashboardLayout>
  );
};

export default SupervisorAppointmentsScreen;
