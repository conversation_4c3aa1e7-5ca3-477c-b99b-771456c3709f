import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Input, Select, Modal } from '../common';

const AssignContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const BackButton = styled(Button)`
  margin-bottom: 16px;
`;

const AssignmentCard = styled(Card)`
  margin-bottom: 24px;
`;

const SectionTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid ${props => props.theme.colors.borderLight};
`;

const SelectionGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  margin-bottom: 32px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const SelectionSection = styled.div``;

const SearchContainer = styled.div`
  margin-bottom: 16px;
`;

const ItemsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
`;

const ItemCard = styled.div`
  padding: 16px;
  border: 2px solid ${props => props.selected ? props.theme.colors.primary : props.theme.colors.border};
  border-radius: 12px;
  background: ${props => props.selected ? props.theme.colors.primary + '10' : 'white'};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: ${props => props.theme.colors.primary};
  }
`;

const ItemHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
`;

const ItemAvatar = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: ${props => props.color || props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 18px;
`;

const ItemInfo = styled.div`
  flex: 1;
`;

const ItemName = styled.h4`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 4px 0;
`;

const ItemMeta = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
`;

const ItemDetails = styled.div`
  font-size: 13px;
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.4;
`;

const AssignmentSummary = styled(Card)`
  margin-bottom: 24px;
`;

const SummaryGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
`;

const SummarySection = styled.div``;

const SummaryItem = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 12px;
`;

const SummaryAvatar = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: ${props => props.color || props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 20px;
`;

const SummaryInfo = styled.div`
  flex: 1;
`;

const SummaryName = styled.h4`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 4px 0;
`;

const SummaryMeta = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
`;

const AssignmentOptions = styled.div`
  margin-bottom: 24px;
`;

const OptionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 16px;
  justify-content: flex-end;
`;

const SupervisorAssignCaregiverScreen = () => {
  const { patientId } = useParams();
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();

  const [selectedPatient, setSelectedPatient] = useState(null);
  const [selectedCaregiver, setSelectedCaregiver] = useState(null);
  const [patients, setPatients] = useState([]);
  const [caregivers, setCaregivers] = useState([]);
  const [patientSearch, setPatientSearch] = useState('');
  const [caregiverSearch, setCaregiverSearch] = useState('');
  const [assignmentType, setAssignmentType] = useState('primary');
  const [schedule, setSchedule] = useState('full_time');
  const [startDate, setStartDate] = useState('');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const roleColors = getRoleColors(user?.role || 'supervisor');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Caregivers', icon: '👨‍⚕️', path: '/supervisor/caregivers' },
    { label: 'Patients', icon: '👥', path: '/supervisor/patients' },
    { label: 'Assignments', icon: '📋', path: '/supervisor/assignments' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  useEffect(() => {
    loadData();
    if (patientId) {
      // Pre-select patient if provided in URL
      loadPatientById(patientId);
    }
  }, [patientId]);

  const loadData = async () => {
    try {
      // Mock patients data
      const mockPatients = [
        {
          id: 'patient-1',
          name: 'Marie Dubois',
          age: 72,
          condition: 'Alzheimer\'s Disease',
          severity: 'Moderate',
          location: 'Paris 15th',
          currentCaregiver: null,
          needsAssistance: ['medication', 'mobility', 'cognitive']
        },
        {
          id: 'patient-2',
          name: 'Pierre Martin',
          age: 68,
          condition: 'Parkinson\'s Disease',
          severity: 'Mild',
          location: 'Paris 12th',
          currentCaregiver: 'Marc Dubois',
          needsAssistance: ['mobility', 'therapy']
        },
        {
          id: 'patient-3',
          name: 'Jean Dupont',
          age: 75,
          condition: 'Dementia',
          severity: 'Severe',
          location: 'Paris 8th',
          currentCaregiver: null,
          needsAssistance: ['medication', 'mobility', 'cognitive', 'daily_care']
        }
      ];

      // Mock caregivers data
      const mockCaregivers = [
        {
          id: 'caregiver-1',
          name: 'Sophie Martin',
          specialization: 'Alzheimer Care',
          experience: '5 years',
          availability: 'Available',
          currentPatients: 3,
          maxPatients: 5,
          location: 'Paris 15th',
          rating: 4.8,
          languages: ['French', 'English']
        },
        {
          id: 'caregiver-2',
          name: 'Marc Dubois',
          specialization: 'Parkinson Care',
          experience: '8 years',
          availability: 'Busy',
          currentPatients: 4,
          maxPatients: 4,
          location: 'Paris 12th',
          rating: 4.9,
          languages: ['French', 'Spanish']
        },
        {
          id: 'caregiver-3',
          name: 'Claire Rousseau',
          specialization: 'Dementia Care',
          experience: '3 years',
          availability: 'Available',
          currentPatients: 2,
          maxPatients: 6,
          location: 'Paris 8th',
          rating: 4.6,
          languages: ['French', 'Italian']
        }
      ];

      setPatients(mockPatients);
      setCaregivers(mockCaregivers);
      
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load data');
    }
  };

  const loadPatientById = (id) => {
    const patient = patients.find(p => p.id === id);
    if (patient) {
      setSelectedPatient(patient);
    }
  };

  const handlePatientSelect = (patient) => {
    setSelectedPatient(patient);
  };

  const handleCaregiverSelect = (caregiver) => {
    setSelectedCaregiver(caregiver);
  };

  const handleAssign = () => {
    if (!selectedPatient || !selectedCaregiver) {
      toast.error('Please select both a patient and a caregiver');
      return;
    }
    
    if (!startDate) {
      toast.error('Please select a start date');
      return;
    }
    
    setShowConfirmModal(true);
  };

  const confirmAssignment = async () => {
    setLoading(true);
    try {
      const assignmentData = {
        patientId: selectedPatient.id,
        caregiverId: selectedCaregiver.id,
        assignmentType,
        schedule,
        startDate,
        notes,
        assignedBy: user.uid,
        assignedAt: new Date().toISOString()
      };

      // In a real implementation, you would save this to Firebase
      console.log('Creating assignment:', assignmentData);
      
      toast.success(`${selectedCaregiver.name} has been assigned to ${selectedPatient.name}`);
      setShowConfirmModal(false);
      navigate('/supervisor/assignments');
      
    } catch (error) {
      console.error('Error creating assignment:', error);
      toast.error('Failed to create assignment');
    } finally {
      setLoading(false);
    }
  };

  const getCaregiverColor = () => '#FF9800';
  const getPatientColor = () => '#4CAF50';

  const filteredPatients = patients.filter(patient =>
    patient.name.toLowerCase().includes(patientSearch.toLowerCase()) ||
    patient.condition.toLowerCase().includes(patientSearch.toLowerCase())
  );

  const filteredCaregivers = caregivers.filter(caregiver =>
    caregiver.name.toLowerCase().includes(caregiverSearch.toLowerCase()) ||
    caregiver.specialization.toLowerCase().includes(caregiverSearch.toLowerCase())
  );

  return (
    <DashboardLayout
      title="Assign Caregiver"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <AssignContainer>
        <BackButton
          variant="ghost"
          onClick={() => navigate('/supervisor/caregivers')}
          icon="←"
        >
          Back to Caregivers
        </BackButton>

        <AssignmentCard title="Assign Caregiver to Patient" theme={theme}>
          <SelectionGrid>
            <SelectionSection>
              <SectionTitle theme={theme}>Select Patient</SectionTitle>
              <SearchContainer>
                <Input
                  placeholder="Search patients..."
                  value={patientSearch}
                  onChange={(e) => setPatientSearch(e.target.value)}
                  icon="🔍"
                />
              </SearchContainer>
              <ItemsList>
                {filteredPatients.map(patient => (
                  <ItemCard
                    key={patient.id}
                    selected={selectedPatient?.id === patient.id}
                    onClick={() => handlePatientSelect(patient)}
                    theme={theme}
                  >
                    <ItemHeader>
                      <ItemAvatar color={getPatientColor()} theme={theme}>
                        {patient.name.charAt(0)}
                      </ItemAvatar>
                      <ItemInfo>
                        <ItemName theme={theme}>{patient.name}</ItemName>
                        <ItemMeta theme={theme}>
                          Age: {patient.age} • {patient.condition}
                        </ItemMeta>
                      </ItemInfo>
                    </ItemHeader>
                    <ItemDetails theme={theme}>
                      📍 {patient.location} • Severity: {patient.severity}
                      <br />
                      Current caregiver: {patient.currentCaregiver || 'None assigned'}
                    </ItemDetails>
                  </ItemCard>
                ))}
              </ItemsList>
            </SelectionSection>

            <SelectionSection>
              <SectionTitle theme={theme}>Select Caregiver</SectionTitle>
              <SearchContainer>
                <Input
                  placeholder="Search caregivers..."
                  value={caregiverSearch}
                  onChange={(e) => setCaregiverSearch(e.target.value)}
                  icon="🔍"
                />
              </SearchContainer>
              <ItemsList>
                {filteredCaregivers.map(caregiver => (
                  <ItemCard
                    key={caregiver.id}
                    selected={selectedCaregiver?.id === caregiver.id}
                    onClick={() => handleCaregiverSelect(caregiver)}
                    theme={theme}
                  >
                    <ItemHeader>
                      <ItemAvatar color={getCaregiverColor()} theme={theme}>
                        {caregiver.name.charAt(0)}
                      </ItemAvatar>
                      <ItemInfo>
                        <ItemName theme={theme}>{caregiver.name}</ItemName>
                        <ItemMeta theme={theme}>
                          {caregiver.specialization} • {caregiver.experience}
                        </ItemMeta>
                      </ItemInfo>
                    </ItemHeader>
                    <ItemDetails theme={theme}>
                      📍 {caregiver.location} • ⭐ {caregiver.rating}
                      <br />
                      Patients: {caregiver.currentPatients}/{caregiver.maxPatients} • {caregiver.availability}
                    </ItemDetails>
                  </ItemCard>
                ))}
              </ItemsList>
            </SelectionSection>
          </SelectionGrid>

          {selectedPatient && selectedCaregiver && (
            <>
              <AssignmentSummary title="Assignment Summary" theme={theme}>
                <SummaryGrid>
                  <SummarySection>
                    <h4 style={{ margin: '0 0 16px 0', color: theme.colors.text }}>Patient</h4>
                    <SummaryItem theme={theme}>
                      <SummaryAvatar color={getPatientColor()} theme={theme}>
                        {selectedPatient.name.charAt(0)}
                      </SummaryAvatar>
                      <SummaryInfo>
                        <SummaryName theme={theme}>{selectedPatient.name}</SummaryName>
                        <SummaryMeta theme={theme}>
                          {selectedPatient.condition} • {selectedPatient.location}
                        </SummaryMeta>
                      </SummaryInfo>
                    </SummaryItem>
                  </SummarySection>

                  <SummarySection>
                    <h4 style={{ margin: '0 0 16px 0', color: theme.colors.text }}>Caregiver</h4>
                    <SummaryItem theme={theme}>
                      <SummaryAvatar color={getCaregiverColor()} theme={theme}>
                        {selectedCaregiver.name.charAt(0)}
                      </SummaryAvatar>
                      <SummaryInfo>
                        <SummaryName theme={theme}>{selectedCaregiver.name}</SummaryName>
                        <SummaryMeta theme={theme}>
                          {selectedCaregiver.specialization} • ⭐ {selectedCaregiver.rating}
                        </SummaryMeta>
                      </SummaryInfo>
                    </SummaryItem>
                  </SummarySection>
                </SummaryGrid>
              </AssignmentSummary>

              <AssignmentOptions>
                <SectionTitle theme={theme}>Assignment Details</SectionTitle>
                <OptionsGrid>
                  <Select
                    label="Assignment Type"
                    value={assignmentType}
                    onChange={setAssignmentType}
                    options={[
                      { value: 'primary', label: 'Primary Caregiver' },
                      { value: 'secondary', label: 'Secondary Caregiver' },
                      { value: 'temporary', label: 'Temporary Assignment' },
                      { value: 'emergency', label: 'Emergency Coverage' }
                    ]}
                  />
                  
                  <Select
                    label="Schedule"
                    value={schedule}
                    onChange={setSchedule}
                    options={[
                      { value: 'full_time', label: 'Full Time' },
                      { value: 'part_time', label: 'Part Time' },
                      { value: 'weekdays', label: 'Weekdays Only' },
                      { value: 'weekends', label: 'Weekends Only' },
                      { value: 'nights', label: 'Night Shifts' }
                    ]}
                  />
                  
                  <Input
                    label="Start Date"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                </OptionsGrid>
                
                <Input
                  label="Notes"
                  placeholder="Additional notes about this assignment..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  multiline
                  rows={3}
                />
              </AssignmentOptions>
            </>
          )}

          <ButtonGroup>
            <Button
              variant="outline"
              onClick={() => navigate('/supervisor/caregivers')}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleAssign}
              disabled={!selectedPatient || !selectedCaregiver || !startDate}
            >
              Create Assignment
            </Button>
          </ButtonGroup>
        </AssignmentCard>

        {/* Confirmation Modal */}
        <Modal
          isOpen={showConfirmModal}
          onClose={() => setShowConfirmModal(false)}
          title="Confirm Assignment"
          maxWidth="600px"
        >
          {selectedPatient && selectedCaregiver && (
            <div>
              <p style={{ marginBottom: '24px', color: theme.colors.text }}>
                Are you sure you want to assign <strong>{selectedCaregiver.name}</strong> to <strong>{selectedPatient.name}</strong>?
              </p>
              
              <div style={{ 
                background: theme.colors.lightGray,
                padding: '16px',
                borderRadius: '8px',
                marginBottom: '24px'
              }}>
                <div><strong>Assignment Type:</strong> {assignmentType}</div>
                <div><strong>Schedule:</strong> {schedule}</div>
                <div><strong>Start Date:</strong> {startDate}</div>
                {notes && <div><strong>Notes:</strong> {notes}</div>}
              </div>
              
              <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
                <Button
                  variant="outline"
                  onClick={() => setShowConfirmModal(false)}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={confirmAssignment}
                  loading={loading}
                >
                  Confirm Assignment
                </Button>
              </div>
            </div>
          )}
        </Modal>
      </AssignContainer>
    </DashboardLayout>
  );
};

export default SupervisorAssignCaregiverScreen;
