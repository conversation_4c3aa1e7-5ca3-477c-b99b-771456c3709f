import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Input, Modal } from '../common';
import { firebaseCaregiverService } from '../../services/firebaseCaregiverService';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '../../config/firebase';

const ManagementContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const RefreshButton = styled.button`
  padding: 8px;
  border: none;
  background: transparent;
  color: ${props => props.theme.colors.primary};
  cursor: pointer;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;

  &:hover {
    background: ${props => props.theme.colors.primaryLighter};
  }
`;

const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 32px;
`;

const PatientsSection = styled(Card)`
  padding: 24px;
  height: fit-content;
`;

const SectionTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 20px 0;
`;

const PatientsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const PatientItem = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid ${props => props.selected ? props.theme.colors.primary : 'transparent'};
  background: ${props => props.selected ? props.theme.colors.primaryLighter : props.theme.colors.lightGray};

  &:hover {
    background: ${props => props.theme.colors.primaryLighter};
  }
`;

const PatientAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: ${props => props.theme.colors.primary};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
`;

const PatientInfo = styled.div`
  flex: 1;
`;

const PatientName = styled.div`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const PatientEmail = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
`;

const CaregiversSection = styled(Card)`
  padding: 24px;
`;

const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
`;

const SearchContainer = styled.div`
  margin-bottom: 20px;
`;

const CaregiversList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const CaregiverCard = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 12px;
  border: 2px solid ${props => props.selected ? props.theme.colors.primary : 'transparent'};
`;

const CaregiverAvatar = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: ${props => props.theme.colors.primary};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
`;

const CaregiverInfo = styled.div`
  flex: 1;
`;

const CaregiverName = styled.div`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin-bottom: 4px;
`;

const CaregiverEmail = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
`;

const CaregiverActions = styled.div`
  display: flex;
  gap: 8px;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: ${props => props.theme.colors.textSecondary};
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
`;

const LoadingSpinner = styled.div`
  width: 50px;
  height: 50px;
  border: 4px solid ${props => props.theme.colors.border};
  border-top: 4px solid ${props => props.theme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const SupervisorCaregiverManagementScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(true);
  const [patients, setPatients] = useState([]);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [assignedCaregivers, setAssignedCaregivers] = useState([]);
  const [availableCaregivers, setAvailableCaregivers] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [selectedCaregiverId, setSelectedCaregiverId] = useState(null);
  const [assigning, setAssigning] = useState(false);

  const roleColors = getRoleColors(user?.role || 'supervisor');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Caregivers', icon: '👨‍⚕️', path: '/supervisor/caregivers' },
    { label: 'Patients', icon: '👥', path: '/supervisor/patients' },
    { label: 'Tracking', icon: '📍', path: '/supervisor/tracking' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  useEffect(() => {
    loadData();
  }, [user]);

  useEffect(() => {
    if (selectedPatient) {
      loadCaregivers();
    }
  }, [selectedPatient]);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadPatients(),
        loadAvailableCaregivers()
      ]);
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const loadPatients = async () => {
    try {
      // Get patients from Firestore
      const usersCollection = collection(db, 'users');
      const q = query(usersCollection, where('role', '==', 'patient'));
      const querySnapshot = await getDocs(q);
      
      const patientsData = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        patientsData.push({
          id: doc.id,
          uid: doc.id,
          ...data,
          name: `${data.firstName || ''} ${data.lastName || ''}`.trim() || data.email || 'Unknown Patient'
        });
      });

      setPatients(patientsData);
      
      // Select first patient by default
      if (patientsData.length > 0 && !selectedPatient) {
        setSelectedPatient(patientsData[0]);
      }
    } catch (error) {
      console.error('Error loading patients:', error);
      throw error;
    }
  };

  const loadCaregivers = async () => {
    if (!selectedPatient) return;

    try {
      const caregivers = await firebaseCaregiverService.getPatientCaregivers(selectedPatient.uid);
      setAssignedCaregivers(caregivers);
    } catch (error) {
      console.error('Error loading caregivers:', error);
      toast.error('Failed to load caregivers');
    }
  };

  const loadAvailableCaregivers = async () => {
    try {
      const caregivers = await firebaseCaregiverService.getAvailableCaregivers();
      setAvailableCaregivers(caregivers);
    } catch (error) {
      console.error('Error loading available caregivers:', error);
      toast.error('Failed to load available caregivers');
    }
  };

  const handleAssignCaregiver = async () => {
    if (!selectedPatient || !selectedCaregiverId) return;

    try {
      setAssigning(true);
      await firebaseCaregiverService.assignCaregiverToPatient(
        selectedPatient.uid,
        selectedCaregiverId,
        user.uid
      );

      toast.success('Caregiver assigned successfully');
      setShowAssignModal(false);
      setSelectedCaregiverId(null);
      await loadCaregivers();
    } catch (error) {
      console.error('Error assigning caregiver:', error);
      toast.error('Failed to assign caregiver');
    } finally {
      setAssigning(false);
    }
  };

  const handleRemoveCaregiver = async (caregiverId) => {
    if (!selectedPatient) return;

    if (!confirm('Are you sure you want to remove this caregiver?')) {
      return;
    }

    try {
      await firebaseCaregiverService.removeCaregiverAssignment(
        selectedPatient.uid,
        caregiverId
      );

      toast.success('Caregiver removed successfully');
      await loadCaregivers();
    } catch (error) {
      console.error('Error removing caregiver:', error);
      toast.error('Failed to remove caregiver');
    }
  };

  const getInitials = (person) => {
    const firstName = person.firstName || '';
    const lastName = person.lastName || '';
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() || '?';
  };

  const filteredAvailableCaregivers = availableCaregivers.filter(caregiver => {
    // Filter out already assigned caregivers
    if (assignedCaregivers.some(assigned => assigned.uid === caregiver.uid)) {
      return false;
    }

    const fullName = `${caregiver.firstName || ''} ${caregiver.lastName || ''}`.toLowerCase();
    const email = (caregiver.email || '').toLowerCase();
    const query = searchQuery.toLowerCase();
    return fullName.includes(query) || email.includes(query);
  });

  if (loading) {
    return (
      <DashboardLayout
        title="Caregiver Management"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <ManagementContainer>
          <LoadingContainer>
            <LoadingSpinner theme={theme} />
          </LoadingContainer>
        </ManagementContainer>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Caregiver Management"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <ManagementContainer>
        <Header>
          <Title theme={theme}>Caregiver Management</Title>
          <RefreshButton theme={theme} onClick={loadData} title="Refresh">
            🔄
          </RefreshButton>
        </Header>

        <ContentGrid>
          {/* Patients Section */}
          <PatientsSection theme={theme}>
            <SectionTitle theme={theme}>Select Patient</SectionTitle>
            <PatientsList>
              {patients.length === 0 ? (
                <EmptyState theme={theme}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>👥</div>
                  <div>No patients found</div>
                </EmptyState>
              ) : (
                patients.map(patient => (
                  <PatientItem
                    key={patient.uid}
                    theme={theme}
                    selected={selectedPatient?.uid === patient.uid}
                    onClick={() => setSelectedPatient(patient)}
                  >
                    <PatientAvatar theme={theme}>
                      {getInitials(patient)}
                    </PatientAvatar>
                    <PatientInfo>
                      <PatientName theme={theme}>{patient.name}</PatientName>
                      <PatientEmail theme={theme}>{patient.email}</PatientEmail>
                    </PatientInfo>
                  </PatientItem>
                ))
              )}
            </PatientsList>
          </PatientsSection>

          {/* Caregivers Section */}
          <CaregiversSection theme={theme}>
            <SectionHeader>
              <SectionTitle theme={theme}>
                {selectedPatient ? `Caregivers for ${selectedPatient.name}` : 'Select a patient'}
              </SectionTitle>
              {selectedPatient && (
                <ActionButtons>
                  <Button
                    variant="primary"
                    onClick={() => setShowAssignModal(true)}
                  >
                    Assign Caregiver
                  </Button>
                </ActionButtons>
              )}
            </SectionHeader>

            {selectedPatient ? (
              <>
                <CaregiversList>
                  {assignedCaregivers.length === 0 ? (
                    <EmptyState theme={theme}>
                      <div style={{ fontSize: '48px', marginBottom: '16px' }}>👨‍⚕️</div>
                      <div>No caregivers assigned</div>
                      <p>Assign caregivers to this patient to get started.</p>
                    </EmptyState>
                  ) : (
                    assignedCaregivers.map(caregiver => (
                      <CaregiverCard key={caregiver.uid} theme={theme}>
                        <CaregiverAvatar theme={theme}>
                          {getInitials(caregiver)}
                        </CaregiverAvatar>
                        <CaregiverInfo>
                          <CaregiverName theme={theme}>
                            {`${caregiver.firstName || ''} ${caregiver.lastName || ''}`.trim() || 'Caregiver'}
                          </CaregiverName>
                          <CaregiverEmail theme={theme}>
                            {caregiver.email}
                          </CaregiverEmail>
                        </CaregiverInfo>
                        <CaregiverActions>
                          <Button
                            variant="danger"
                            size="small"
                            onClick={() => handleRemoveCaregiver(caregiver.uid)}
                          >
                            Remove
                          </Button>
                        </CaregiverActions>
                      </CaregiverCard>
                    ))
                  )}
                </CaregiversList>
              </>
            ) : (
              <EmptyState theme={theme}>
                <div style={{ fontSize: '48px', marginBottom: '16px' }}>👈</div>
                <div>Select a patient to manage their caregivers</div>
              </EmptyState>
            )}
          </CaregiversSection>
        </ContentGrid>

        {/* Assign Caregiver Modal */}
        <Modal
          isOpen={showAssignModal}
          onClose={() => setShowAssignModal(false)}
          title="Assign Caregiver"
          maxWidth="600px"
        >
          <div>
            <SearchContainer>
              <Input
                placeholder="Search caregivers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                icon="🔍"
              />
            </SearchContainer>

            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              {filteredAvailableCaregivers.length === 0 ? (
                <EmptyState theme={theme}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>👨‍⚕️</div>
                  <div>No available caregivers found</div>
                </EmptyState>
              ) : (
                <CaregiversList>
                  {filteredAvailableCaregivers.map(caregiver => (
                    <CaregiverCard
                      key={caregiver.uid}
                      theme={theme}
                      selected={selectedCaregiverId === caregiver.uid}
                      onClick={() => setSelectedCaregiverId(caregiver.uid)}
                      style={{ cursor: 'pointer' }}
                    >
                      <CaregiverAvatar theme={theme}>
                        {getInitials(caregiver)}
                      </CaregiverAvatar>
                      <CaregiverInfo>
                        <CaregiverName theme={theme}>
                          {`${caregiver.firstName || ''} ${caregiver.lastName || ''}`.trim() || 'Caregiver'}
                        </CaregiverName>
                        <CaregiverEmail theme={theme}>
                          {caregiver.email}
                        </CaregiverEmail>
                      </CaregiverInfo>
                    </CaregiverCard>
                  ))}
                </CaregiversList>
              )}
            </div>

            <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end', marginTop: '24px' }}>
              <Button
                variant="outline"
                onClick={() => setShowAssignModal(false)}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleAssignCaregiver}
                loading={assigning}
                disabled={!selectedCaregiverId || assigning}
              >
                {assigning ? 'Assigning...' : 'Assign Caregiver'}
              </Button>
            </div>
          </div>
        </Modal>
      </ManagementContainer>
    </DashboardLayout>
  );
};

export default SupervisorCaregiverManagementScreen;
