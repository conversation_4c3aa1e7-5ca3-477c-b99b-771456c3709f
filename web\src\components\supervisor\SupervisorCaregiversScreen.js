import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Input, Select, Modal } from '../common';
import { formatDate } from '../../utils/dateUtils';

const CaregiversContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const Controls = styled.div`
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
`;

const CaregiversGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
`;

const CaregiverCard = styled(Card)`
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.lg};
  }
`;

const CaregiverHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
`;

const CaregiverAvatar = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #FF9800 0%, #FFB74D 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: 700;
  box-shadow: ${props => props.theme.shadows.md};
`;

const CaregiverInfo = styled.div`
  flex: 1;
`;

const CaregiverName = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 4px 0;
`;

const CaregiverMeta = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  margin-bottom: 4px;
`;

const StatusBadge = styled.span`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  
  ${props => {
    switch (props.status) {
      case 'active':
        return `background: #E8F5E8; color: #2E7D32;`;
      case 'busy':
        return `background: #FFF3E0; color: #F57C00;`;
      case 'offline':
        return `background: #F5F5F5; color: #757575;`;
      default:
        return `background: #F5F5F5; color: #757575;`;
    }
  }}
`;

const CaregiverStats = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin: 16px 0;
`;

const StatItem = styled.div`
  text-align: center;
  padding: 8px;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 8px;
`;

const StatValue = styled.div`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const StatLabel = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
  margin-top: 2px;
`;

const CaregiverActions = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 16px;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: ${props => props.theme.shadows.md};
`;

const EmptyIcon = styled.div`
  font-size: 64px;
  margin-bottom: 16px;
`;

const SupervisorCaregiversScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const navigate = useNavigate();

  const [caregivers, setCaregivers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [selectedCaregiver, setSelectedCaregiver] = useState(null);

  const roleColors = getRoleColors(user?.role || 'supervisor');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', path: '/dashboard' },
    { label: 'Caregivers', icon: '👨‍⚕️', path: '/supervisor/caregivers' },
    { label: 'Patients', icon: '👥', path: '/supervisor/patients' },
    { label: 'Map', icon: '🗺️', path: '/map' },
    { label: 'Profile', icon: '👤', path: '/profile' }
  ];

  useEffect(() => {
    loadCaregivers();
  }, []);

  const loadCaregivers = async () => {
    try {
      setLoading(true);
      
      // Mock caregiver data
      const mockCaregivers = [
        {
          id: 'caregiver-1',
          name: 'Sophie Martin',
          email: '<EMAIL>',
          phone: '+33 1 23 45 67 89',
          status: 'active',
          specialization: 'Alzheimer Care',
          experience: '5 years',
          assignedPatients: 8,
          completedActivities: 24,
          rating: 4.8,
          location: 'Paris 15th',
          lastActivity: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
          joinDate: new Date('2020-03-15')
        },
        {
          id: 'caregiver-2',
          name: 'Marc Dubois',
          email: '<EMAIL>',
          phone: '+33 1 98 76 54 32',
          status: 'busy',
          specialization: 'Parkinson Care',
          experience: '8 years',
          assignedPatients: 6,
          completedActivities: 18,
          rating: 4.9,
          location: 'Paris 12th',
          lastActivity: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
          joinDate: new Date('2018-09-20')
        },
        {
          id: 'caregiver-3',
          name: 'Claire Rousseau',
          email: '<EMAIL>',
          phone: '+33 1 11 22 33 44',
          status: 'offline',
          specialization: 'Dementia Care',
          experience: '3 years',
          assignedPatients: 5,
          completedActivities: 15,
          rating: 4.6,
          location: 'Paris 8th',
          lastActivity: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
          joinDate: new Date('2021-11-10')
        }
      ];
      
      setCaregivers(mockCaregivers);
      
    } catch (error) {
      console.error('Error loading caregivers:', error);
      toast.error('Failed to load caregivers');
    } finally {
      setLoading(false);
    }
  };

  const handleCaregiverClick = (caregiver) => {
    navigate(`/supervisor/caregivers/${caregiver.id}`);
  };

  const handleAssignPatients = (caregiver, e) => {
    e.stopPropagation();
    setSelectedCaregiver(caregiver);
    setShowAssignModal(true);
  };

  const handleViewPatients = (caregiver, e) => {
    e.stopPropagation();
    navigate(`/supervisor/caregivers/${caregiver.id}/patients`);
  };

  const handleContactCaregiver = (caregiver, e) => {
    e.stopPropagation();
    window.open(`tel:${caregiver.phone}`);
  };

  const handleAddCaregiver = () => {
    navigate('/scanner?type=caregiver');
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return 'Active';
      case 'busy': return 'Busy';
      case 'offline': return 'Offline';
      default: return status;
    }
  };

  const filteredCaregivers = caregivers.filter(caregiver => {
    const matchesSearch = caregiver.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         caregiver.specialization.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || caregiver.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <DashboardLayout
        title="Caregivers Management"
        roleName={user?.role || 'User'}
        menuItems={menuItems}
        headerBackgroundColor={roleColors.primary}
        accentColor={roleColors.secondary}
      >
        <CaregiversContainer>
          <Card theme={theme}>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              Loading caregivers...
            </div>
          </Card>
        </CaregiversContainer>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Caregivers Management"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <CaregiversContainer>
        <Header>
          <Title theme={theme}>Caregivers ({filteredCaregivers.length})</Title>
          <Controls>
            <Input
              placeholder="Search caregivers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{ width: '250px' }}
              icon="🔍"
            />
            <Select
              value={statusFilter}
              onChange={setStatusFilter}
              options={[
                { value: 'all', label: 'All Status' },
                { value: 'active', label: 'Active' },
                { value: 'busy', label: 'Busy' },
                { value: 'offline', label: 'Offline' }
              ]}
              style={{ width: '150px' }}
            />
            <Button
              variant="primary"
              onClick={handleAddCaregiver}
              icon="+"
            >
              Add Caregiver
            </Button>
            <Button
              variant="outline"
              onClick={loadCaregivers}
              loading={loading}
              icon="🔄"
            >
              Refresh
            </Button>
          </Controls>
        </Header>

        {filteredCaregivers.length === 0 ? (
          <EmptyState theme={theme}>
            <EmptyIcon>👨‍⚕️</EmptyIcon>
            <h3>No caregivers found</h3>
            <p>
              {searchQuery || statusFilter !== 'all' 
                ? 'No caregivers match your current filters.'
                : 'No caregivers have been added yet.'
              }
            </p>
            <Button 
              variant="primary" 
              onClick={handleAddCaregiver}
              style={{ marginTop: '16px' }}
            >
              Add First Caregiver
            </Button>
          </EmptyState>
        ) : (
          <CaregiversGrid>
            {filteredCaregivers.map(caregiver => (
              <CaregiverCard
                key={caregiver.id}
                onClick={() => handleCaregiverClick(caregiver)}
                theme={theme}
              >
                <CaregiverHeader>
                  <CaregiverAvatar>
                    {caregiver.name.charAt(0)}
                  </CaregiverAvatar>
                  <CaregiverInfo>
                    <CaregiverName theme={theme}>{caregiver.name}</CaregiverName>
                    <CaregiverMeta theme={theme}>
                      {caregiver.specialization} • {caregiver.experience}
                    </CaregiverMeta>
                    <CaregiverMeta theme={theme}>
                      📍 {caregiver.location} • ⭐ {caregiver.rating}
                    </CaregiverMeta>
                  </CaregiverInfo>
                  <StatusBadge status={caregiver.status}>
                    {getStatusText(caregiver.status)}
                  </StatusBadge>
                </CaregiverHeader>

                <CaregiverStats>
                  <StatItem theme={theme}>
                    <StatValue theme={theme}>{caregiver.assignedPatients}</StatValue>
                    <StatLabel theme={theme}>Patients</StatLabel>
                  </StatItem>
                  <StatItem theme={theme}>
                    <StatValue theme={theme}>{caregiver.completedActivities}</StatValue>
                    <StatLabel theme={theme}>Activities</StatLabel>
                  </StatItem>
                  <StatItem theme={theme}>
                    <StatValue theme={theme}>{caregiver.rating}</StatValue>
                    <StatLabel theme={theme}>Rating</StatLabel>
                  </StatItem>
                </CaregiverStats>

                <div style={{ 
                  fontSize: '12px', 
                  color: theme.colors.textSecondary,
                  marginBottom: '16px'
                }}>
                  Last activity: {formatDate(caregiver.lastActivity, 'datetime')}
                </div>

                <CaregiverActions>
                  <Button
                    variant="primary"
                    size="small"
                    onClick={(e) => handleAssignPatients(caregiver, e)}
                    icon="👥"
                  >
                    Assign
                  </Button>
                  <Button
                    variant="outline"
                    size="small"
                    onClick={(e) => handleViewPatients(caregiver, e)}
                    icon="👁️"
                  >
                    Patients
                  </Button>
                  <Button
                    variant="outline"
                    size="small"
                    onClick={(e) => handleContactCaregiver(caregiver, e)}
                    icon="📞"
                  >
                    Call
                  </Button>
                </CaregiverActions>
              </CaregiverCard>
            ))}
          </CaregiversGrid>
        )}

        {/* Assign Patients Modal */}
        <Modal
          isOpen={showAssignModal}
          onClose={() => setShowAssignModal(false)}
          title="Assign Patients"
          maxWidth="600px"
        >
          {selectedCaregiver && (
            <div>
              <p>Assign patients to {selectedCaregiver.name}</p>
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <div style={{ fontSize: '48px', marginBottom: '16px' }}>🚧</div>
                <p>Patient assignment interface coming soon!</p>
                <Button 
                  variant="outline" 
                  onClick={() => setShowAssignModal(false)}
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </Modal>
      </CaregiversContainer>
    </DashboardLayout>
  );
};

export default SupervisorCaregiversScreen;
