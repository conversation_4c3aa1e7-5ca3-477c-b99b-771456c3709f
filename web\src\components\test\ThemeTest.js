import React from 'react';
import styled from 'styled-components';
import { useTheme } from '../../contexts/ThemeContext';
import Button from '../common/Button';

const TestContainer = styled.div`
  padding: 20px;
  background: ${props => props.theme.colors.background || '#fff'};
  color: ${props => props.theme.colors.text || '#333'};
`;

const TestCard = styled.div`
  padding: 16px;
  margin: 16px 0;
  background: ${props => props.theme.colors.lightGray || '#f5f5f5'};
  border: 1px solid ${props => props.theme.colors.border || '#e0e0e0'};
  border-radius: 8px;
`;

const ColorSwatch = styled.div`
  display: inline-block;
  width: 40px;
  height: 40px;
  margin: 4px;
  border-radius: 4px;
  background: ${props => props.color};
  border: 1px solid #ccc;
`;

const ThemeTest = () => {
  const { theme, userRole } = useTheme();

  if (!theme || !theme.colors) {
    return (
      <div style={{ padding: '20px', color: 'red' }}>
        <h2>❌ Theme Error</h2>
        <p>Theme is not loaded properly</p>
      </div>
    );
  }

  return (
    <TestContainer theme={theme}>
      <h1>🎨 Theme Test Component</h1>
      <p>Current Role: <strong>{userRole || 'Unknown'}</strong></p>
      
      <TestCard theme={theme}>
        <h3>Theme Colors Test</h3>
        <div>
          <p>Primary: <ColorSwatch color={theme.colors.primary} /></p>
          <p>Secondary: <ColorSwatch color={theme.colors.secondary} /></p>
          <p>Background: <ColorSwatch color={theme.colors.background} /></p>
          <p>Light Gray: <ColorSwatch color={theme.colors.lightGray} /></p>
          <p>Text: <ColorSwatch color={theme.colors.text} /></p>
        </div>
      </TestCard>

      <TestCard theme={theme}>
        <h3>Button Components Test</h3>
        <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
          <Button variant="primary">Primary</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="outline">Outline</Button>
          <Button variant="ghost">Ghost</Button>
          <Button variant="danger">Danger</Button>
        </div>
      </TestCard>

      <TestCard theme={theme}>
        <h3>Theme Object Debug</h3>
        <pre style={{ fontSize: '12px', overflow: 'auto' }}>
          {JSON.stringify(theme.colors, null, 2)}
        </pre>
      </TestCard>
    </TestContainer>
  );
};

export default ThemeTest;
