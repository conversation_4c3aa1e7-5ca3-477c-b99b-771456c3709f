import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { Button, Modal } from '../common';

const VideoCallContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #000;
  z-index: 1000;
  display: flex;
  flex-direction: column;
`;

const VideoArea = styled.div`
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const MainVideo = styled.div`
  width: 100%;
  height: 100%;
  background: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
`;

const VideoElement = styled.video`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const VideoPlaceholder = styled.div`
  text-align: center;
  color: white;
`;

const PlaceholderIcon = styled.div`
  font-size: 64px;
  margin-bottom: 16px;
`;

const PlaceholderText = styled.h3`
  font-size: 24px;
  margin: 0 0 8px 0;
`;

const PlaceholderSubtext = styled.p`
  font-size: 16px;
  margin: 0;
  opacity: 0.7;
`;

const SelfVideo = styled.div`
  position: absolute;
  top: 20px;
  right: 20px;
  width: 200px;
  height: 150px;
  background: #333;
  border-radius: 12px;
  overflow: hidden;
  border: 2px solid #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
`;

const SelfVideoElement = styled.video`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const CallInfo = styled.div`
  position: absolute;
  top: 20px;
  left: 20px;
  color: white;
  background: rgba(0, 0, 0, 0.5);
  padding: 16px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
`;

const ParticipantName = styled.h3`
  font-size: 20px;
  margin: 0 0 4px 0;
`;

const CallDuration = styled.div`
  font-size: 14px;
  opacity: 0.8;
`;

const CallStatus = styled.div`
  font-size: 14px;
  margin-top: 4px;
  color: #4CAF50;
`;

const ControlsBar = styled.div`
  background: rgba(0, 0, 0, 0.8);
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  backdrop-filter: blur(10px);
`;

const ControlButton = styled.button`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  ${props => {
    switch (props.variant) {
      case 'danger':
        return `
          background: #F44336;
          color: white;
          &:hover { background: #D32F2F; }
        `;
      case 'mute':
        return `
          background: ${props.active ? '#F44336' : '#4CAF50'};
          color: white;
          &:hover { opacity: 0.8; }
        `;
      case 'video':
        return `
          background: ${props.active ? '#F44336' : '#4CAF50'};
          color: white;
          &:hover { opacity: 0.8; }
        `;
      default:
        return `
          background: #666;
          color: white;
          &:hover { background: #777; }
        `;
    }
  }}
`;

const ChatPanel = styled.div`
  position: absolute;
  right: ${props => props.isOpen ? '0' : '-350px'};
  top: 0;
  bottom: 80px;
  width: 350px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  transition: right 0.3s ease;
  display: flex;
  flex-direction: column;
  border-left: 1px solid rgba(0, 0, 0, 0.1);
`;

const ChatHeader = styled.div`
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.9);
`;

const ChatTitle = styled.h4`
  margin: 0;
  font-size: 16px;
  color: #333;
`;

const ChatMessages = styled.div`
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const ChatMessage = styled.div`
  padding: 8px 12px;
  border-radius: 12px;
  max-width: 80%;
  align-self: ${props => props.isOwn ? 'flex-end' : 'flex-start'};
  background: ${props => props.isOwn ? '#2196F3' : '#f0f0f0'};
  color: ${props => props.isOwn ? 'white' : '#333'};
  font-size: 14px;
`;

const ChatInput = styled.div`
  padding: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 8px;
`;

const ChatInputField = styled.input`
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 20px;
  outline: none;
  font-size: 14px;
`;

const ChatSendButton = styled.button`
  padding: 8px 16px;
  background: #2196F3;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  
  &:hover {
    background: #1976D2;
  }
`;

const VideoCallComponent = () => {
  const { callId } = useParams();
  const { user } = useAuth();
  const { theme } = useTheme();
  const navigate = useNavigate();

  const [callState, setCallState] = useState('connecting'); // connecting, connected, ended
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOff, setIsVideoOff] = useState(false);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [callDuration, setCallDuration] = useState(0);
  const [participant, setParticipant] = useState(null);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [showEndCallModal, setShowEndCallModal] = useState(false);

  const localVideoRef = useRef(null);
  const remoteVideoRef = useRef(null);
  const callStartTime = useRef(null);

  useEffect(() => {
    initializeCall();
    return () => {
      endCall();
    };
  }, []);

  useEffect(() => {
    if (callState === 'connected') {
      callStartTime.current = Date.now();
      const interval = setInterval(() => {
        setCallDuration(Math.floor((Date.now() - callStartTime.current) / 1000));
      }, 1000);
      
      return () => clearInterval(interval);
    }
  }, [callState]);

  const initializeCall = async () => {
    try {
      // Mock participant data
      const mockParticipant = {
        id: 'doctor-1',
        name: 'Dr. Sarah Johnson',
        role: 'doctor',
        avatar: 'SJ'
      };
      
      setParticipant(mockParticipant);
      
      // Simulate connection process
      setTimeout(() => {
        setCallState('connected');
        toast.success('Call connected successfully');
      }, 2000);
      
      // Initialize local video stream
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: true,
          audio: true
        });
        
        if (localVideoRef.current) {
          localVideoRef.current.srcObject = stream;
        }
      } catch (error) {
        console.error('Error accessing media devices:', error);
        toast.error('Unable to access camera/microphone');
      }
      
    } catch (error) {
      console.error('Error initializing call:', error);
      toast.error('Failed to initialize call');
      setCallState('ended');
    }
  };

  const endCall = () => {
    // Stop all media streams
    if (localVideoRef.current && localVideoRef.current.srcObject) {
      const tracks = localVideoRef.current.srcObject.getTracks();
      tracks.forEach(track => track.stop());
    }
    
    setCallState('ended');
    navigate('/dashboard');
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
    // In a real implementation, you would mute/unmute the audio track
    toast.info(isMuted ? 'Microphone unmuted' : 'Microphone muted');
  };

  const toggleVideo = () => {
    setIsVideoOff(!isVideoOff);
    // In a real implementation, you would enable/disable the video track
    toast.info(isVideoOff ? 'Camera enabled' : 'Camera disabled');
  };

  const toggleChat = () => {
    setIsChatOpen(!isChatOpen);
  };

  const sendMessage = () => {
    if (newMessage.trim()) {
      const message = {
        id: Date.now(),
        text: newMessage,
        sender: user.displayName,
        isOwn: true,
        timestamp: new Date()
      };
      
      setMessages([...messages, message]);
      setNewMessage('');
      
      // Simulate response from other participant
      setTimeout(() => {
        const response = {
          id: Date.now() + 1,
          text: 'Thank you for your message.',
          sender: participant?.name,
          isOwn: false,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, response]);
      }, 1000);
    }
  };

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleEndCall = () => {
    setShowEndCallModal(true);
  };

  const confirmEndCall = () => {
    setShowEndCallModal(false);
    endCall();
  };

  if (callState === 'ended') {
    return null; // Component will unmount and navigate away
  }

  return (
    <VideoCallContainer>
      <VideoArea>
        <MainVideo>
          {callState === 'connected' ? (
            <VideoElement
              ref={remoteVideoRef}
              autoPlay
              playsInline
              style={{ display: isVideoOff ? 'none' : 'block' }}
            />
          ) : (
            <VideoPlaceholder>
              <PlaceholderIcon>📹</PlaceholderIcon>
              <PlaceholderText>
                {callState === 'connecting' ? 'Connecting...' : 'Call Ended'}
              </PlaceholderText>
              <PlaceholderSubtext>
                {callState === 'connecting' 
                  ? `Connecting to ${participant?.name || 'participant'}...`
                  : 'Thank you for using NeuroCare'
                }
              </PlaceholderSubtext>
            </VideoPlaceholder>
          )}
          
          {!isVideoOff && (
            <VideoPlaceholder style={{ position: 'absolute' }}>
              <PlaceholderIcon>👤</PlaceholderIcon>
              <PlaceholderText>{participant?.name}</PlaceholderText>
            </VideoPlaceholder>
          )}
        </MainVideo>

        <SelfVideo>
          <SelfVideoElement
            ref={localVideoRef}
            autoPlay
            playsInline
            muted
            style={{ display: isVideoOff ? 'none' : 'block' }}
          />
          {isVideoOff && (
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              height: '100%',
              color: 'white',
              fontSize: '32px'
            }}>
              📷
            </div>
          )}
        </SelfVideo>

        <CallInfo>
          <ParticipantName>{participant?.name || 'Unknown'}</ParticipantName>
          <CallDuration>Duration: {formatDuration(callDuration)}</CallDuration>
          <CallStatus>
            {callState === 'connecting' ? '🔄 Connecting...' : '🟢 Connected'}
          </CallStatus>
        </CallInfo>

        <ChatPanel isOpen={isChatOpen}>
          <ChatHeader>
            <ChatTitle>Chat</ChatTitle>
          </ChatHeader>
          
          <ChatMessages>
            {messages.map(message => (
              <ChatMessage key={message.id} isOwn={message.isOwn}>
                {message.text}
              </ChatMessage>
            ))}
          </ChatMessages>
          
          <ChatInput>
            <ChatInputField
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Type a message..."
              onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
            />
            <ChatSendButton onClick={sendMessage}>
              Send
            </ChatSendButton>
          </ChatInput>
        </ChatPanel>
      </VideoArea>

      <ControlsBar>
        <ControlButton
          variant="mute"
          active={isMuted}
          onClick={toggleMute}
          title={isMuted ? 'Unmute' : 'Mute'}
        >
          {isMuted ? '🔇' : '🎤'}
        </ControlButton>

        <ControlButton
          variant="video"
          active={isVideoOff}
          onClick={toggleVideo}
          title={isVideoOff ? 'Turn on camera' : 'Turn off camera'}
        >
          {isVideoOff ? '📷' : '📹'}
        </ControlButton>

        <ControlButton
          onClick={toggleChat}
          title="Toggle chat"
        >
          💬
        </ControlButton>

        <ControlButton
          title="Share screen"
        >
          🖥️
        </ControlButton>

        <ControlButton
          variant="danger"
          onClick={handleEndCall}
          title="End call"
        >
          📞
        </ControlButton>
      </ControlsBar>

      {/* End Call Confirmation Modal */}
      <Modal
        isOpen={showEndCallModal}
        onClose={() => setShowEndCallModal(false)}
        title="End Call"
        maxWidth="400px"
      >
        <div>
          <p style={{ marginBottom: '24px', color: theme.colors.text }}>
            Are you sure you want to end this call with {participant?.name}?
          </p>
          
          <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
            <Button
              variant="outline"
              onClick={() => setShowEndCallModal(false)}
            >
              Continue Call
            </Button>
            <Button
              variant="danger"
              onClick={confirmEndCall}
            >
              End Call
            </Button>
          </div>
        </div>
      </Modal>
    </VideoCallContainer>
  );
};

export default VideoCallComponent;
