import React from 'react';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../dashboards/DashboardLayout';

const VideoContainer = styled.div`
  max-width: 1000px;
  margin: 0 auto;
`;

const ComingSoon = styled.div`
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: ${props => props.theme.shadows.md};
`;

const ComingSoonIcon = styled.div`
  font-size: 80px;
  margin-bottom: 24px;
`;

const ComingSoonTitle = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0 0 16px 0;
`;

const ComingSoonMessage = styled.p`
  font-size: 18px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
  line-height: 1.6;
`;

const VideoCallScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', screen: 'dashboard' },
    { label: 'Video Calls', icon: '📹', screen: 'video-call' },
    { label: 'Profile', icon: '👤', screen: 'profile' }
  ];

  return (
    <DashboardLayout
      title="Video Consultations"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <VideoContainer>
        <ComingSoon theme={theme}>
          <ComingSoonIcon>📹</ComingSoonIcon>
          <ComingSoonTitle theme={theme}>Video Consultations</ComingSoonTitle>
          <ComingSoonMessage theme={theme}>
            This feature is coming soon! You'll be able to conduct secure video consultations with healthcare providers here.
          </ComingSoonMessage>
        </ComingSoon>
      </VideoContainer>
    </DashboardLayout>
  );
};

export default VideoCallScreen;
