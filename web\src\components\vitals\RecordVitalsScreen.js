import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useVitals } from '../../contexts/VitalsContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Input } from '../common';
import useForm from '../../hooks/useForm';
import { validateVitalSign } from '../../utils/validation';
import {
  VITAL_SIGN_TYPES,
  VITAL_SIGN_DISPLAY_NAMES,
  VITAL_SIGN_UNITS,
  VITAL_SIGN_NORMAL_RANGES
} from '../../utils/constants';

const RecordContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const BackButton = styled(But<PERSON>)`
  margin-bottom: 16px;
`;

const FormCard = styled(Card)`
  margin-bottom: 24px;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid ${props => props.theme.colors.borderLight};
`;

const VitalsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
`;

const VitalInputCard = styled.div`
  border: 2px solid ${props => props.theme.colors.border};
  border-radius: 12px;
  padding: 20px;
  background: white;
  transition: all 0.3s ease;

  &:focus-within {
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.theme.colors.primary}20;
  }
`;

const VitalInputHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
`;

const VitalIcon = styled.div`
  font-size: 24px;
`;

const VitalInputTitle = styled.h4`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const VitalInputSubtitle = styled.p`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 4px 0 0 0;
`;

const VitalInputWrapper = styled.div`
  position: relative;
`;

const VitalInput = styled(Input)`
  font-size: 18px;
  font-weight: 600;
  text-align: center;
`;

const VitalUnit = styled.div`
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  font-weight: 500;
  pointer-events: none;
`;

const NormalRange = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
  text-align: center;
  margin-top: 8px;
`;

const QuickActions = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
`;

const QuickActionButton = styled.button`
  padding: 4px 8px;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 4px;
  background: white;
  color: ${props => props.theme.colors.textSecondary};
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: ${props => props.theme.colors.lightGray};
    border-color: ${props => props.theme.colors.primary};
  }
`;

const NotesSection = styled.div`
  margin-top: 24px;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 32px;
`;

const RecordVitalsScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const { addVitalSigns } = useVitals();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', screen: 'dashboard' },
    { label: 'Vitals', icon: '❤️', screen: 'vitals' },
    { label: 'Profile', icon: '👤', screen: 'profile' }
  ];

  const vitalSignsConfig = [
    {
      type: VITAL_SIGN_TYPES.BLOOD_PRESSURE_SYSTOLIC,
      icon: '🩸',
      title: 'Blood Pressure (Systolic)',
      subtitle: 'Upper number',
      placeholder: '120',
      quickActions: ['110', '120', '130', '140']
    },
    {
      type: VITAL_SIGN_TYPES.BLOOD_PRESSURE_DIASTOLIC,
      icon: '🩸',
      title: 'Blood Pressure (Diastolic)',
      subtitle: 'Lower number',
      placeholder: '80',
      quickActions: ['70', '80', '90', '100']
    },
    {
      type: VITAL_SIGN_TYPES.HEART_RATE,
      icon: '❤️',
      title: 'Heart Rate',
      subtitle: 'Beats per minute',
      placeholder: '72',
      quickActions: ['60', '70', '80', '90']
    },
    {
      type: VITAL_SIGN_TYPES.TEMPERATURE,
      icon: '🌡️',
      title: 'Body Temperature',
      subtitle: 'Celsius',
      placeholder: '36.5',
      quickActions: ['36.0', '36.5', '37.0', '37.5']
    },
    {
      type: VITAL_SIGN_TYPES.OXYGEN_SATURATION,
      icon: '🫁',
      title: 'Oxygen Saturation',
      subtitle: 'Percentage',
      placeholder: '98',
      quickActions: ['95', '96', '97', '98', '99']
    },
    {
      type: VITAL_SIGN_TYPES.BLOOD_SUGAR,
      icon: '🍯',
      title: 'Blood Sugar',
      subtitle: 'mg/dL',
      placeholder: '100',
      quickActions: ['80', '90', '100', '110']
    },
    {
      type: VITAL_SIGN_TYPES.WEIGHT,
      icon: '⚖️',
      title: 'Weight',
      subtitle: 'Kilograms',
      placeholder: '70',
      quickActions: []
    },
    {
      type: VITAL_SIGN_TYPES.HEIGHT,
      icon: '📏',
      title: 'Height',
      subtitle: 'Centimeters',
      placeholder: '170',
      quickActions: []
    }
  ];

  const initialValues = vitalSignsConfig.reduce((acc, vital) => {
    acc[vital.type] = '';
    return acc;
  }, { notes: '' });

  const validators = vitalSignsConfig.reduce((acc, vital) => {
    acc[vital.type] = (value) => {
      if (!value) return { isValid: true, message: '' }; // Optional fields
      return validateVitalSign(vital.type, value);
    };
    return acc;
  }, {});

  const {
    values,
    errors,
    handleChange,
    handleSubmit,
    setValue,
    getFieldProps
  } = useForm(
    initialValues,
    validators,
    async (formData) => {
      setLoading(true);
      try {
        // Filter out empty values
        const vitalsData = {};
        vitalSignsConfig.forEach(vital => {
          if (formData[vital.type] && formData[vital.type].trim() !== '') {
            vitalsData[vital.type] = parseFloat(formData[vital.type]);
          }
        });

        if (Object.keys(vitalsData).length === 0) {
          toast.error('Please enter at least one vital sign measurement');
          return;
        }

        const vitalRecord = {
          patientId: user.uid,
          values: vitalsData,
          notes: formData.notes,
          recordedAt: new Date().toISOString(),
          recordedBy: user.uid
        };

        await addVitalSigns(vitalRecord);
        toast.success('Vital signs recorded successfully!');
        navigate('/vitals');
      } catch (error) {
        toast.error('Failed to record vital signs. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  );

  const handleQuickAction = (vitalType, value) => {
    setValue(vitalType, value);
  };

  const getNormalRange = (type) => {
    const range = VITAL_SIGN_NORMAL_RANGES[type];
    if (!range) return null;
    return `Normal: ${range.min} - ${range.max} ${VITAL_SIGN_UNITS[type]}`;
  };

  const hasAnyValue = () => {
    return vitalSignsConfig.some(vital => values[vital.type] && values[vital.type].trim() !== '');
  };

  return (
    <DashboardLayout
      title="Record Vitals"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <RecordContainer>
        <BackButton
          variant="ghost"
          onClick={() => navigate('/vitals')}
          icon="←"
        >
          Back to Vitals
        </BackButton>

        <FormCard title="Record Vital Signs" theme={theme}>
          <form onSubmit={handleSubmit}>
            <FormSection>
              <SectionTitle theme={theme}>Vital Signs Measurements</SectionTitle>
              <p style={{
                color: theme.colors.textSecondary,
                marginBottom: '24px',
                fontSize: '14px'
              }}>
                Enter your current vital signs. You can record one or multiple measurements at once.
              </p>

              <VitalsGrid>
                {vitalSignsConfig.map(vital => (
                  <VitalInputCard key={vital.type} theme={theme}>
                    <VitalInputHeader>
                      <VitalIcon>{vital.icon}</VitalIcon>
                      <div>
                        <VitalInputTitle theme={theme}>{vital.title}</VitalInputTitle>
                        <VitalInputSubtitle theme={theme}>{vital.subtitle}</VitalInputSubtitle>
                      </div>
                    </VitalInputHeader>

                    <VitalInputWrapper>
                      <VitalInput
                        type="number"
                        step="0.1"
                        placeholder={vital.placeholder}
                        value={values[vital.type]}
                        onChange={(e) => setValue(vital.type, e.target.value)}
                        error={errors[vital.type]}
                      />
                      <VitalUnit theme={theme}>
                        {VITAL_SIGN_UNITS[vital.type]}
                      </VitalUnit>
                    </VitalInputWrapper>

                    {getNormalRange(vital.type) && (
                      <NormalRange theme={theme}>
                        {getNormalRange(vital.type)}
                      </NormalRange>
                    )}

                    {vital.quickActions.length > 0 && (
                      <QuickActions>
                        {vital.quickActions.map(action => (
                          <QuickActionButton
                            key={action}
                            type="button"
                            onClick={() => handleQuickAction(vital.type, action)}
                            theme={theme}
                          >
                            {action}
                          </QuickActionButton>
                        ))}
                      </QuickActions>
                    )}
                  </VitalInputCard>
                ))}
              </VitalsGrid>
            </FormSection>

            <NotesSection>
              <Input
                {...getFieldProps('notes')}
                label="Additional Notes"
                placeholder="Any additional information about your measurements or how you're feeling"
                multiline
                rows={3}
              />
            </NotesSection>

            <ButtonGroup>
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/vitals')}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={loading}
                disabled={!hasAnyValue()}
              >
                Save Vital Signs
              </Button>
            </ButtonGroup>
          </form>
        </FormCard>
      </RecordContainer>
    </DashboardLayout>
  );
};

export default RecordVitalsScreen;
