import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useVitals } from '../../contexts/VitalsContext';
import DashboardLayout from '../dashboards/DashboardLayout';
import { Button, Card, Modal, Input } from '../common';
import { formatDate, isToday } from '../../utils/dateUtils';
import {
  VITAL_SIGN_TYPES,
  VITAL_SIGN_DISPLAY_NAMES,
  VITAL_SIGN_UNITS,
  VITAL_SIGN_NORMAL_RANGES
} from '../../utils/constants';
import { validateVitalSign } from '../../utils/validation';

const VitalsContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const VitalsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
`;

const VitalCard = styled(Card)`
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.lg};
  }
`;

const VitalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const VitalIcon = styled.div`
  font-size: 32px;
`;

const VitalTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const VitalValue = styled.div`
  font-size: 36px;
  font-weight: 700;
  color: ${props => {
    if (props.status === 'normal') return '#4CAF50';
    if (props.status === 'warning') return '#FF9800';
    if (props.status === 'danger') return '#F44336';
    return props.theme.colors.text;
  }};
  margin-bottom: 8px;
`;

const VitalUnit = styled.span`
  font-size: 18px;
  font-weight: 400;
  color: ${props => props.theme.colors.textSecondary};
  margin-left: 8px;
`;

const VitalStatus = styled.div`
  font-size: 14px;
  color: ${props => {
    if (props.status === 'normal') return '#4CAF50';
    if (props.status === 'warning') return '#FF9800';
    if (props.status === 'danger') return '#F44336';
    return props.theme.colors.textSecondary;
  }};
  font-weight: 500;
`;

const VitalDate = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
  margin-top: 8px;
`;

const HistorySection = styled.div`
  margin-top: 32px;
`;

const HistoryHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const HistoryTitle = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const HistoryList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const HistoryItem = styled(Card)`
  padding: 16px;
`;

const HistoryItemHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const HistoryItemTitle = styled.h4`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const HistoryItemDate = styled.span`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
`;

const HistoryItemValues = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
`;

const HistoryItemValue = styled.div`
  text-align: center;
  padding: 8px;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 8px;
`;

const HistoryItemValueLabel = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
  margin-bottom: 4px;
`;

const HistoryItemValueNumber = styled.div`
  font-size: 18px;
  font-weight: 600;
  color: ${props => {
    if (props.status === 'normal') return '#4CAF50';
    if (props.status === 'warning') return '#FF9800';
    if (props.status === 'danger') return '#F44336';
    return props.theme.colors.text;
  }};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: ${props => props.theme.shadows.md};
`;

const EmptyIcon = styled.div`
  font-size: 64px;
  margin-bottom: 16px;
`;

const EmptyTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 8px 0;
`;

const EmptyMessage = styled.p`
  font-size: 16px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0 0 24px 0;
`;

const VitalsScreen = () => {
  const { user } = useAuth();
  const { theme, getRoleColors } = useTheme();
  const { vitals, loading, addVitalSigns, getLatestVitals, getVitalsHistory } = useVitals();
  const navigate = useNavigate();

  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedVital, setSelectedVital] = useState(null);
  const [latestVitals, setLatestVitals] = useState({});

  const roleColors = getRoleColors(user?.role || 'patient');

  const menuItems = [
    { label: 'Dashboard', icon: '🏠', screen: 'dashboard' },
    { label: 'Vitals', icon: '❤️', screen: 'vitals' },
    { label: 'Profile', icon: '👤', screen: 'profile' }
  ];

  useEffect(() => {
    loadLatestVitals();
  }, [vitals]);

  const loadLatestVitals = () => {
    const latest = {};
    Object.values(VITAL_SIGN_TYPES).forEach(type => {
      const vitalData = getLatestVitals(type);
      if (vitalData) {
        latest[type] = vitalData;
      }
    });
    setLatestVitals(latest);
  };

  const getVitalStatus = (type, value) => {
    const ranges = VITAL_SIGN_NORMAL_RANGES[type];
    if (!ranges || !value) return 'unknown';

    const numValue = parseFloat(value);
    if (numValue >= ranges.min && numValue <= ranges.max) {
      return 'normal';
    } else if (numValue < ranges.min * 0.8 || numValue > ranges.max * 1.2) {
      return 'danger';
    } else {
      return 'warning';
    }
  };

  const getVitalStatusText = (status) => {
    switch (status) {
      case 'normal': return 'Normal';
      case 'warning': return 'Attention';
      case 'danger': return 'Abnormal';
      default: return 'No data';
    }
  };

  const getVitalIcon = (type) => {
    switch (type) {
      case VITAL_SIGN_TYPES.BLOOD_PRESSURE_SYSTOLIC:
      case VITAL_SIGN_TYPES.BLOOD_PRESSURE_DIASTOLIC:
        return '🩸';
      case VITAL_SIGN_TYPES.HEART_RATE:
        return '❤️';
      case VITAL_SIGN_TYPES.TEMPERATURE:
        return '🌡️';
      case VITAL_SIGN_TYPES.OXYGEN_SATURATION:
        return '🫁';
      case VITAL_SIGN_TYPES.BLOOD_SUGAR:
        return '🍯';
      case VITAL_SIGN_TYPES.WEIGHT:
        return '⚖️';
      case VITAL_SIGN_TYPES.HEIGHT:
        return '📏';
      default:
        return '📊';
    }
  };

  const handleVitalClick = (type) => {
    setSelectedVital(type);
    // Navigate to detailed view or show history
    navigate(`/vitals/${type}`);
  };

  const handleAddVitals = () => {
    setShowAddModal(true);
  };

  const recentHistory = getVitalsHistory(7); // Last 7 days

  return (
    <DashboardLayout
      title="Vital Signs"
      roleName={user?.role || 'User'}
      menuItems={menuItems}
      headerBackgroundColor={roleColors.primary}
      accentColor={roleColors.secondary}
    >
      <VitalsContainer>
        <Header>
          <Title theme={theme}>My Vital Signs</Title>
          <Button
            variant="primary"
            onClick={handleAddVitals}
            icon="+"
          >
            Record Vitals
          </Button>
        </Header>

        {Object.keys(latestVitals).length > 0 ? (
          <>
            <VitalsGrid>
              {Object.values(VITAL_SIGN_TYPES).map(type => {
                const vitalData = latestVitals[type];
                const value = vitalData?.value;
                const status = value ? getVitalStatus(type, value) : 'unknown';

                return (
                  <VitalCard
                    key={type}
                    onClick={() => handleVitalClick(type)}
                    theme={theme}
                  >
                    <VitalHeader>
                      <div>
                        <VitalIcon>{getVitalIcon(type)}</VitalIcon>
                        <VitalTitle theme={theme}>
                          {VITAL_SIGN_DISPLAY_NAMES[type]}
                        </VitalTitle>
                      </div>
                    </VitalHeader>

                    <VitalValue status={status} theme={theme}>
                      {value || '--'}
                      <VitalUnit theme={theme}>
                        {VITAL_SIGN_UNITS[type]}
                      </VitalUnit>
                    </VitalValue>

                    <VitalStatus status={status}>
                      {getVitalStatusText(status)}
                    </VitalStatus>

                    {vitalData && (
                      <VitalDate theme={theme}>
                        {isToday(vitalData.recordedAt)
                          ? 'Today'
                          : formatDate(vitalData.recordedAt, 'short')
                        }
                      </VitalDate>
                    )}
                  </VitalCard>
                );
              })}
            </VitalsGrid>

            {recentHistory.length > 0 && (
              <HistorySection>
                <HistoryHeader>
                  <HistoryTitle theme={theme}>Recent Records</HistoryTitle>
                  <Button
                    variant="outline"
                    onClick={() => navigate('/vitals/history')}
                  >
                    View All History
                  </Button>
                </HistoryHeader>

                <HistoryList>
                  {recentHistory.slice(0, 5).map((record, index) => (
                    <HistoryItem key={index} theme={theme}>
                      <HistoryItemHeader>
                        <HistoryItemTitle theme={theme}>
                          Vital Signs Record
                        </HistoryItemTitle>
                        <HistoryItemDate theme={theme}>
                          {formatDate(record.recordedAt, 'datetime')}
                        </HistoryItemDate>
                      </HistoryItemHeader>

                      <HistoryItemValues>
                        {Object.entries(record.values).map(([type, value]) => {
                          const status = getVitalStatus(type, value);
                          return (
                            <HistoryItemValue key={type} theme={theme}>
                              <HistoryItemValueLabel theme={theme}>
                                {VITAL_SIGN_DISPLAY_NAMES[type]}
                              </HistoryItemValueLabel>
                              <HistoryItemValueNumber status={status} theme={theme}>
                                {value} {VITAL_SIGN_UNITS[type]}
                              </HistoryItemValueNumber>
                            </HistoryItemValue>
                          );
                        })}
                      </HistoryItemValues>
                    </HistoryItem>
                  ))}
                </HistoryList>
              </HistorySection>
            )}
          </>
        ) : (
          <EmptyState theme={theme}>
            <EmptyIcon>❤️</EmptyIcon>
            <EmptyTitle theme={theme}>No Vital Signs Recorded</EmptyTitle>
            <EmptyMessage theme={theme}>
              Start tracking your health by recording your first vital signs measurement.
            </EmptyMessage>
            <Button
              variant="primary"
              onClick={handleAddVitals}
            >
              Record Your First Vitals
            </Button>
          </EmptyState>
        )}
      </VitalsContainer>
    </DashboardLayout>
  );
};

export default VitalsScreen;
