import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import styled, { keyframes } from 'styled-components';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { Modal, Button } from '../common';

const pulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
`;

const VoiceModalContent = styled.div`
  text-align: center;
  padding: 20px;
`;

const VoiceIcon = styled.div`
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: ${props => props.listening ? 
    `linear-gradient(135deg, ${props.theme.colors.primary} 0%, ${props.theme.colors.primaryLight} 100%)` :
    props.theme.colors.lightGray
  };
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px auto;
  font-size: 48px;
  color: ${props => props.listening ? 'white' : props.theme.colors.textSecondary};
  animation: ${props => props.listening ? pulse : 'none'} 2s infinite;
  transition: all 0.3s ease;
`;

const VoiceStatus = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 12px 0;
`;

const VoiceText = styled.p`
  font-size: 16px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0 0 24px 0;
  line-height: 1.5;
`;

const TranscriptBox = styled.div`
  background: ${props => props.theme.colors.lightGray};
  border-radius: 8px;
  padding: 16px;
  margin: 24px 0;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-style: ${props => props.hasText ? 'normal' : 'italic'};
  color: ${props => props.hasText ? props.theme.colors.text : props.theme.colors.textSecondary};
`;

const CommandsList = styled.div`
  text-align: left;
  margin: 24px 0;
  padding: 16px;
  background: ${props => props.theme.colors.lightGray};
  border-radius: 8px;
`;

const CommandsTitle = styled.h4`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0 0 12px 0;
`;

const CommandItem = styled.div`
  font-size: 13px;
  color: ${props => props.theme.colors.textSecondary};
  margin: 4px 0;
  
  &::before {
    content: "• ";
    color: ${props => props.theme.colors.primary};
    font-weight: bold;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 24px;
`;

const VoiceCommandModal = ({ isOpen, onClose }) => {
  const { user } = useAuth();
  const { theme } = useTheme();
  const navigate = useNavigate();
  
  const [listening, setListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [recognition, setRecognition] = useState(null);
  const [supported, setSupported] = useState(false);

  useEffect(() => {
    // Check if speech recognition is supported
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    
    if (SpeechRecognition) {
      setSupported(true);
      const recognitionInstance = new SpeechRecognition();
      
      recognitionInstance.continuous = true;
      recognitionInstance.interimResults = true;
      recognitionInstance.lang = 'en-US'; // You can make this configurable
      
      recognitionInstance.onstart = () => {
        setListening(true);
      };
      
      recognitionInstance.onend = () => {
        setListening(false);
      };
      
      recognitionInstance.onresult = (event) => {
        let finalTranscript = '';
        
        for (let i = event.resultIndex; i < event.results.length; i++) {
          if (event.results[i].isFinal) {
            finalTranscript += event.results[i][0].transcript;
          }
        }
        
        if (finalTranscript) {
          setTranscript(finalTranscript);
          processVoiceCommand(finalTranscript.toLowerCase().trim());
        }
      };
      
      recognitionInstance.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setListening(false);
        
        if (event.error === 'not-allowed') {
          toast.error('Microphone access denied. Please enable microphone permissions.');
        } else {
          toast.error('Speech recognition error. Please try again.');
        }
      };
      
      setRecognition(recognitionInstance);
    } else {
      setSupported(false);
    }
  }, []);

  const voiceCommands = {
    // Navigation commands
    'go to dashboard': () => navigate('/dashboard'),
    'open dashboard': () => navigate('/dashboard'),
    'go to appointments': () => navigate('/appointments'),
    'show appointments': () => navigate('/appointments'),
    'go to medications': () => navigate('/medications'),
    'show medications': () => navigate('/medications'),
    'go to prescriptions': () => navigate('/prescriptions'),
    'show prescriptions': () => navigate('/prescriptions'),
    'go to vitals': () => navigate('/vitals'),
    'show vitals': () => navigate('/vitals'),
    'record vitals': () => navigate('/vitals/record'),
    'go to profile': () => navigate('/profile'),
    'show profile': () => navigate('/profile'),
    'go to settings': () => navigate('/settings'),
    'open settings': () => navigate('/settings'),
    'go to chat': () => navigate('/chat'),
    'open messages': () => navigate('/chat'),
    
    // Action commands
    'new appointment': () => navigate('/appointments/new'),
    'book appointment': () => navigate('/appointments/new'),
    'schedule appointment': () => navigate('/appointments/new'),
    'add medication': () => navigate('/medications/new'),
    'record medication': () => navigate('/medications/new'),
    
    // Role-specific commands
    'show patients': () => {
      if (user?.role === 'doctor' || user?.role === 'supervisor' || user?.role === 'caregiver') {
        navigate('/patients');
      } else {
        toast.error('This command is not available for your role');
      }
    },
    'show map': () => {
      if (user?.role === 'supervisor') {
        navigate('/map');
      } else {
        toast.error('Map access is only available for supervisors');
      }
    },
    
    // Help commands
    'help': () => {
      toast.info('Voice commands are available. Say "show commands" to see all available commands.');
    },
    'what can i say': () => {
      toast.info('You can say commands like "go to dashboard", "new appointment", "record vitals", etc.');
    },
    
    // Close commands
    'close': () => onClose(),
    'cancel': () => onClose(),
    'stop listening': () => stopListening()
  };

  const processVoiceCommand = (command) => {
    console.log('Processing command:', command);
    
    // Find matching command
    const matchedCommand = Object.keys(voiceCommands).find(cmd => 
      command.includes(cmd) || cmd.includes(command)
    );
    
    if (matchedCommand) {
      toast.success(`Executing: ${matchedCommand}`);
      voiceCommands[matchedCommand]();
      onClose();
    } else {
      toast.warning(`Command not recognized: "${command}". Try saying "help" for available commands.`);
    }
  };

  const startListening = () => {
    if (recognition && supported) {
      setTranscript('');
      recognition.start();
    }
  };

  const stopListening = () => {
    if (recognition) {
      recognition.stop();
    }
  };

  const getAvailableCommands = () => {
    const baseCommands = [
      'Go to dashboard',
      'Show appointments',
      'New appointment',
      'Go to medications',
      'Record vitals',
      'Go to profile',
      'Open settings',
      'Help'
    ];

    const roleSpecificCommands = [];
    
    if (user?.role === 'doctor' || user?.role === 'supervisor' || user?.role === 'caregiver') {
      roleSpecificCommands.push('Show patients');
    }
    
    if (user?.role === 'supervisor') {
      roleSpecificCommands.push('Show map');
    }

    return [...baseCommands, ...roleSpecificCommands];
  };

  if (!supported) {
    return (
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        title="Voice Commands"
        maxWidth="500px"
      >
        <VoiceModalContent>
          <VoiceIcon theme={theme}>🚫</VoiceIcon>
          <VoiceStatus theme={theme}>Not Supported</VoiceStatus>
          <VoiceText theme={theme}>
            Voice commands are not supported in your browser. Please use a modern browser with speech recognition support.
          </VoiceText>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </VoiceModalContent>
      </Modal>
    );
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Voice Commands"
      maxWidth="600px"
    >
      <VoiceModalContent>
        <VoiceIcon listening={listening} theme={theme}>
          {listening ? '🎤' : '🔇'}
        </VoiceIcon>
        
        <VoiceStatus theme={theme}>
          {listening ? 'Listening...' : 'Voice Commands Ready'}
        </VoiceStatus>
        
        <VoiceText theme={theme}>
          {listening 
            ? 'Speak your command clearly. I\'m listening...'
            : 'Click "Start Listening" and speak your command.'
          }
        </VoiceText>

        <TranscriptBox hasText={!!transcript} theme={theme}>
          {transcript || 'Your speech will appear here...'}
        </TranscriptBox>

        <CommandsList theme={theme}>
          <CommandsTitle theme={theme}>Available Commands:</CommandsTitle>
          {getAvailableCommands().map((command, index) => (
            <CommandItem key={index} theme={theme}>
              "{command}"
            </CommandItem>
          ))}
        </CommandsList>

        <ActionButtons>
          {!listening ? (
            <Button
              variant="primary"
              onClick={startListening}
              icon="🎤"
            >
              Start Listening
            </Button>
          ) : (
            <Button
              variant="outline"
              onClick={stopListening}
              icon="⏹️"
            >
              Stop Listening
            </Button>
          )}
          
          <Button
            variant="outline"
            onClick={onClose}
          >
            Close
          </Button>
        </ActionButtons>
      </VoiceModalContent>
    </Modal>
  );
};

export default VoiceCommandModal;
