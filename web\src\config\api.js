import axios from 'axios';
import { auth, db } from './firebase';
import { collection, getDocs, doc, getDoc } from 'firebase/firestore';

// List of possible base URLs for the API server
const POSSIBLE_BASE_URLS = [
  'http://localhost:3001',
  'http://127.0.0.1:3001',

  // Common local network IP patterns
  // 192.168.0.x - 192.168.255.x range (most home networks)
  ...[...Array(256).keys()].map(i => `http://192.168.0.${i}:3001`),
  ...[...Array(256).keys()].map(i => `http://192.168.1.${i}:3001`),

  // 172.16.x.x - 172.31.x.x range (some corporate networks)
  ...[...Array(20).keys()].map(i => `http://172.16.0.${i}:3001`),
  ...[...Array(20).keys()].map(i => `http://172.17.0.${i}:3001`),

  // 10.0.x.x range (some larger networks)
  ...[...Array(20).keys()].map(i => `http://10.0.0.${i}:3001`),

  // Add specific known IPs
  'http://***************:3001',
  'http://*************:3001',
  'http://***********:3001',
  'http://*************:3001',
  'http://************:3001',
  'http://************:3001',
  'http://***********:3001',
  'http://************:3001',
  'http://***********:3001',
];

// Storage key for caching the working API URL
const API_URL_STORAGE_KEY = 'neurocare_api_url';

// Function to get cached API URL from storage
const getCachedApiUrl = () => {
  try {
    return localStorage.getItem(API_URL_STORAGE_KEY);
  } catch (error) {
    console.error('Error accessing localStorage:', error);
    return null;
  }
};

// Function to set cached API URL in storage
const setCachedApiUrl = (url) => {
  try {
    localStorage.setItem(API_URL_STORAGE_KEY, url);
  } catch (error) {
    console.error('Error writing to localStorage:', error);
  }
};

// Function to test if a URL is reachable
const isUrlReachable = async (baseUrl) => {
  try {
    const url = `${baseUrl}/health`;
    console.log(`Testing connection to: ${url}`);
    const response = await axios.get(url, {
      timeout: 3000,
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });

    if (response.status === 200) {
      console.log(`✅ Successfully connected to ${baseUrl}`);
      return true;
    }
    return false;
  } catch (error) {
    console.log(`❌ URL ${baseUrl} is not reachable: ${error.message}`);
    return false;
  }
};

// Function to find a working API URL
const findWorkingApiUrl = async () => {
  try {
    console.log('🔍 Starting API server discovery...');

    // First check if we have a cached URL
    const cachedUrl = getCachedApiUrl();
    if (cachedUrl) {
      console.log('📋 Found cached API URL:', cachedUrl);
      // Verify the cached URL still works
      if (await isUrlReachable(cachedUrl)) {
        console.log('✅ Cached API URL is working:', cachedUrl);
        return cachedUrl;
      } else {
        console.log('❌ Cached API URL is no longer working, searching for alternatives...');
      }
    }

    // Priority URLs to try first (faster connection check)
    const priorityUrls = [
      'http://localhost:3001',
      'http://127.0.0.1:3001'
    ];

    // Try priority URLs first
    for (const baseUrl of priorityUrls) {
      if (await isUrlReachable(baseUrl)) {
        console.log('🚀 Found working priority API URL:', baseUrl);
        setCachedApiUrl(baseUrl);
        return baseUrl;
      }
    }

    console.log('⏳ Checking network IP addresses...');

    // Try to find IP addresses in batches to improve performance
    const batchSize = 10;
    const otherUrls = POSSIBLE_BASE_URLS.filter(url => !priorityUrls.includes(url));

    for (let i = 0; i < otherUrls.length; i += batchSize) {
      const batch = otherUrls.slice(i, i + batchSize);

      // Create an array of promises for parallel testing
      const results = await Promise.all(
        batch.map(async (baseUrl) => {
          const isReachable = await isUrlReachable(baseUrl);
          return { baseUrl, isReachable };
        })
      );

      // Find the first working URL in this batch
      const workingUrl = results.find(result => result.isReachable);
      if (workingUrl) {
        console.log('🚀 Found working API URL:', workingUrl.baseUrl);
        setCachedApiUrl(workingUrl.baseUrl);
        return workingUrl.baseUrl;
      }

      // Progress indicator
      console.log(`⏳ Checked ${i + batch.length}/${otherUrls.length} possible addresses...`);
    }

    // If no URL works, return the default but with a more visible warning
    console.error('⚠️ NO WORKING API URL FOUND - SERVER MAY BE DOWN');
    console.error('Make sure the server is running and accessible on your network');

    // Return localhost as fallback
    const fallbackUrl = 'http://localhost:3001';
    console.log(`⚠️ Using fallback URL: ${fallbackUrl}`);
    return fallbackUrl;
  } catch (error) {
    console.error('❌ Error during API URL discovery:', error);
    return 'http://localhost:3001';
  }
};

// Initialize with default API URL (fallback)
const API_URL = 'http://localhost:3001';

// Flag to control direct Firebase access
// Set to true to always use Firebase directly instead of the API server
// Set to false to try API server first, then fall back to Firebase if needed
const USE_DIRECT_FIREBASE = false; // Try to use API server first

// Create an axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  timeout: 10000, // 10 seconds timeout
  headers: {
    'Content-Type': 'application/json',
  }
});

// Try to find a working URL in the background
(async () => {
  try {
    const url = await findWorkingApiUrl();
    // Update the axios instance with the new URL
    api.defaults.baseURL = url;
    console.log('API URL updated to:', url);
  } catch (error) {
    console.error('Error finding API URL:', error);
  }
})();

// Request interceptor
api.interceptors.request.use(
  async (config) => {
    try {
      console.log(`🚀 Request: ${config.method.toUpperCase()} ${config.url}`, config.data || '');

      // Get the current user's ID token for authentication
      const currentUser = auth.currentUser;
      if (currentUser) {
        const token = await currentUser.getIdToken();
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    } catch (error) {
      console.error('Request interceptor error:', error);
      return config;
    }
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    console.log(`✅ Response: ${response.config.method.toUpperCase()} ${response.config.url}`, response.data);
    return response;
  },
  (error) => {
    // Log detailed error information
    console.error(`❌ Response Error: ${error.config?.url || 'unknown url'}`, error.response?.data || error.message);

    // For network errors, provide more detailed logging
    if (error.message === 'Network Error') {
      console.error('Network Error Details:');
      console.error('- Base URL:', error.config?.baseURL);
      console.error('- Full URL:', error.config?.baseURL + error.config?.url);
      console.error('- Method:', error.config?.method?.toUpperCase());
      console.error('- Headers:', JSON.stringify(error.config?.headers, null, 2));
      console.error('- Timeout:', error.config?.timeout);
      console.error('This is likely due to the server being unreachable or CORS issues');
    }

    return Promise.reject(error);
  }
);

// Wrap API calls with better error handling
const withFallback = async (apiCall, fallbackData = null) => {
  try {
    const response = await apiCall();
    console.log('API call succeeded');
    return response.data;
  } catch (error) {
    console.error('API call failed:', error.message);
    console.error('Full error:', error);
    console.error('Response data:', error.response?.data);
    console.error('Request config:', error.config);

    // If fallbackData is provided, return it instead of throwing an error
    if (fallbackData !== null) {
      console.log('Using fallback data:', fallbackData);
      return fallbackData;
    }

    // Rethrow with more details
    const enhancedError = new Error(
      `API Error: ${error.message}. ${error.response?.data?.error || ''}`
    );
    enhancedError.originalError = error;
    enhancedError.responseData = error.response?.data;
    throw enhancedError;
  }
};

// Function to get linked users directly from Firestore
const getLinkedUsersFromFirestore = async (relationshipType) => {
  try {
    console.log('Getting linked users directly from Firestore for relationship type:', relationshipType);

    // Get current user
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.error('No authenticated user found');
      return [];
    }

    // Get user document
    const userDocRef = doc(db, 'users', currentUser.uid);
    const userSnapshot = await getDoc(userDocRef);

    if (!userSnapshot.exists()) {
      console.log('User document not found in Firestore');
      return [];
    }

    const userData = userSnapshot.data();
    let linkedUserIds = [];

    // Get the right array based on relationship type and user role
    if (userData.role === 'doctor' && relationshipType === 'patients') {
      linkedUserIds = userData.linkedPatients || [];
    } else if (userData.role === 'caregiver' && relationshipType === 'patients') {
      linkedUserIds = userData.linkedPatients || [];
    } else if (userData.role === 'patient' && relationshipType === 'doctors') {
      linkedUserIds = userData.linkedDoctors || [];
    } else if (userData.role === 'patient' && relationshipType === 'caregivers') {
      linkedUserIds = userData.linkedCaregivers || [];
    }

    // If no linked users, return empty array
    if (linkedUserIds.length === 0) {
      console.log(`No linked ${relationshipType} found in Firestore`);
      return [];
    }

    // Get user data for all linked users
    const linkedUsers = [];
    for (const userId of linkedUserIds) {
      const linkedUserDocRef = doc(db, 'users', userId);
      const linkedUserSnapshot = await getDoc(linkedUserDocRef);

      if (linkedUserSnapshot.exists()) {
        const linkedUserData = linkedUserSnapshot.data();
        linkedUsers.push({
          uid: linkedUserData.uid || userId,
          displayName: linkedUserData.displayName || '',
          role: linkedUserData.role || ''
        });
      }
    }

    console.log(`Successfully retrieved ${linkedUsers.length} linked ${relationshipType} from Firestore`);
    return linkedUsers;
  } catch (error) {
    console.error(`Error getting linked ${relationshipType} from Firestore:`, error);
    return [];
  }
};

// Function to get users directly from Firestore
const getUsersFromFirestore = async () => {
  try {
    console.log('Getting users directly from Firestore');
    const usersCollection = collection(db, 'users');
    const usersSnapshot = await getDocs(usersCollection);

    if (usersSnapshot.empty) {
      console.log('No users found in Firestore');
      return [];
    }

    const users = [];
    usersSnapshot.forEach(doc => {
      const userData = doc.data();
      let createdAtDate = null;

      // Safely handle createdAt conversion
      if (userData.createdAt) {
        // Check if it's a Firestore timestamp (has toDate method)
        if (typeof userData.createdAt.toDate === 'function') {
          createdAtDate = userData.createdAt.toDate();
        } else if (userData.createdAt instanceof Date) {
          // It's already a Date object
          createdAtDate = userData.createdAt;
        } else if (typeof userData.createdAt === 'string') {
          // It's a string, try to parse it
          createdAtDate = new Date(userData.createdAt);
        }
      }

      users.push({
        uid: userData.uid || doc.id,
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        displayName: userData.displayName || '',
        email: userData.email || '',
        role: userData.role || '',
        speciality: userData.speciality || '',
        location: userData.location || { city: '', country: '' },
        status: userData.status || 'active',
        createdAt: createdAtDate
      });
    });

    console.log('Successfully retrieved users from Firestore:', users.length);
    return users;
  } catch (error) {
    console.error('Error getting users from Firestore:', error);
    // Return empty array instead of throwing to prevent cascading errors
    return [];
  }
};

// Define the API functions
const usersAPI = {
  getUserProfile: () => withFallback(() => api.get('/api/auth/profile')),

  updateUserProfile: (data) => withFallback(() => api.put('/api/auth/profile', data)),

  createInitialProfile: (data) => withFallback(() => api.post('/api/auth/create-profile', data)),

  getUserByCode: (userCode) => withFallback(() => api.get(`/api/users/code/${userCode}`)),

  linkUser: (targetUserCode, relationshipType) =>
    withFallback(() => api.post('/api/users/link', { targetUserCode, relationshipType })),

  getLinkedUsers: async (relationshipType) => {
    try {
      // If direct Firebase access is enabled, use Firestore directly
      if (USE_DIRECT_FIREBASE) {
        console.log('Using direct Firebase access for linked users');
        return await getLinkedUsersFromFirestore(relationshipType);
      }

      // Try the API first
      const apiResult = await withFallback(
        () => api.get(`/api/users/linked/${relationshipType}`),
        null // Don't provide fallback data yet, we'll handle it below
      );

      // If API call succeeded, return the result
      if (apiResult !== null) {
        return apiResult;
      }

      // If API call failed, fall back to direct Firestore access
      console.log('API call failed, falling back to direct Firestore access');
      return await getLinkedUsersFromFirestore(relationshipType);
    } catch (error) {
      console.error('Error in getLinkedUsers:', error);
      // Final fallback to direct Firestore access
      console.log('Error occurred, falling back to direct Firestore access');
      return await getLinkedUsersFromFirestore(relationshipType);
    }
  },

  // Admin APIs
  admin: {
    getAllUsers: async () => {
      try {
        // First try the API
        if (!USE_DIRECT_FIREBASE) {
          const apiResult = await withFallback(() => api.get('/api/users/admin/all'), []);

          // Validate the API result
          if (Array.isArray(apiResult)) {
            console.log('Successfully retrieved users from API');
            return apiResult;
          } else {
            console.warn('API returned non-array data, falling back to Firestore');
            return await getUsersFromFirestore();
          }
        }

        // If direct Firebase access is enabled, use Firestore directly
        console.log('Using direct Firebase access');
        return await getUsersFromFirestore();
      } catch (error) {
        console.error('Error in getAllUsers:', error);
        // Fallback to direct Firestore access
        console.log('Error occurred, falling back to direct Firestore access');
        return await getUsersFromFirestore();
      }
    },

    getUser: (uid) => withFallback(() => api.get(`/api/users/admin/${uid}`)),

    createUser: (userData) => withFallback(() => api.post('/api/users/admin', userData)),

    updateUser: (uid, userData) => withFallback(() => api.put(`/api/users/admin/${uid}`, userData)),

    deleteUser: (uid) => withFallback(() => api.delete(`/api/users/admin/${uid}`)),

    banUser: (uid, banned) => withFallback(() => api.put(`/api/users/admin/${uid}/ban`, { banned }))
  }
};

export const authAPI = {
  createProfile: (userData) => api.post('/api/auth/create-profile', userData),
  updateProfile: (userData) => api.put('/api/auth/profile', userData),
  getProfile: () => api.get('/api/auth/profile'),
  addCaregiver: (email) => api.post('/api/auth/add-caregiver', { caregiverEmail: email }),

  // Methods with fallback for offline scenarios
  updateProfileWithFallback: async (userData) => {
    return withFallback(() => api.put('/api/auth/profile', userData), { success: true });
  },
  getProfileWithFallback: async () => {
    return withFallback(() => api.get('/api/auth/profile'), { profile: null });
  }
};

export const notificationsAPI = {
  createReminder: (reminder) => api.post('/api/notifications/reminder', reminder),
  getReminders: () => api.get('/api/notifications/reminders'),
  updateReminder: (id, data) => api.put(`/api/notifications/reminder/${id}`, data),
  deleteReminder: (id) => api.delete(`/api/notifications/reminder/${id}`),
};

export { API_URL, api, withFallback, USE_DIRECT_FIREBASE, usersAPI };
