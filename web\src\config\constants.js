// Application constants (converted from React Native client)

// User roles
export const USER_ROLES = {
  PATIENT: 'patient',
  DOCTOR: 'doctor',
  CAREGIVER: 'caregiver',
  SUPERVISOR: 'supervisor',
  ADMIN: 'admin'
};

// Appointment statuses
export const APPOINTMENT_STATUS = {
  SCHEDULED: 'scheduled',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  RESCHEDULED: 'rescheduled'
};

// Appointment types
export const APPOINTMENT_TYPES = {
  CONSULTATION: 'consultation',
  THERAPY: 'therapy',
  CHECKUP: 'checkup',
  EMERGENCY: 'emergency',
  VIDEO_CALL: 'video_call',
  FOLLOW_UP: 'follow_up'
};

// Message types
export const MESSAGE_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  VOICE: 'voice',
  VIDEO: 'video',
  DOCUMENT: 'document',
  LOCATION: 'location',
  SYSTEM: 'system'
};

// Notification types
export const NOTIFICATION_TYPES = {
  APPOINTMENT_REMINDER: 'appointment_reminder',
  MEDICATION_REMINDER: 'medication_reminder',
  EMERGENCY_ALERT: 'emergency_alert',
  MESSAGE_RECEIVED: 'message_received',
  VITALS_ALERT: 'vitals_alert',
  ACTIVITY_REMINDER: 'activity_reminder',
  SYSTEM_UPDATE: 'system_update'
};

// Activity types for caregivers
export const ACTIVITY_TYPES = {
  MEDICATION: 'medication',
  EXERCISE: 'exercise',
  COGNITIVE: 'cognitive',
  MEAL: 'meal',
  SOCIAL: 'social',
  HYGIENE: 'hygiene',
  THERAPY: 'therapy',
  OTHER: 'other'
};

// Vital signs types
export const VITAL_TYPES = {
  HEART_RATE: 'heart_rate',
  BLOOD_PRESSURE: 'blood_pressure',
  TEMPERATURE: 'temperature',
  OXYGEN_SATURATION: 'oxygen_saturation',
  WEIGHT: 'weight',
  BLOOD_SUGAR: 'blood_sugar',
  RESPIRATORY_RATE: 'respiratory_rate'
};

// Prescription statuses
export const PRESCRIPTION_STATUS = {
  DRAFT: 'draft',
  SENT: 'sent',
  RECEIVED: 'received',
  FILLED: 'filled',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
};

// Emergency contact types
export const EMERGENCY_CONTACT_TYPES = {
  EMERGENCY_SERVICES: '911',
  CAREGIVER: 'caregiver',
  FAMILY: 'family',
  DOCTOR: 'doctor',
  SUPERVISOR: 'supervisor'
};

// Location types
export const LOCATION_TYPES = {
  HOME: 'home',
  CLINIC: 'clinic',
  HOSPITAL: 'hospital',
  PHARMACY: 'pharmacy',
  THERAPY_CENTER: 'therapy_center',
  DAY_CARE: 'day_care',
  OTHER: 'other'
};

// Video call statuses
export const VIDEO_CALL_STATUS = {
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  ENDED: 'ended',
  FAILED: 'failed',
  BUSY: 'busy',
  DECLINED: 'declined'
};

// QR Code types
export const QR_CODE_TYPES = {
  USER_PROFILE: 'user_profile',
  PATIENT_INFO: 'patient_info',
  CAREGIVER_INFO: 'caregiver_info',
  APPOINTMENT: 'appointment',
  MEDICATION: 'medication',
  EMERGENCY_CONTACT: 'emergency_contact'
};

// File upload types
export const FILE_TYPES = {
  IMAGE: 'image',
  DOCUMENT: 'document',
  AUDIO: 'audio',
  VIDEO: 'video'
};

// Supported file extensions
export const SUPPORTED_FILE_EXTENSIONS = {
  IMAGE: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
  DOCUMENT: ['.pdf', '.doc', '.docx', '.txt'],
  AUDIO: ['.mp3', '.wav', '.m4a'],
  VIDEO: ['.mp4', '.mov', '.avi']
};

// Maximum file sizes (in bytes)
export const MAX_FILE_SIZES = {
  IMAGE: 5 * 1024 * 1024, // 5MB
  DOCUMENT: 10 * 1024 * 1024, // 10MB
  AUDIO: 25 * 1024 * 1024, // 25MB
  VIDEO: 100 * 1024 * 1024 // 100MB
};

// API endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password'
  },
  USERS: {
    PROFILE: '/users/profile',
    UPDATE_PROFILE: '/users/profile',
    UPLOAD_AVATAR: '/users/avatar',
    CHANGE_PASSWORD: '/users/change-password'
  },
  APPOINTMENTS: {
    LIST: '/appointments',
    CREATE: '/appointments',
    UPDATE: '/appointments',
    DELETE: '/appointments',
    RESCHEDULE: '/appointments/reschedule'
  },
  MESSAGES: {
    CONVERSATIONS: '/messages/conversations',
    SEND: '/messages/send',
    HISTORY: '/messages/history'
  },
  VITALS: {
    LIST: '/vitals',
    CREATE: '/vitals',
    STATS: '/vitals/stats'
  },
  PRESCRIPTIONS: {
    LIST: '/prescriptions',
    CREATE: '/prescriptions',
    UPDATE: '/prescriptions'
  }
};

// Local storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'neurocare_auth_token',
  REFRESH_TOKEN: 'neurocare_refresh_token',
  USER_PROFILE: 'neurocare_user_profile',
  THEME_PREFERENCE: 'neurocare_theme',
  LANGUAGE_PREFERENCE: 'neurocare_language',
  NOTIFICATION_SETTINGS: 'neurocare_notifications',
  OFFLINE_DATA: 'neurocare_offline_data'
};

// Default pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100
};

// Time constants
export const TIME_CONSTANTS = {
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
  WEEK: 7 * 24 * 60 * 60 * 1000,
  MONTH: 30 * 24 * 60 * 60 * 1000
};

// Validation constants
export const VALIDATION = {
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_MAX_LENGTH: 128,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  EMAIL_MAX_LENGTH: 254,
  PHONE_MIN_LENGTH: 10,
  PHONE_MAX_LENGTH: 15,
  MESSAGE_MAX_LENGTH: 1000,
  NOTES_MAX_LENGTH: 2000
};

// Geographic constants
export const GEO_CONSTANTS = {
  DEFAULT_LATITUDE: 48.8566, // Paris
  DEFAULT_LONGITUDE: 2.3522,
  DEFAULT_ZOOM: 13,
  GEOFENCE_RADIUS: 100, // meters
  LOCATION_UPDATE_INTERVAL: 30000 // 30 seconds
};

// Voice command constants
export const VOICE_COMMANDS = {
  NAVIGATION: [
    'go to dashboard',
    'open appointments',
    'show messages',
    'view profile',
    'open scanner'
  ],
  ACTIONS: [
    'call emergency',
    'record vitals',
    'take medication',
    'start navigation',
    'send message'
  ]
};

// Medical condition types
export const MEDICAL_CONDITIONS = {
  ALZHEIMERS: 'alzheimers',
  PARKINSONS: 'parkinsons',
  DEMENTIA: 'dementia',
  STROKE: 'stroke',
  DIABETES: 'diabetes',
  HYPERTENSION: 'hypertension',
  HEART_DISEASE: 'heart_disease',
  OTHER: 'other'
};

// Severity levels
export const SEVERITY_LEVELS = {
  MILD: 'mild',
  MODERATE: 'moderate',
  SEVERE: 'severe',
  CRITICAL: 'critical'
};

// Languages supported
export const SUPPORTED_LANGUAGES = {
  EN: 'en',
  FR: 'fr',
  ES: 'es',
  DE: 'de',
  IT: 'it'
};

// Default app settings
export const DEFAULT_SETTINGS = {
  LANGUAGE: SUPPORTED_LANGUAGES.EN,
  NOTIFICATIONS_ENABLED: true,
  LOCATION_ENABLED: true,
  VOICE_COMMANDS_ENABLED: true,
  AUTO_BACKUP: true,
  THEME: 'light'
};

// Error codes
export const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  SERVER_ERROR: 'SERVER_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR'
};

// Success messages
export const SUCCESS_MESSAGES = {
  PROFILE_UPDATED: 'Profile updated successfully',
  APPOINTMENT_CREATED: 'Appointment scheduled successfully',
  MESSAGE_SENT: 'Message sent successfully',
  VITALS_RECORDED: 'Vital signs recorded successfully',
  PRESCRIPTION_CREATED: 'Prescription created successfully'
};

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection error. Please check your internet connection.',
  AUTH_ERROR: 'Authentication failed. Please log in again.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  PERMISSION_ERROR: 'You do not have permission to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Server error. Please try again later.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.'
};

export default {
  USER_ROLES,
  APPOINTMENT_STATUS,
  APPOINTMENT_TYPES,
  MESSAGE_TYPES,
  NOTIFICATION_TYPES,
  ACTIVITY_TYPES,
  VITAL_TYPES,
  PRESCRIPTION_STATUS,
  EMERGENCY_CONTACT_TYPES,
  LOCATION_TYPES,
  VIDEO_CALL_STATUS,
  QR_CODE_TYPES,
  FILE_TYPES,
  SUPPORTED_FILE_EXTENSIONS,
  MAX_FILE_SIZES,
  API_ENDPOINTS,
  STORAGE_KEYS,
  PAGINATION,
  TIME_CONSTANTS,
  VALIDATION,
  GEO_CONSTANTS,
  VOICE_COMMANDS,
  MEDICAL_CONDITIONS,
  SEVERITY_LEVELS,
  SUPPORTED_LANGUAGES,
  DEFAULT_SETTINGS,
  ERROR_CODES,
  SUCCESS_MESSAGES,
  ERROR_MESSAGES
};
