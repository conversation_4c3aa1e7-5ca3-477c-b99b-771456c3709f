/**
 * Initialize Firebase Cloud Messaging for Web
 * 
 * This function handles all the Firebase Cloud Messaging
 * initialization for web browsers with proper error handling.
 */
export const initializeMessaging = async () => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') {
    console.log('Skipping Firebase messaging on server-side');
    return { token: null, enabled: false };
  }

  try {
    // Check if the browser supports service workers and notifications
    if (!('serviceWorker' in navigator) || !('Notification' in window)) {
      console.log('Browser does not support notifications or service workers');
      return { token: null, enabled: false };
    }

    // Check if Firebase messaging is available
    let messaging;
    try {
      // Import Firebase messaging for web
      const { getMessaging, getToken, onMessage } = await import('firebase/messaging');
      const { app } = await import('./firebase');
      messaging = getMessaging(app);
    } catch (error) {
      console.log('Firebase messaging module not available:', error.message);
      return { token: null, enabled: false, error };
    }
    
    if (!messaging) {
      console.log('Firebase messaging module loaded but is undefined');
      return { token: null, enabled: false };
    }
    
    // Check for permissions
    let permission = Notification.permission;
    
    if (permission === 'default') {
      permission = await Notification.requestPermission();
    }
      
    if (permission !== 'granted') {
      console.log('Notification permissions not granted');
      return { token: null, enabled: false };
    }
    
    // Get FCM token
    try {
      const { getToken } = await import('firebase/messaging');
      
      // You need to add your VAPID key here
      // Get it from Firebase Console > Project Settings > Cloud Messaging > Web configuration
      const vapidKey = process.env.REACT_APP_VAPID_KEY || 'YOUR_VAPID_KEY_HERE';
      
      const token = await getToken(messaging, {
        vapidKey: vapidKey
      });
      
      console.log('Firebase token:', token ? token.substring(0, 10) + '...' : 'null');
      
      // Configure foreground notification handling
      const { onMessage } = await import('firebase/messaging');
      onMessage(messaging, (payload) => {
        console.log('Foreground message received:', payload);
        
        // Display notification manually for foreground messages
        if (payload.notification) {
          new Notification(payload.notification.title, {
            body: payload.notification.body,
            icon: payload.notification.icon || '/favicon.ico'
          });
        }
      });
      
      // Return the token and enabled status
      return { token, enabled: true };
    } catch (tokenError) {
      console.error('Error getting FCM token:', tokenError);
      return { token: null, enabled: false, error: tokenError };
    }
  } catch (error) {
    console.log('Error initializing Firebase messaging:', error.message);
    return { token: null, enabled: false, error };
  }
};

/**
 * Register the device's FCM token with your backend
 */
export const registerDeviceForNotifications = async (userId, token, apiToken) => {
  if (!token) {
    console.log('No token provided for registration');
    return false;
  }
  
  try {
    console.log('Registering device for user:', userId);
    
    // Make an API call to your backend to register the FCM token
    const response = await fetch('/api/users/register-device', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiToken}`
      },
      body: JSON.stringify({
        userId,
        token,
        device: 'web'
      })
    });
    
    const data = await response.json();
    console.log('Device registration response:', data);
    return data.success;
  } catch (error) {
    console.error('Error registering device for notifications:', error);
    return false;
  }
};

/**
 * Show a browser notification
 */
export const showNotification = (title, options = {}) => {
  if (!('Notification' in window)) {
    console.log('Browser does not support notifications');
    return;
  }

  if (Notification.permission === 'granted') {
    new Notification(title, {
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      ...options
    });
  } else {
    console.log('Notification permission not granted');
  }
};

/**
 * Request notification permission
 */
export const requestNotificationPermission = async () => {
  if (!('Notification' in window)) {
    console.log('Browser does not support notifications');
    return false;
  }

  const permission = await Notification.requestPermission();
  return permission === 'granted';
};

export default {
  initializeMessaging,
  registerDeviceForNotifications,
  showNotification,
  requestNotificationPermission
};
