// Color palette for different user roles
export const COLORS = {
  // Primary colors
  primary: '#4CAF50',
  primaryDark: '#388E3C',
  primaryLight: '#81C784',

  // Secondary colors
  secondary: '#2196F3',
  secondaryDark: '#1976D2',
  secondaryLight: '#64B5F6',

  // Role-specific colors (exact from mobile client)
  patient: 'rgba(255, 149, 43, 1)',      // Orange for patients
  doctor: 'rgba(170, 86, 255, 1)',       // Purple for doctors
  admin: 'rgba(16, 107, 0, 1)',          // Green for admin
  caregiver: 'rgba(0, 169, 255, 1)',     // Blue for caregivers
  supervisor: 'rgb(255, 0, 242)',        // Bright magenta/pink for supervisors

  // Status colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',

  // Neutral colors
  white: '#FFFFFF',
  black: '#000000',
  gray: '#9E9E9E',
  lightGray: '#F5F5F5',
  darkGray: '#424242',

  // Background colors
  background: '#F5F5F5',
  surface: '#FFFFFF',

  // Text colors
  text: '#212121',
  textSecondary: '#757575',
  textLight: '#FFFFFF',

  // Border colors
  border: '#E0E0E0',
  borderLight: '#F0F0F0',

  // Gradient colors
  gradientStart: '#4CAF50',
  gradientEnd: '#81C784',
};

// Typography
export const TYPOGRAPHY = {
  fontFamily: {
    regular: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    medium: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    bold: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  },
  fontSize: {
    xs: '12px',
    sm: '14px',
    md: '16px',
    lg: '18px',
    xl: '20px',
    xxl: '24px',
    xxxl: '32px',
  },
  fontWeight: {
    light: 300,
    regular: 400,
    medium: 500,
    semiBold: 600,
    bold: 700,
  },
  lineHeight: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.8,
  },
};

// Spacing
export const SPACING = {
  xs: '4px',
  sm: '8px',
  md: '16px',
  lg: '24px',
  xl: '32px',
  xxl: '48px',
  xxxl: '64px',
};

// Border radius
export const BORDER_RADIUS = {
  sm: '4px',
  md: '8px',
  lg: '12px',
  xl: '16px',
  round: '50%',
};

// Shadows
export const SHADOWS = {
  sm: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
  md: '0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)',
  lg: '0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)',
  xl: '0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)',
};

// Breakpoints for responsive design
export const BREAKPOINTS = {
  mobile: '480px',
  tablet: '768px',
  desktop: '1024px',
  large: '1200px',
};

// Z-index values
export const Z_INDEX = {
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modalBackdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
};

// Animation durations
export const ANIMATION = {
  fast: '150ms',
  normal: '300ms',
  slow: '500ms',
};

// Role-specific theme configurations (exact from mobile client)
export const ROLE_THEMES = {
  patient: {
    primary: COLORS.patient,
    primaryDark: 'rgba(255, 149, 43, 0.9)',
    primaryLight: 'rgba(255, 149, 43, 0.8)',
    primaryLighter: 'rgba(255, 149, 43, 0.1)',
    secondary: 'rgba(255, 149, 43, 0.6)',
    background: COLORS.background,
    surface: COLORS.surface,
    text: COLORS.text,
    textSecondary: COLORS.textSecondary,
    textLight: COLORS.textLight,
    lightGray: COLORS.lightGray,
    gray: COLORS.gray,
    darkGray: COLORS.darkGray,
    border: COLORS.border,
    borderLight: COLORS.borderLight,
    success: COLORS.success,
    warning: COLORS.warning,
    error: COLORS.error,
    info: COLORS.info,
    white: COLORS.white,
    black: COLORS.black,
  },
  doctor: {
    primary: COLORS.doctor,
    primaryDark: 'rgba(170, 86, 255, 0.9)',
    primaryLight: 'rgba(170, 86, 255, 0.8)',
    primaryLighter: 'rgba(170, 86, 255, 0.1)',
    secondary: 'rgba(170, 86, 255, 0.6)',
    background: COLORS.background,
    surface: COLORS.surface,
    text: COLORS.text,
    textSecondary: COLORS.textSecondary,
    textLight: COLORS.textLight,
    lightGray: COLORS.lightGray,
    gray: COLORS.gray,
    darkGray: COLORS.darkGray,
    border: COLORS.border,
    borderLight: COLORS.borderLight,
    success: COLORS.success,
    warning: COLORS.warning,
    error: COLORS.error,
    info: COLORS.info,
    white: COLORS.white,
    black: COLORS.black,
  },
  admin: {
    primary: COLORS.admin,
    primaryDark: 'rgba(16, 107, 0, 0.9)',
    primaryLight: 'rgba(16, 107, 0, 0.8)',
    primaryLighter: 'rgba(16, 107, 0, 0.1)',
    secondary: 'rgba(16, 107, 0, 0.6)',
    background: COLORS.background,
    surface: COLORS.surface,
    text: COLORS.text,
    textSecondary: COLORS.textSecondary,
    textLight: COLORS.textLight,
    lightGray: COLORS.lightGray,
    gray: COLORS.gray,
    darkGray: COLORS.darkGray,
    border: COLORS.border,
    borderLight: COLORS.borderLight,
    success: COLORS.success,
    warning: COLORS.warning,
    error: COLORS.error,
    info: COLORS.info,
    white: COLORS.white,
    black: COLORS.black,
  },
  caregiver: {
    primary: COLORS.caregiver,
    primaryDark: 'rgba(0, 169, 255, 0.9)',
    primaryLight: 'rgba(0, 169, 255, 0.8)',
    primaryLighter: 'rgba(0, 169, 255, 0.1)',
    secondary: 'rgba(0, 169, 255, 0.6)',
    background: COLORS.background,
    surface: COLORS.surface,
    text: COLORS.text,
    textSecondary: COLORS.textSecondary,
    textLight: COLORS.textLight,
    lightGray: COLORS.lightGray,
    gray: COLORS.gray,
    darkGray: COLORS.darkGray,
    border: COLORS.border,
    borderLight: COLORS.borderLight,
    success: COLORS.success,
    warning: COLORS.warning,
    error: COLORS.error,
    info: COLORS.info,
    white: COLORS.white,
    black: COLORS.black,
  },
  supervisor: {
    primary: COLORS.supervisor,
    primaryDark: 'rgba(255, 0, 242, 0.9)',
    primaryLight: 'rgba(255, 0, 242, 0.8)',
    primaryLighter: 'rgba(255, 0, 242, 0.1)',
    secondary: 'rgba(255, 0, 242, 0.6)',
    background: COLORS.background,
    surface: COLORS.surface,
    text: COLORS.text,
    textSecondary: COLORS.textSecondary,
    textLight: COLORS.textLight,
    lightGray: COLORS.lightGray,
    gray: COLORS.gray,
    darkGray: COLORS.darkGray,
    border: COLORS.border,
    borderLight: COLORS.borderLight,
    success: COLORS.success,
    warning: COLORS.warning,
    error: COLORS.error,
    info: COLORS.info,
    white: COLORS.white,
    black: COLORS.black,
  },
};

// Default theme
export const DEFAULT_THEME = {
  colors: COLORS,
  typography: TYPOGRAPHY,
  spacing: SPACING,
  borderRadius: BORDER_RADIUS,
  shadows: SHADOWS,
  breakpoints: BREAKPOINTS,
  zIndex: Z_INDEX,
  animation: ANIMATION,
};
