// Define role-specific colors (EXACT from mobile client)
export const ROLE_COLORS = {
  patient: {
    primary: 'rgba(255, 149, 43, 1)', // Orange for patients
    primaryLight: 'rgba(255, 149, 43, 0.8)',
    primaryLighter: 'rgba(255, 149, 43, 0.1)'
  },
  doctor: {
    primary: 'rgba(170, 86, 255, 1)', // Purple for doctors
    primaryLight: 'rgba(170, 86, 255, 0.8)',
    primaryLighter: 'rgba(170, 86, 255, 0.1)'
  },
  supervisor: {
    primary: 'rgb(255, 0, 242)', // Bright magenta/pink for supervisors
    primaryLight: 'rgba(255, 0, 242, 0.8)',
    primaryLighter: 'rgba(255, 0, 242, 0.1)'
  },
  caregiver: {
    primary: 'rgba(0, 169, 255, 1)', // Blue for caregivers
    primaryLight: 'rgba(0, 169, 255, 0.8)',
    primaryLighter: 'rgba(0, 169, 255, 0.1)'
  },
  admin: {
    primary: 'rgba(16, 107, 0, 1)', // Green for admin
    primaryLight: 'rgba(16, 107, 0, 0.8)',
    primaryLighter: 'rgba(16, 107, 0, 0.1)'
  },
  default: {
    primary: 'rgba(16, 107, 0, 1)', // Default green
    primaryLight: 'rgba(16, 107, 0, 0.8)',
    primaryLighter: 'rgba(16, 107, 0, 0.1)'
  }
};

// Export patient colors for direct access (EXACT from mobile client)
export const PATIENT_COLORS = ROLE_COLORS.patient;

export const COLORS = {
  success: '#4CAF50',
  error: '#F44336',
  warning: '#FFA726',
  info: '#29B6F6',
  textDark: '#212121',
  textMedium: '#757575',
  textLight: '#BDBDBD',
  background: '#f5f7fa',
  surface: '#FFFFFF',
  border: '#E0E0E0',

  // Additional colors for web compatibility
  white: '#FFFFFF',
  black: '#000000',
  gray: '#9E9E9E',
  lightGray: '#F5F5F5',
  darkGray: '#424242',
  text: '#212121',
  textSecondary: '#757575',
  borderLight: '#F0F0F0',
};

// Typography
export const TYPOGRAPHY = {
  fontFamily: {
    regular: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    medium: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    bold: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  },
  fontSize: {
    xs: '12px',
    sm: '14px',
    md: '16px',
    lg: '18px',
    xl: '20px',
    xxl: '24px',
    xxxl: '32px',
  },
  fontWeight: {
    light: 300,
    regular: 400,
    medium: 500,
    semiBold: 600,
    bold: 700,
  },
  lineHeight: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.8,
  },
};

// Spacing
export const SPACING = {
  xs: '4px',
  sm: '8px',
  md: '16px',
  lg: '24px',
  xl: '32px',
  xxl: '48px',
  xxxl: '64px',
};

// Border radius
export const BORDER_RADIUS = {
  sm: '4px',
  md: '8px',
  lg: '12px',
  xl: '16px',
  round: '50%',
};

// Shadows
export const SHADOWS = {
  sm: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
  md: '0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)',
  lg: '0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)',
  xl: '0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)',
};

// Breakpoints for responsive design
export const BREAKPOINTS = {
  mobile: '480px',
  tablet: '768px',
  desktop: '1024px',
  large: '1200px',
};

// Z-index values
export const Z_INDEX = {
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modalBackdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
};

// Animation durations
export const ANIMATION = {
  fast: '150ms',
  normal: '300ms',
  slow: '500ms',
};

// Function to get theme for role (EXACT from mobile client)
export const getThemeForRole = (role = 'default') => {
  // Get role-specific colors or default if role doesn't exist
  const roleColors = ROLE_COLORS[role] || ROLE_COLORS.default;
  const primaryColor = roleColors.primary;

  // Base theme applied to the entire application (adapted for web)
  const baseTheme = {
    roundness: 8,
    colors: {
      primary: roleColors.primary,
      primaryLight: roleColors.primaryLight,
      primaryLighter: roleColors.primaryLighter,
      accent: COLORS.success,
      background: COLORS.background,
      surface: COLORS.surface,
      error: COLORS.error,
      text: COLORS.textDark,
      textSecondary: COLORS.textMedium,
      textLight: COLORS.textLight,
      placeholder: COLORS.textLight,
      backdrop: 'rgba(0, 0, 0, 0.5)',
      notification: COLORS.info,
      success: COLORS.success,
      warning: COLORS.warning,
      info: COLORS.info,
      border: COLORS.border,
      borderLight: COLORS.borderLight || '#F0F0F0',
      white: COLORS.white,
      black: COLORS.black,
      gray: COLORS.gray,
      lightGray: COLORS.lightGray,
      darkGray: COLORS.darkGray,
    },
  };

  return baseTheme;
};

// Role-specific theme configurations (for compatibility)
export const ROLE_THEMES = {
  patient: getThemeForRole('patient'),
  doctor: getThemeForRole('doctor'),
  admin: getThemeForRole('admin'),
  caregiver: getThemeForRole('caregiver'),
  supervisor: getThemeForRole('supervisor'),
};

// Default theme (using the mobile pattern)
export const DEFAULT_THEME = getThemeForRole('default');

// Export default function like mobile client
export default getThemeForRole;
