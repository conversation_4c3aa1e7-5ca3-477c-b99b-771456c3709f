// Color palette for different user roles
export const COLORS = {
  // Primary colors
  primary: '#4CAF50',
  primaryDark: '#388E3C',
  primaryLight: '#81C784',
  
  // Secondary colors
  secondary: '#2196F3',
  secondaryDark: '#1976D2',
  secondaryLight: '#64B5F6',
  
  // Role-specific colors
  patient: '#4CAF50',      // Green
  doctor: '#2196F3',       // Blue
  admin: '#9C27B0',        // Purple
  caregiver: '#FF9800',    // Orange
  supervisor: '#E91E63',   // Pink/Magenta
  
  // Status colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  
  // Neutral colors
  white: '#FFFFFF',
  black: '#000000',
  gray: '#9E9E9E',
  lightGray: '#F5F5F5',
  darkGray: '#424242',
  
  // Background colors
  background: '#F5F5F5',
  surface: '#FFFFFF',
  
  // Text colors
  text: '#212121',
  textSecondary: '#757575',
  textLight: '#FFFFFF',
  
  // Border colors
  border: '#E0E0E0',
  borderLight: '#F0F0F0',
  
  // Gradient colors
  gradientStart: '#4CAF50',
  gradientEnd: '#81C784',
};

// Typography
export const TYPOGRAPHY = {
  fontFamily: {
    regular: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    medium: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    bold: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  },
  fontSize: {
    xs: '12px',
    sm: '14px',
    md: '16px',
    lg: '18px',
    xl: '20px',
    xxl: '24px',
    xxxl: '32px',
  },
  fontWeight: {
    light: 300,
    regular: 400,
    medium: 500,
    semiBold: 600,
    bold: 700,
  },
  lineHeight: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.8,
  },
};

// Spacing
export const SPACING = {
  xs: '4px',
  sm: '8px',
  md: '16px',
  lg: '24px',
  xl: '32px',
  xxl: '48px',
  xxxl: '64px',
};

// Border radius
export const BORDER_RADIUS = {
  sm: '4px',
  md: '8px',
  lg: '12px',
  xl: '16px',
  round: '50%',
};

// Shadows
export const SHADOWS = {
  sm: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
  md: '0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)',
  lg: '0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)',
  xl: '0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)',
};

// Breakpoints for responsive design
export const BREAKPOINTS = {
  mobile: '480px',
  tablet: '768px',
  desktop: '1024px',
  large: '1200px',
};

// Z-index values
export const Z_INDEX = {
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modalBackdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
};

// Animation durations
export const ANIMATION = {
  fast: '150ms',
  normal: '300ms',
  slow: '500ms',
};

// Role-specific theme configurations
export const ROLE_THEMES = {
  patient: {
    primary: COLORS.patient,
    secondary: COLORS.primaryLight,
    background: COLORS.background,
    surface: COLORS.white,
    text: COLORS.text,
  },
  doctor: {
    primary: COLORS.doctor,
    secondary: COLORS.secondaryLight,
    background: COLORS.background,
    surface: COLORS.white,
    text: COLORS.text,
  },
  admin: {
    primary: COLORS.admin,
    secondary: '#CE93D8',
    background: COLORS.background,
    surface: COLORS.white,
    text: COLORS.text,
  },
  caregiver: {
    primary: COLORS.caregiver,
    secondary: '#FFB74D',
    background: COLORS.background,
    surface: COLORS.white,
    text: COLORS.text,
  },
  supervisor: {
    primary: COLORS.supervisor,
    secondary: '#F48FB1',
    background: COLORS.background,
    surface: COLORS.white,
    text: COLORS.text,
  },
};

// Default theme
export const DEFAULT_THEME = {
  colors: COLORS,
  typography: TYPOGRAPHY,
  spacing: SPACING,
  borderRadius: BORDER_RADIUS,
  shadows: SHADOWS,
  breakpoints: BREAKPOINTS,
  zIndex: Z_INDEX,
  animation: ANIMATION,
};
