import React, { createContext, useContext, useState, useEffect } from 'react';
import { collection, query, where, orderBy, onSnapshot, addDoc, updateDoc, deleteDoc, doc } from 'firebase/firestore';
import { db } from '../config/firebase';
import { useAuth } from './AuthContext';

const AppointmentContext = createContext();

export const useAppointments = () => {
  const context = useContext(AppointmentContext);
  if (!context) {
    throw new Error('useAppointments must be used within an AppointmentProvider');
  }
  return context;
};

export const AppointmentProvider = ({ children }) => {
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useAuth();

  // Fetch appointments based on user role
  useEffect(() => {
    if (!user) {
      setAppointments([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    let appointmentsQuery;

    try {
      // Build query based on user role
      switch (user.role) {
        case 'patient':
          appointmentsQuery = query(
            collection(db, 'appointments'),
            where('patientId', '==', user.uid),
            orderBy('appointmentDate', 'desc')
          );
          break;
        case 'doctor':
          appointmentsQuery = query(
            collection(db, 'appointments'),
            where('doctorId', '==', user.uid),
            orderBy('appointmentDate', 'desc')
          );
          break;
        case 'caregiver':
          appointmentsQuery = query(
            collection(db, 'appointments'),
            where('caregiverId', '==', user.uid),
            orderBy('appointmentDate', 'desc')
          );
          break;
        case 'supervisor':
        case 'admin':
          // Supervisors and admins can see all appointments
          appointmentsQuery = query(
            collection(db, 'appointments'),
            orderBy('appointmentDate', 'desc')
          );
          break;
        default:
          appointmentsQuery = query(
            collection(db, 'appointments'),
            where('patientId', '==', user.uid),
            orderBy('appointmentDate', 'desc')
          );
      }

      const unsubscribe = onSnapshot(
        appointmentsQuery,
        (snapshot) => {
          const appointmentsList = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
          setAppointments(appointmentsList);
          setLoading(false);
        },
        (error) => {
          console.error('Error fetching appointments:', error);
          setError(error.message);
          setLoading(false);
        }
      );

      return unsubscribe;
    } catch (error) {
      console.error('Error setting up appointments listener:', error);
      setError(error.message);
      setLoading(false);
    }
  }, [user]);

  // Add new appointment
  const addAppointment = async (appointmentData) => {
    try {
      const newAppointment = {
        ...appointmentData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 'scheduled'
      };

      const docRef = await addDoc(collection(db, 'appointments'), newAppointment);
      return { id: docRef.id, ...newAppointment };
    } catch (error) {
      console.error('Error adding appointment:', error);
      throw error;
    }
  };

  // Update appointment
  const updateAppointment = async (appointmentId, updates) => {
    try {
      const appointmentRef = doc(db, 'appointments', appointmentId);
      const updateData = {
        ...updates,
        updatedAt: new Date().toISOString()
      };

      await updateDoc(appointmentRef, updateData);
      return updateData;
    } catch (error) {
      console.error('Error updating appointment:', error);
      throw error;
    }
  };

  // Cancel appointment
  const cancelAppointment = async (appointmentId, reason = '') => {
    try {
      const appointmentRef = doc(db, 'appointments', appointmentId);
      await updateDoc(appointmentRef, {
        status: 'cancelled',
        cancellationReason: reason,
        cancelledAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error cancelling appointment:', error);
      throw error;
    }
  };

  // Delete appointment
  const deleteAppointment = async (appointmentId) => {
    try {
      await deleteDoc(doc(db, 'appointments', appointmentId));
    } catch (error) {
      console.error('Error deleting appointment:', error);
      throw error;
    }
  };

  // Get upcoming appointments
  const getUpcomingAppointments = () => {
    const now = new Date();
    return appointments.filter(appointment => {
      const appointmentDate = new Date(appointment.appointmentDate);
      return appointmentDate >= now && appointment.status === 'scheduled';
    });
  };

  // Get past appointments
  const getPastAppointments = () => {
    const now = new Date();
    return appointments.filter(appointment => {
      const appointmentDate = new Date(appointment.appointmentDate);
      return appointmentDate < now || appointment.status === 'completed';
    });
  };

  // Get appointments by status
  const getAppointmentsByStatus = (status) => {
    return appointments.filter(appointment => appointment.status === status);
  };

  // Get today's appointments
  const getTodaysAppointments = () => {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    return appointments.filter(appointment => {
      const appointmentDate = new Date(appointment.appointmentDate);
      return appointmentDate >= startOfDay && appointmentDate < endOfDay;
    });
  };

  const value = {
    appointments,
    loading,
    error,
    addAppointment,
    updateAppointment,
    cancelAppointment,
    deleteAppointment,
    getUpcomingAppointments,
    getPastAppointments,
    getAppointmentsByStatus,
    getTodaysAppointments,
  };

  return (
    <AppointmentContext.Provider value={value}>
      {children}
    </AppointmentContext.Provider>
  );
};
