import React, { createContext, useState, useContext, useEffect } from 'react';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  sendPasswordResetEmail,
  sendEmailVerification,
  signOut,
  onAuthStateChanged,
  EmailAuthProvider,
  reauthenticateWithCredential,
  updatePassword
} from 'firebase/auth';
import { doc, setDoc, getDoc } from 'firebase/firestore';
import {
  auth,
  db,
  updateProfile
} from '../config/firebase';

// Function to generate a unique 8-character alphanumeric code
const generateUserCode = () => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let code = '';
  for (let i = 0; i < 8; i++) {
    code += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return code;
};

const AuthContext = createContext({});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isRegistering, setIsRegistering] = useState(false);

  // Register new user
  const register = async (email, password, displayName, role = 'patient') => {
    try {
      setIsRegistering(true);

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new Error('Invalid email format');
      }

      if (password.length < 6) {
        throw new Error('Password must be at least 6 characters');
      }

      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const newUser = userCredential.user;

      // Update the user's display name
      await updateProfile(newUser, {
        displayName: displayName
      });

      // Generate a unique user code
      const userCode = generateUserCode();

      // Create user document in Firestore
      const userData = {
        uid: newUser.uid,
        email: newUser.email,
        displayName: displayName,
        role: role,
        userCode: userCode,
        profileCompleted: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isActive: true,
        emailVerified: newUser.emailVerified
      };

      try {
        await setDoc(doc(db, 'users', newUser.uid), userData);
        console.log('User document created successfully');
      } catch (firestoreError) {
        console.warn('Error creating user document:', firestoreError);
        // Continue with registration even if Firestore fails
      }

      // Send email verification
      try {
        await sendEmailVerification(newUser);
        console.log('Verification email sent');
      } catch (emailError) {
        console.warn('Error sending verification email:', emailError);
      }

      // Update local user state
      setUser({ ...newUser, ...userData });

      return { ...newUser, ...userData };
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    } finally {
      setIsRegistering(false);
    }
  };

  // Login user
  const login = async (email, password) => {
    try {
      if (!email || !password) {
        throw new Error('Email and password are required');
      }

      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const currentUser = userCredential.user;

      try {
        // Get user data from Firestore
        const userRef = doc(db, 'users', currentUser.uid);
        const userSnap = await getDoc(userRef).catch(e => {
          console.log("Ignoring Firestore error during login:", e.message);
          return { exists: () => false, data: () => ({}) };
        });

        if (userSnap.exists && userSnap.exists()) {
          const userData = userSnap.data();
          setUser({ ...currentUser, ...userData });
          return { ...currentUser, ...userData };
        }
      } catch (firestoreError) {
        console.warn("Error accessing Firestore during login:", firestoreError);
        // Continue with just the auth user if Firestore fails
      }

      // Fallback to basic auth user data
      setUser(currentUser);
      return currentUser;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  // Logout user
  const logout = async () => {
    try {
      await signOut(auth);
      setUser(null);
      // Clear any local storage if needed
      localStorage.removeItem('userPreferences');
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  };

  // Reset password
  const resetPassword = async (email) => {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error) {
      console.error('Password reset error:', error);
      throw error;
    }
  };

  // Update user profile
  const updateUserProfile = async (updates) => {
    try {
      if (!user) throw new Error('No user logged in');

      // Update Firebase Auth profile if display name is being updated
      if (updates.displayName) {
        await updateProfile(auth.currentUser, {
          displayName: updates.displayName
        });
      }

      // Update Firestore document
      const userRef = doc(db, 'users', user.uid);
      const updateData = {
        ...updates,
        updatedAt: new Date().toISOString()
      };

      try {
        await setDoc(userRef, updateData, { merge: true });

        // Update local user state
        setUser(prevUser => ({ ...prevUser, ...updateData }));

        return { success: true };
      } catch (firestoreError) {
        console.warn('Error updating user document:', firestoreError);
        // Update local state even if Firestore fails
        setUser(prevUser => ({ ...prevUser, ...updateData }));
        return { success: true, warning: 'Profile updated locally but may not sync' };
      }
    } catch (error) {
      console.error('Profile update error:', error);
      throw error;
    }
  };

  // Change password
  const changePassword = async (currentPassword, newPassword) => {
    try {
      if (!user) throw new Error('No user logged in');

      const credential = EmailAuthProvider.credential(user.email, currentPassword);
      await reauthenticateWithCredential(auth.currentUser, credential);
      await updatePassword(auth.currentUser, newPassword);

      return { success: true };
    } catch (error) {
      console.error('Password change error:', error);
      throw error;
    }
  };

  // Monitor auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (authUser) => {
      try {
        if (authUser && !isRegistering) {
          // User is signed in
          console.log('🔄 Auth state changed - User signed in:', authUser.uid);
          try {
            const userRef = doc(db, 'users', authUser.uid);
            const userSnap = await getDoc(userRef);

            if (userSnap.exists()) {
              const userData = userSnap.data();
              console.log('✅ User data loaded from Firestore in auth state change:', userData);
              setUser({ ...authUser, ...userData });
            } else {
              console.log('❌ User document not found in auth state change, creating basic profile');
              // Create basic user document if it doesn't exist
              const basicUserData = {
                uid: authUser.uid,
                email: authUser.email,
                displayName: authUser.displayName || '',
                role: 'patient',
                profileCompleted: false,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                isActive: true,
                emailVerified: authUser.emailVerified
              };

              await setDoc(userRef, basicUserData);
              setUser({ ...authUser, ...basicUserData });
            }
          } catch (firestoreError) {
            console.error('❌ Error in auth state change:', firestoreError);
            setUser(authUser);
          }
        } else if (!isRegistering) {
          // User is signed out
          console.log('🔄 Auth state changed - User signed out');
          setUser(null);
        }
      } catch (error) {
        console.warn('Error in auth state change:', error);
        if (!isRegistering) setUser(authUser);
      } finally {
        setLoading(false);
      }
    });

    return unsubscribe;
  }, [isRegistering]);

  const value = {
    user,
    loading,
    login,
    register,
    logout,
    resetPassword,
    updateUserProfile,
    changePassword,
    isRegistering
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
