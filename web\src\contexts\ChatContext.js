import React, { createContext, useContext, useState, useEffect } from 'react';
import { 
  collection, 
  query, 
  where, 
  orderBy, 
  onSnapshot, 
  addDoc, 
  serverTimestamp,
  updateDoc,
  doc,
  getDocs
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { useAuth } from './AuthContext';
import { toast } from 'react-toastify';

const ChatContext = createContext();

export const useChat = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};

export const ChatProvider = ({ children }) => {
  const { user } = useAuth();
  const [conversations, setConversations] = useState([]);
  const [activeConversation, setActiveConversation] = useState(null);
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState([]);

  // Load user conversations
  useEffect(() => {
    if (!user?.uid) return;

    const conversationsCollection = collection(db, 'conversations');
    const q = query(
      conversationsCollection,
      where('participants', 'array-contains', user.uid),
      orderBy('lastMessageTime', 'desc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const conversationsData = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        conversationsData.push({
          id: doc.id,
          ...data,
          lastMessageTime: data.lastMessageTime?.toDate?.() || new Date()
        });
      });
      setConversations(conversationsData);
    }, (error) => {
      console.error('Error loading conversations:', error);
      toast.error('Failed to load conversations');
    });

    return unsubscribe;
  }, [user]);

  // Load messages for active conversation
  useEffect(() => {
    if (!activeConversation?.id) {
      setMessages([]);
      return;
    }

    const messagesCollection = collection(db, 'messages');
    const q = query(
      messagesCollection,
      where('conversationId', '==', activeConversation.id),
      orderBy('timestamp', 'asc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const messagesData = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        messagesData.push({
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate?.() || new Date()
        });
      });
      setMessages(messagesData);
    }, (error) => {
      console.error('Error loading messages:', error);
      toast.error('Failed to load messages');
    });

    return unsubscribe;
  }, [activeConversation]);

  // Create or get conversation
  const createConversation = async (participantId, participantName, participantRole) => {
    try {
      setLoading(true);

      // Check if conversation already exists
      const conversationsCollection = collection(db, 'conversations');
      const q = query(
        conversationsCollection,
        where('participants', 'array-contains', user.uid)
      );

      const querySnapshot = await getDocs(q);
      let existingConversation = null;

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.participants.includes(participantId)) {
          existingConversation = { id: doc.id, ...data };
        }
      });

      if (existingConversation) {
        setActiveConversation(existingConversation);
        return existingConversation;
      }

      // Create new conversation
      const conversationData = {
        participants: [user.uid, participantId],
        participantNames: {
          [user.uid]: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email,
          [participantId]: participantName
        },
        participantRoles: {
          [user.uid]: user.role,
          [participantId]: participantRole
        },
        lastMessage: '',
        lastMessageTime: serverTimestamp(),
        lastMessageSender: '',
        unreadCount: {
          [user.uid]: 0,
          [participantId]: 0
        },
        createdAt: serverTimestamp(),
        isActive: true
      };

      const docRef = await addDoc(conversationsCollection, conversationData);
      const newConversation = {
        id: docRef.id,
        ...conversationData,
        lastMessageTime: new Date()
      };

      setActiveConversation(newConversation);
      return newConversation;
    } catch (error) {
      console.error('Error creating conversation:', error);
      toast.error('Failed to create conversation');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Send message
  const sendMessage = async (text, type = 'text', attachments = []) => {
    if (!activeConversation?.id || !text.trim()) return;

    try {
      const messageData = {
        conversationId: activeConversation.id,
        senderId: user.uid,
        senderName: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email,
        senderRole: user.role,
        text: text.trim(),
        type,
        attachments,
        timestamp: serverTimestamp(),
        readBy: [user.uid],
        editedAt: null,
        isDeleted: false
      };

      // Add message to messages collection
      const messagesCollection = collection(db, 'messages');
      await addDoc(messagesCollection, messageData);

      // Update conversation with last message info
      const conversationRef = doc(db, 'conversations', activeConversation.id);
      const otherParticipantId = activeConversation.participants.find(id => id !== user.uid);
      
      await updateDoc(conversationRef, {
        lastMessage: text.trim(),
        lastMessageTime: serverTimestamp(),
        lastMessageSender: user.uid,
        [`unreadCount.${otherParticipantId}`]: (activeConversation.unreadCount?.[otherParticipantId] || 0) + 1
      });

    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
      throw error;
    }
  };

  // Mark messages as read
  const markAsRead = async (conversationId) => {
    if (!conversationId || !user?.uid) return;

    try {
      const conversationRef = doc(db, 'conversations', conversationId);
      await updateDoc(conversationRef, {
        [`unreadCount.${user.uid}`]: 0
      });
    } catch (error) {
      console.error('Error marking as read:', error);
    }
  };

  // Get conversation with user
  const getConversationWithUser = (userId) => {
    return conversations.find(conv => 
      conv.participants.includes(userId) && conv.participants.includes(user.uid)
    );
  };

  // Get unread count for user
  const getUnreadCount = () => {
    return conversations.reduce((total, conv) => {
      return total + (conv.unreadCount?.[user.uid] || 0);
    }, 0);
  };

  // Start video call
  const startVideoCall = async (conversationId) => {
    try {
      const callData = {
        conversationId,
        initiatorId: user.uid,
        initiatorName: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email,
        status: 'calling',
        startTime: serverTimestamp(),
        participants: activeConversation.participants
      };

      const callsCollection = collection(db, 'videoCalls');
      const docRef = await addDoc(callsCollection, callData);

      // Send call notification message
      await sendMessage('📞 Video call started', 'call', []);

      return docRef.id;
    } catch (error) {
      console.error('Error starting video call:', error);
      toast.error('Failed to start video call');
      throw error;
    }
  };

  // End video call
  const endVideoCall = async (callId) => {
    try {
      const callRef = doc(db, 'videoCalls', callId);
      await updateDoc(callRef, {
        status: 'ended',
        endTime: serverTimestamp()
      });

      // Send call ended message
      await sendMessage('📞 Video call ended', 'call', []);
    } catch (error) {
      console.error('Error ending video call:', error);
      toast.error('Failed to end video call');
    }
  };

  const value = {
    // State
    conversations,
    activeConversation,
    messages,
    loading,
    onlineUsers,

    // Actions
    setActiveConversation,
    createConversation,
    sendMessage,
    markAsRead,
    startVideoCall,
    endVideoCall,

    // Helpers
    getConversationWithUser,
    getUnreadCount
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
};

export default ChatContext;
