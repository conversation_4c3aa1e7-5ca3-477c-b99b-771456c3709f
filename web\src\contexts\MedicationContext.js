import React, { createContext, useContext, useState, useEffect } from 'react';
import { collection, query, where, orderBy, onSnapshot, addDoc, updateDoc, deleteDoc, doc } from 'firebase/firestore';
import { db } from '../config/firebase';
import { useAuth } from './AuthContext';

const MedicationContext = createContext();

export const useMedications = () => {
  const context = useContext(MedicationContext);
  if (!context) {
    throw new Error('useMedications must be used within a MedicationProvider');
  }
  return context;
};

export const MedicationProvider = ({ children }) => {
  const [medications, setMedications] = useState([]);
  const [prescriptions, setPrescriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useAuth();

  // Fetch medications and prescriptions based on user role
  useEffect(() => {
    if (!user) {
      setMedications([]);
      setPrescriptions([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      let medicationsQuery;
      let prescriptionsQuery;

      // Build queries based on user role
      switch (user.role) {
        case 'patient':
          medicationsQuery = query(
            collection(db, 'medications'),
            where('patientId', '==', user.uid),
            orderBy('createdAt', 'desc')
          );
          prescriptionsQuery = query(
            collection(db, 'prescriptions'),
            where('patientId', '==', user.uid),
            where('status', '==', 'sent'),
            orderBy('createdAt', 'desc')
          );
          break;
        case 'doctor':
          medicationsQuery = query(
            collection(db, 'medications'),
            where('doctorId', '==', user.uid),
            orderBy('createdAt', 'desc')
          );
          prescriptionsQuery = query(
            collection(db, 'prescriptions'),
            where('doctorId', '==', user.uid),
            orderBy('createdAt', 'desc')
          );
          break;
        case 'caregiver':
          medicationsQuery = query(
            collection(db, 'medications'),
            where('caregiverId', '==', user.uid),
            orderBy('createdAt', 'desc')
          );
          prescriptionsQuery = query(
            collection(db, 'prescriptions'),
            where('caregiverId', '==', user.uid),
            orderBy('createdAt', 'desc')
          );
          break;
        case 'supervisor':
        case 'admin':
          // Supervisors and admins can see all medications and prescriptions
          medicationsQuery = query(
            collection(db, 'medications'),
            orderBy('createdAt', 'desc')
          );
          prescriptionsQuery = query(
            collection(db, 'prescriptions'),
            orderBy('createdAt', 'desc')
          );
          break;
        default:
          medicationsQuery = query(
            collection(db, 'medications'),
            where('patientId', '==', user.uid),
            orderBy('createdAt', 'desc')
          );
          prescriptionsQuery = query(
            collection(db, 'prescriptions'),
            where('patientId', '==', user.uid),
            orderBy('createdAt', 'desc')
          );
      }

      // Set up medications listener
      const unsubscribeMedications = onSnapshot(
        medicationsQuery,
        (snapshot) => {
          const medicationsList = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
          setMedications(medicationsList);
        },
        (error) => {
          console.error('Error fetching medications:', error);
          setError(error.message);
        }
      );

      // Set up prescriptions listener
      const unsubscribePrescriptions = onSnapshot(
        prescriptionsQuery,
        (snapshot) => {
          const prescriptionsList = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
          setPrescriptions(prescriptionsList);
          setLoading(false);
        },
        (error) => {
          console.error('Error fetching prescriptions:', error);
          setError(error.message);
          setLoading(false);
        }
      );

      return () => {
        unsubscribeMedications();
        unsubscribePrescriptions();
      };
    } catch (error) {
      console.error('Error setting up medications/prescriptions listeners:', error);
      setError(error.message);
      setLoading(false);
    }
  }, [user]);

  // Add new medication
  const addMedication = async (medicationData) => {
    try {
      const newMedication = {
        ...medicationData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 'active'
      };

      const docRef = await addDoc(collection(db, 'medications'), newMedication);
      return { id: docRef.id, ...newMedication };
    } catch (error) {
      console.error('Error adding medication:', error);
      throw error;
    }
  };

  // Update medication
  const updateMedication = async (medicationId, updates) => {
    try {
      const medicationRef = doc(db, 'medications', medicationId);
      const updateData = {
        ...updates,
        updatedAt: new Date().toISOString()
      };

      await updateDoc(medicationRef, updateData);
      return updateData;
    } catch (error) {
      console.error('Error updating medication:', error);
      throw error;
    }
  };

  // Delete medication
  const deleteMedication = async (medicationId) => {
    try {
      await deleteDoc(doc(db, 'medications', medicationId));
    } catch (error) {
      console.error('Error deleting medication:', error);
      throw error;
    }
  };

  // Add new prescription
  const addPrescription = async (prescriptionData) => {
    try {
      const newPrescription = {
        ...prescriptionData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 'sent'
      };

      const docRef = await addDoc(collection(db, 'prescriptions'), newPrescription);
      return { id: docRef.id, ...newPrescription };
    } catch (error) {
      console.error('Error adding prescription:', error);
      throw error;
    }
  };

  // Update prescription
  const updatePrescription = async (prescriptionId, updates) => {
    try {
      const prescriptionRef = doc(db, 'prescriptions', prescriptionId);
      const updateData = {
        ...updates,
        updatedAt: new Date().toISOString()
      };

      await updateDoc(prescriptionRef, updateData);
      return updateData;
    } catch (error) {
      console.error('Error updating prescription:', error);
      throw error;
    }
  };

  // Get active medications
  const getActiveMedications = () => {
    return medications.filter(medication => medication.status === 'active');
  };

  // Get recent prescriptions (last 3)
  const getRecentPrescriptions = () => {
    return prescriptions
      .filter(prescription => prescription.status === 'sent')
      .slice(0, 3);
  };

  // Get medications by status
  const getMedicationsByStatus = (status) => {
    return medications.filter(medication => medication.status === status);
  };

  // Get prescriptions by status
  const getPrescriptionsByStatus = (status) => {
    return prescriptions.filter(prescription => prescription.status === status);
  };

  const value = {
    medications,
    prescriptions,
    loading,
    error,
    addMedication,
    updateMedication,
    deleteMedication,
    addPrescription,
    updatePrescription,
    getActiveMedications,
    getRecentPrescriptions,
    getMedicationsByStatus,
    getPrescriptionsByStatus,
  };

  return (
    <MedicationContext.Provider value={value}>
      {children}
    </MedicationContext.Provider>
  );
};
