import React, { createContext, useContext, useState, useEffect } from 'react';
import { COLORS, ROLE_THEMES, DEFAULT_THEME } from '../config/theme';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState(DEFAULT_THEME);
  const [userRole, setUserRole] = useState(null);
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Get theme based on user role
  const getThemeForRole = (role) => {
    const roleTheme = ROLE_THEMES[role] || ROLE_THEMES.patient;
    return {
      ...DEFAULT_THEME,
      colors: {
        ...DEFAULT_THEME.colors,
        ...roleTheme,
      },
    };
  };

  // Update theme when user role changes
  const updateThemeForRole = (role) => {
    setUserRole(role);
    const newTheme = getThemeForRole(role);
    setCurrentTheme(newTheme);

    // Store preference in localStorage
    localStorage.setItem('userRole', role);
  };

  // Toggle dark mode
  const toggleDarkMode = () => {
    setIsDarkMode(prev => {
      const newMode = !prev;
      localStorage.setItem('darkMode', JSON.stringify(newMode));
      return newMode;
    });
  };

  // Get role-specific colors
  const getRoleColors = (role) => {
    return ROLE_THEMES[role] || ROLE_THEMES.patient;
  };

  // Get primary color for current role
  const getPrimaryColor = () => {
    return currentTheme.colors.primary;
  };

  // Get secondary color for current role
  const getSecondaryColor = () => {
    return currentTheme.colors.secondary;
  };

  // Load saved preferences on mount
  useEffect(() => {
    const savedRole = localStorage.getItem('userRole');
    const savedDarkMode = localStorage.getItem('darkMode');

    if (savedRole) {
      updateThemeForRole(savedRole);
    }

    if (savedDarkMode) {
      setIsDarkMode(JSON.parse(savedDarkMode));
    }
  }, []);

  // Apply dark mode styles when toggled
  useEffect(() => {
    if (isDarkMode) {
      setCurrentTheme(prev => ({
        ...prev,
        colors: {
          ...prev.colors,
          background: '#121212',
          surface: '#1E1E1E',
          text: '#FFFFFF',
          textSecondary: '#B0B0B0',
          border: '#333333',
          borderLight: '#2A2A2A',
        },
      }));
    } else {
      // Reset to light mode
      const roleTheme = getThemeForRole(userRole);
      setCurrentTheme(roleTheme);
    }
  }, [isDarkMode, userRole]);

  const value = {
    theme: currentTheme || DEFAULT_THEME,
    userRole,
    isDarkMode,
    updateThemeForRole,
    toggleDarkMode,
    getRoleColors,
    getPrimaryColor,
    getSecondaryColor,
    colors: COLORS,
    roleThemes: ROLE_THEMES,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
