import React, { createContext, useContext, useState, useEffect } from 'react';
import { collection, query, where, getDocs, doc, updateDoc, deleteDoc, onSnapshot } from 'firebase/firestore';
import { db } from '../config/firebase';
import { useAuth } from './AuthContext';
import { USER_ROLES } from '../config/constants';

const UsersContext = createContext();

export const useUsers = () => {
  const context = useContext(UsersContext);
  if (!context) {
    throw new Error('useUsers must be used within a UsersProvider');
  }
  return context;
};

export const UsersProvider = ({ children }) => {
  const { user } = useAuth();
  const [users, setUsers] = useState([]);
  const [patients, setPatients] = useState([]);
  const [doctors, setDoctors] = useState([]);
  const [caregivers, setCaregivers] = useState([]);
  const [supervisors, setSupervisors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load all users based on current user's role and permissions
  const loadUsers = async (role = null) => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      let usersQuery;
      
      if (role) {
        // Load users of specific role
        usersQuery = query(
          collection(db, 'users'),
          where('role', '==', role)
        );
      } else {
        // Load all users (for admin/supervisor)
        usersQuery = collection(db, 'users');
      }

      const snapshot = await getDocs(usersQuery);
      const usersData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      setUsers(usersData);
      
      // Categorize users by role
      categorizeUsers(usersData);
      
    } catch (error) {
      console.error('Error loading users:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Categorize users by their roles
  const categorizeUsers = (usersData) => {
    const patientsList = usersData.filter(u => u.role === USER_ROLES.PATIENT);
    const doctorsList = usersData.filter(u => u.role === USER_ROLES.DOCTOR);
    const caregiversList = usersData.filter(u => u.role === USER_ROLES.CAREGIVER);
    const supervisorsList = usersData.filter(u => u.role === USER_ROLES.SUPERVISOR);

    setPatients(patientsList);
    setDoctors(doctorsList);
    setCaregivers(caregiversList);
    setSupervisors(supervisorsList);
  };

  // Load patients assigned to current user (for caregivers/doctors)
  const loadAssignedPatients = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      // Query patients linked to current user
      const patientsQuery = query(
        collection(db, 'users'),
        where('role', '==', USER_ROLES.PATIENT),
        where('assignedTo', 'array-contains', user.uid)
      );

      const snapshot = await getDocs(patientsQuery);
      const patientsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      setPatients(patientsData);
      
    } catch (error) {
      console.error('Error loading assigned patients:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Load doctors assigned to current patient
  const loadAssignedDoctors = async () => {
    if (!user || user.role !== USER_ROLES.PATIENT) return;

    try {
      setLoading(true);
      setError(null);

      // Query doctors linked to current patient
      const doctorsQuery = query(
        collection(db, 'users'),
        where('role', '==', USER_ROLES.DOCTOR),
        where('patients', 'array-contains', user.uid)
      );

      const snapshot = await getDocs(doctorsQuery);
      const doctorsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      setDoctors(doctorsData);
      
    } catch (error) {
      console.error('Error loading assigned doctors:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Load caregivers assigned to current patient
  const loadAssignedCaregivers = async () => {
    if (!user || user.role !== USER_ROLES.PATIENT) return;

    try {
      setLoading(true);
      setError(null);

      // Query caregivers linked to current patient
      const caregiversQuery = query(
        collection(db, 'users'),
        where('role', '==', USER_ROLES.CAREGIVER),
        where('patients', 'array-contains', user.uid)
      );

      const snapshot = await getDocs(caregiversQuery);
      const caregiversData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      setCaregivers(caregiversData);
      
    } catch (error) {
      console.error('Error loading assigned caregivers:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Update user profile
  const updateUser = async (userId, updateData) => {
    try {
      setLoading(true);
      setError(null);

      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        ...updateData,
        updatedAt: new Date().toISOString()
      });

      // Reload users to reflect changes
      await loadUsers();
      
      return { success: true };
      
    } catch (error) {
      console.error('Error updating user:', error);
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  // Delete user (admin only)
  const deleteUser = async (userId) => {
    try {
      setLoading(true);
      setError(null);

      const userRef = doc(db, 'users', userId);
      await deleteDoc(userRef);

      // Reload users to reflect changes
      await loadUsers();
      
      return { success: true };
      
    } catch (error) {
      console.error('Error deleting user:', error);
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  // Assign patient to caregiver/doctor
  const assignPatientToUser = async (patientId, assigneeId, assigneeRole) => {
    try {
      setLoading(true);
      setError(null);

      // Update patient's assignedTo array
      const patientRef = doc(db, 'users', patientId);
      await updateDoc(patientRef, {
        assignedTo: [...(patients.find(p => p.id === patientId)?.assignedTo || []), assigneeId],
        updatedAt: new Date().toISOString()
      });

      // Update assignee's patients array
      const assigneeRef = doc(db, 'users', assigneeId);
      const assigneeData = users.find(u => u.id === assigneeId);
      await updateDoc(assigneeRef, {
        patients: [...(assigneeData?.patients || []), patientId],
        updatedAt: new Date().toISOString()
      });

      // Reload users to reflect changes
      await loadUsers();
      
      return { success: true };
      
    } catch (error) {
      console.error('Error assigning patient:', error);
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  // Remove patient assignment
  const removePatientAssignment = async (patientId, assigneeId) => {
    try {
      setLoading(true);
      setError(null);

      // Update patient's assignedTo array
      const patientRef = doc(db, 'users', patientId);
      const patientData = patients.find(p => p.id === patientId);
      await updateDoc(patientRef, {
        assignedTo: (patientData?.assignedTo || []).filter(id => id !== assigneeId),
        updatedAt: new Date().toISOString()
      });

      // Update assignee's patients array
      const assigneeRef = doc(db, 'users', assigneeId);
      const assigneeData = users.find(u => u.id === assigneeId);
      await updateDoc(assigneeRef, {
        patients: (assigneeData?.patients || []).filter(id => id !== patientId),
        updatedAt: new Date().toISOString()
      });

      // Reload users to reflect changes
      await loadUsers();
      
      return { success: true };
      
    } catch (error) {
      console.error('Error removing patient assignment:', error);
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  // Search users by name, email, or role
  const searchUsers = (searchTerm, role = null) => {
    let searchResults = users;
    
    if (role) {
      searchResults = searchResults.filter(user => user.role === role);
    }
    
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      searchResults = searchResults.filter(user => 
        user.displayName?.toLowerCase().includes(term) ||
        user.email?.toLowerCase().includes(term) ||
        user.firstName?.toLowerCase().includes(term) ||
        user.lastName?.toLowerCase().includes(term)
      );
    }
    
    return searchResults;
  };

  // Get user by ID
  const getUserById = (userId) => {
    return users.find(user => user.id === userId);
  };

  // Get users by role
  const getUsersByRole = (role) => {
    return users.filter(user => user.role === role);
  };

  // Set up real-time listeners for users
  useEffect(() => {
    if (!user) return;

    let unsubscribe;

    // Set up real-time listener based on user role
    if (user.role === USER_ROLES.ADMIN || user.role === USER_ROLES.SUPERVISOR) {
      // Admin and supervisors can see all users
      unsubscribe = onSnapshot(
        collection(db, 'users'),
        (snapshot) => {
          const usersData = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
          setUsers(usersData);
          categorizeUsers(usersData);
        },
        (error) => {
          console.error('Error in users listener:', error);
          setError(error.message);
        }
      );
    } else {
      // Other roles load specific users
      loadAssignedPatients();
      loadAssignedDoctors();
      loadAssignedCaregivers();
    }

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [user]);

  const value = {
    // State
    users,
    patients,
    doctors,
    caregivers,
    supervisors,
    loading,
    error,
    
    // Actions
    loadUsers,
    loadAssignedPatients,
    loadAssignedDoctors,
    loadAssignedCaregivers,
    updateUser,
    deleteUser,
    assignPatientToUser,
    removePatientAssignment,
    searchUsers,
    getUserById,
    getUsersByRole
  };

  return (
    <UsersContext.Provider value={value}>
      {children}
    </UsersContext.Provider>
  );
};
