import React, { createContext, useContext, useState, useEffect } from 'react';
import { collection, query, where, orderBy, onSnapshot, addDoc, updateDoc, deleteDoc, doc, limit } from 'firebase/firestore';
import { db } from '../config/firebase';
import { useAuth } from './AuthContext';

const VitalsContext = createContext();

export const useVitals = () => {
  const context = useContext(VitalsContext);
  if (!context) {
    throw new Error('useVitals must be used within a VitalsProvider');
  }
  return context;
};

export const VitalsProvider = ({ children }) => {
  const [vitals, setVitals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useAuth();

  // Fetch vitals based on user role
  useEffect(() => {
    if (!user) {
      setVitals([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      let vitalsQuery;

      // Build query based on user role
      switch (user.role) {
        case 'patient':
          vitalsQuery = query(
            collection(db, 'vitals'),
            where('patientId', '==', user.uid),
            orderBy('recordedAt', 'desc')
          );
          break;
        case 'doctor':
          vitalsQuery = query(
            collection(db, 'vitals'),
            where('doctorId', '==', user.uid),
            orderBy('recordedAt', 'desc')
          );
          break;
        case 'caregiver':
          vitalsQuery = query(
            collection(db, 'vitals'),
            where('caregiverId', '==', user.uid),
            orderBy('recordedAt', 'desc')
          );
          break;
        case 'supervisor':
        case 'admin':
          // Supervisors and admins can see all vitals
          vitalsQuery = query(
            collection(db, 'vitals'),
            orderBy('recordedAt', 'desc')
          );
          break;
        default:
          vitalsQuery = query(
            collection(db, 'vitals'),
            where('patientId', '==', user.uid),
            orderBy('recordedAt', 'desc')
          );
      }

      const unsubscribe = onSnapshot(
        vitalsQuery,
        (snapshot) => {
          const vitalsList = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
          setVitals(vitalsList);
          setLoading(false);
        },
        (error) => {
          console.error('Error fetching vitals:', error);
          setError(error.message);
          setLoading(false);
        }
      );

      return unsubscribe;
    } catch (error) {
      console.error('Error setting up vitals listener:', error);
      setError(error.message);
      setLoading(false);
    }
  }, [user]);

  // Add new vital record
  const addVital = async (vitalData) => {
    try {
      const newVital = {
        ...vitalData,
        recordedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const docRef = await addDoc(collection(db, 'vitals'), newVital);
      return { id: docRef.id, ...newVital };
    } catch (error) {
      console.error('Error adding vital:', error);
      throw error;
    }
  };

  // Update vital record
  const updateVital = async (vitalId, updates) => {
    try {
      const vitalRef = doc(db, 'vitals', vitalId);
      const updateData = {
        ...updates,
        updatedAt: new Date().toISOString()
      };

      await updateDoc(vitalRef, updateData);
      return updateData;
    } catch (error) {
      console.error('Error updating vital:', error);
      throw error;
    }
  };

  // Delete vital record
  const deleteVital = async (vitalId) => {
    try {
      await deleteDoc(doc(db, 'vitals', vitalId));
    } catch (error) {
      console.error('Error deleting vital:', error);
      throw error;
    }
  };

  // Get recent vitals (last 10)
  const getRecentVitals = () => {
    return vitals.slice(0, 10);
  };

  // Get vitals by type
  const getVitalsByType = (type) => {
    return vitals.filter(vital => vital.type === type);
  };

  // Get vitals for a specific date range
  const getVitalsInDateRange = (startDate, endDate) => {
    return vitals.filter(vital => {
      const vitalDate = new Date(vital.recordedAt);
      return vitalDate >= startDate && vitalDate <= endDate;
    });
  };

  // Get today's vitals
  const getTodaysVitals = () => {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    return vitals.filter(vital => {
      const vitalDate = new Date(vital.recordedAt);
      return vitalDate >= startOfDay && vitalDate < endOfDay;
    });
  };

  // Get latest vital by type
  const getLatestVitalByType = (type) => {
    const typeVitals = getVitalsByType(type);
    return typeVitals.length > 0 ? typeVitals[0] : null;
  };

  // Get vital statistics
  const getVitalStatistics = (type, days = 7) => {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const recentVitals = vitals.filter(vital => {
      const vitalDate = new Date(vital.recordedAt);
      return vital.type === type && vitalDate >= cutoffDate;
    });

    if (recentVitals.length === 0) {
      return { average: 0, min: 0, max: 0, count: 0 };
    }

    const values = recentVitals.map(vital => parseFloat(vital.value));
    const sum = values.reduce((acc, val) => acc + val, 0);
    const average = sum / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);

    return {
      average: Math.round(average * 100) / 100,
      min,
      max,
      count: values.length
    };
  };

  // Check if vital is within normal range
  const isVitalNormal = (type, value) => {
    const normalRanges = {
      'blood_pressure_systolic': { min: 90, max: 140 },
      'blood_pressure_diastolic': { min: 60, max: 90 },
      'heart_rate': { min: 60, max: 100 },
      'temperature': { min: 36.1, max: 37.2 },
      'oxygen_saturation': { min: 95, max: 100 },
      'blood_sugar': { min: 70, max: 140 },
      'weight': { min: 0, max: 1000 }, // Very broad range
      'height': { min: 0, max: 300 }   // Very broad range
    };

    const range = normalRanges[type];
    if (!range) return true; // Unknown type, assume normal

    const numValue = parseFloat(value);
    return numValue >= range.min && numValue <= range.max;
  };

  const value = {
    vitals,
    loading,
    error,
    addVital,
    updateVital,
    deleteVital,
    getRecentVitals,
    getVitalsByType,
    getVitalsInDateRange,
    getTodaysVitals,
    getLatestVitalByType,
    getVitalStatistics,
    isVitalNormal,
  };

  return (
    <VitalsContext.Provider value={value}>
      {children}
    </VitalsContext.Provider>
  );
};
