import { useState, useEffect, useCallback, useRef } from 'react';

/**
 * Custom hook for API calls with loading, error, and data state management
 * @param {function} apiFunction - The API function to call
 * @param {any} initialData - Initial data value
 * @param {boolean} immediate - Whether to call the API immediately
 * @returns {object} API state and control functions
 */
const useApi = (apiFunction, initialData = null, immediate = true) => {
  const [data, setData] = useState(initialData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastFetch, setLastFetch] = useState(null);
  
  // Use ref to track if component is mounted to prevent state updates after unmount
  const isMountedRef = useRef(true);
  
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Execute the API call
  const execute = useCallback(async (...args) => {
    if (!isMountedRef.current) return;
    
    setLoading(true);
    setError(null);

    try {
      const result = await apiFunction(...args);
      
      if (isMountedRef.current) {
        setData(result);
        setLastFetch(new Date());
      }
      
      return result;
    } catch (err) {
      if (isMountedRef.current) {
        setError(err);
        console.error('API call failed:', err);
      }
      throw err;
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [apiFunction]);

  // Reset the state
  const reset = useCallback(() => {
    if (!isMountedRef.current) return;
    
    setData(initialData);
    setLoading(false);
    setError(null);
    setLastFetch(null);
  }, [initialData]);

  // Refresh the data (re-execute with last parameters)
  const refresh = useCallback(() => {
    return execute();
  }, [execute]);

  // Execute immediately if requested
  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [immediate, execute]);

  return {
    data,
    loading,
    error,
    lastFetch,
    execute,
    reset,
    refresh,
    // Computed states
    isSuccess: !loading && !error && data !== null,
    isError: !loading && !!error,
    isEmpty: !loading && !error && (data === null || data === undefined || (Array.isArray(data) && data.length === 0))
  };
};

/**
 * Hook for paginated API calls
 * @param {function} apiFunction - The API function that accepts page and limit parameters
 * @param {object} options - Configuration options
 * @returns {object} Paginated API state and controls
 */
export const usePaginatedApi = (apiFunction, options = {}) => {
  const {
    initialPage = 1,
    pageSize = 10,
    immediate = true
  } = options;

  const [page, setPage] = useState(initialPage);
  const [totalPages, setTotalPages] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [allData, setAllData] = useState([]);

  const {
    data,
    loading,
    error,
    execute,
    reset: resetApi
  } = useApi(
    useCallback(
      (pageNum = page, limit = pageSize) => apiFunction(pageNum, limit),
      [apiFunction, page, pageSize]
    ),
    null,
    false
  );

  // Update pagination info when data changes
  useEffect(() => {
    if (data) {
      if (data.totalPages !== undefined) setTotalPages(data.totalPages);
      if (data.totalItems !== undefined) setTotalItems(data.totalItems);
      if (data.items) {
        if (page === 1) {
          setAllData(data.items);
        } else {
          setAllData(prev => [...prev, ...data.items]);
        }
      }
    }
  }, [data, page]);

  // Load specific page
  const loadPage = useCallback((pageNum) => {
    setPage(pageNum);
    return execute(pageNum, pageSize);
  }, [execute, pageSize]);

  // Load next page
  const loadNextPage = useCallback(() => {
    if (page < totalPages) {
      return loadPage(page + 1);
    }
  }, [page, totalPages, loadPage]);

  // Load previous page
  const loadPreviousPage = useCallback(() => {
    if (page > 1) {
      return loadPage(page - 1);
    }
  }, [page, loadPage]);

  // Reset pagination
  const reset = useCallback(() => {
    setPage(initialPage);
    setTotalPages(0);
    setTotalItems(0);
    setAllData([]);
    resetApi();
  }, [initialPage, resetApi]);

  // Load first page immediately if requested
  useEffect(() => {
    if (immediate) {
      loadPage(initialPage);
    }
  }, [immediate, initialPage, loadPage]);

  return {
    // Data
    data: data?.items || [],
    allData,
    
    // Pagination state
    page,
    totalPages,
    totalItems,
    pageSize,
    
    // Loading and error states
    loading,
    error,
    
    // Actions
    loadPage,
    loadNextPage,
    loadPreviousPage,
    reset,
    
    // Computed states
    hasNextPage: page < totalPages,
    hasPreviousPage: page > 1,
    isFirstPage: page === 1,
    isLastPage: page === totalPages,
    isEmpty: !loading && !error && allData.length === 0
  };
};

/**
 * Hook for infinite scroll API calls
 * @param {function} apiFunction - The API function
 * @param {object} options - Configuration options
 * @returns {object} Infinite scroll API state and controls
 */
export const useInfiniteApi = (apiFunction, options = {}) => {
  const {
    pageSize = 10,
    immediate = true
  } = options;

  const [hasMore, setHasMore] = useState(true);
  const [allData, setAllData] = useState([]);
  const pageRef = useRef(1);

  const {
    data,
    loading,
    error,
    execute
  } = useApi(
    useCallback(
      (page = pageRef.current, limit = pageSize) => apiFunction(page, limit),
      [apiFunction, pageSize]
    ),
    null,
    false
  );

  // Update data when new page loads
  useEffect(() => {
    if (data) {
      const newItems = data.items || data;
      
      if (pageRef.current === 1) {
        setAllData(newItems);
      } else {
        setAllData(prev => [...prev, ...newItems]);
      }
      
      // Check if there are more items
      setHasMore(newItems.length === pageSize);
    }
  }, [data, pageSize]);

  // Load more data
  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      pageRef.current += 1;
      return execute(pageRef.current, pageSize);
    }
  }, [loading, hasMore, execute, pageSize]);

  // Reset to first page
  const reset = useCallback(() => {
    pageRef.current = 1;
    setAllData([]);
    setHasMore(true);
  }, []);

  // Refresh from beginning
  const refresh = useCallback(() => {
    reset();
    return execute(1, pageSize);
  }, [reset, execute, pageSize]);

  // Load first page immediately if requested
  useEffect(() => {
    if (immediate) {
      execute(1, pageSize);
    }
  }, [immediate, execute, pageSize]);

  return {
    data: allData,
    loading,
    error,
    hasMore,
    loadMore,
    reset,
    refresh,
    isEmpty: !loading && !error && allData.length === 0
  };
};

export default useApi;
