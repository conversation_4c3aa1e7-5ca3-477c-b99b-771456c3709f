import { useState, useCallback } from 'react';
import { validateForm } from '../utils/validation';

/**
 * Custom hook for form management with validation
 * @param {object} initialValues - Initial form values
 * @param {object} validators - Validation functions for each field
 * @param {function} onSubmit - Submit handler function
 * @returns {object} Form state and handlers
 */
const useForm = (initialValues = {}, validators = {}, onSubmit) => {
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValid, setIsValid] = useState(true);

  // Update a single field value
  const setValue = useCallback((name, value) => {
    setValues(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error for this field when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  }, [errors]);

  // Update multiple field values
  const setValues_ = useCallback((newValues) => {
    setValues(prev => ({
      ...prev,
      ...newValues
    }));
  }, []);

  // Handle input change
  const handleChange = useCallback((event) => {
    const { name, value, type, checked } = event.target;
    const fieldValue = type === 'checkbox' ? checked : value;
    setValue(name, fieldValue);
  }, [setValue]);

  // Handle input blur (mark field as touched)
  const handleBlur = useCallback((event) => {
    const { name } = event.target;
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Validate single field on blur
    if (validators[name]) {
      const validator = validators[name];
      let result;

      if (typeof validator === 'function') {
        result = validator(values[name]);
      } else if (Array.isArray(validator)) {
        for (const v of validator) {
          result = v(values[name]);
          if (!result.isValid) break;
        }
      }

      if (result && !result.isValid) {
        setErrors(prev => ({
          ...prev,
          [name]: result.message
        }));
      }
    }
  }, [validators, values]);

  // Validate all fields
  const validate = useCallback(() => {
    const validation = validateForm(values, validators);
    setErrors(validation.errors);
    setIsValid(validation.isValid);
    return validation.isValid;
  }, [values, validators]);

  // Reset form to initial values
  const reset = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
    setIsValid(true);
  }, [initialValues]);

  // Clear all errors
  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  // Set a specific error
  const setError = useCallback((name, message) => {
    setErrors(prev => ({
      ...prev,
      [name]: message
    }));
  }, []);

  // Handle form submission
  const handleSubmit = useCallback(async (event) => {
    if (event) {
      event.preventDefault();
    }

    // Mark all fields as touched
    const allTouched = Object.keys(values).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {});
    setTouched(allTouched);

    // Validate form
    const isFormValid = validate();

    if (!isFormValid || !onSubmit) {
      return;
    }

    setIsSubmitting(true);

    try {
      await onSubmit(values);
    } catch (error) {
      // Handle submission errors
      if (error.fieldErrors) {
        setErrors(error.fieldErrors);
      } else if (error.message) {
        setError('submit', error.message);
      }
    } finally {
      setIsSubmitting(false);
    }
  }, [values, validate, onSubmit, setError]);

  // Get field props for easy spreading
  const getFieldProps = useCallback((name) => ({
    name,
    value: values[name] || '',
    onChange: handleChange,
    onBlur: handleBlur,
    error: touched[name] && errors[name] ? errors[name] : '',
  }), [values, handleChange, handleBlur, touched, errors]);

  // Check if field has error
  const hasError = useCallback((name) => {
    return touched[name] && !!errors[name];
  }, [touched, errors]);

  // Check if field is touched
  const isTouched = useCallback((name) => {
    return !!touched[name];
  }, [touched]);

  // Check if form is dirty (has changes)
  const isDirty = useCallback(() => {
    return JSON.stringify(values) !== JSON.stringify(initialValues);
  }, [values, initialValues]);

  return {
    // Form state
    values,
    errors,
    touched,
    isSubmitting,
    isValid,

    // Form actions
    setValue,
    setValues: setValues_,
    setError,
    clearErrors,
    reset,
    validate,
    handleSubmit,

    // Event handlers
    handleChange,
    handleBlur,

    // Helper functions
    getFieldProps,
    hasError,
    isTouched,
    isDirty,
  };
};

export { useForm };
export default useForm;
