import {
  collection,
  doc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../config/firebase';

/**
 * Service for managing appointments in Firebase for web application
 */
export const firebaseAppointmentsService = {
  /**
   * Save a new appointment to Firebase
   * @param {Object} appointmentData - The appointment data to save
   * @param {string} patientId - The patient's user ID
   * @returns {Promise<Object>} - The saved appointment record with ID
   */
  saveAppointment: async (appointmentData, patientId) => {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      // Create appointment data with patient ID
      const appointmentToSave = {
        ...appointmentData,
        patientId: patientId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        status: appointmentData.status || 'pending'
      };

      // Add to Firestore
      const appointmentsCollection = collection(db, 'appointments');
      const docRef = await addDoc(appointmentsCollection, appointmentToSave);

      // Return the saved appointment with ID
      return {
        id: docRef.id,
        ...appointmentToSave,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error saving appointment to Firebase:', error);
      throw new Error('Failed to save appointment');
    }
  },

  /**
   * Get all appointments for a specific user
   * @param {string} userId - The user ID
   * @param {string} userRole - The user's role (patient, doctor, caregiver, supervisor)
   * @returns {Promise<Array>} - Array of appointment records
   */
  getUserAppointments: async (userId, userRole) => {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }

      const appointmentsCollection = collection(db, 'appointments');
      let q;

      // Create query based on user role
      switch (userRole) {
        case 'patient':
          q = query(appointmentsCollection, where('patientId', '==', userId));
          break;
        case 'doctor':
          q = query(appointmentsCollection, where('doctorId', '==', userId));
          break;
        default:
          // For other roles, get all appointments they have access to
          q = query(appointmentsCollection);
      }

      const querySnapshot = await getDocs(q);
      const appointments = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        const appointment = {
          id: doc.id,
          ...data
        };

        // Convert timestamps to ISO strings
        if (data.createdAt && typeof data.createdAt.toDate === 'function') {
          appointment.createdAt = data.createdAt.toDate().toISOString();
        }
        if (data.updatedAt && typeof data.updatedAt.toDate === 'function') {
          appointment.updatedAt = data.updatedAt.toDate().toISOString();
        }

        appointments.push(appointment);
      });

      return appointments;
    } catch (error) {
      console.error('Error getting appointments from Firebase:', error);
      return [];
    }
  },

  /**
   * Get upcoming appointments (appointments in the future)
   * @param {string} userId - The user ID
   * @param {string} userRole - The user's role
   * @returns {Promise<Array>} - Array of upcoming appointment records
   */
  getUpcomingAppointments: async (userId, userRole) => {
    try {
      const appointments = await firebaseAppointmentsService.getUserAppointments(userId, userRole);
      const now = new Date();

      // Filter for upcoming appointments
      return appointments.filter(appointment => {
        try {
          const appointmentDate = new Date(`${appointment.date}T${appointment.time}`);
          return appointmentDate > now && appointment.status !== 'cancelled';
        } catch (error) {
          return false;
        }
      }).sort((a, b) => {
        const dateA = new Date(`${a.date}T${a.time}`);
        const dateB = new Date(`${b.date}T${b.time}`);
        return dateA - dateB;
      });
    } catch (error) {
      console.error('Error getting upcoming appointments:', error);
      return [];
    }
  },

  /**
   * Update an appointment
   * @param {string} appointmentId - The appointment ID to update
   * @param {Object} updatedData - The updated appointment data
   * @returns {Promise<Object>} - The updated appointment
   */
  updateAppointment: async (appointmentId, updatedData) => {
    try {
      if (!appointmentId) {
        throw new Error('Appointment ID is required');
      }

      const appointmentRef = doc(db, 'appointments', appointmentId);
      const appointmentSnap = await getDoc(appointmentRef);

      if (!appointmentSnap.exists()) {
        throw new Error('Appointment not found');
      }

      const currentData = appointmentSnap.data();

      // Update the document
      const dataToUpdate = {
        ...updatedData,
        updatedAt: serverTimestamp()
      };

      await updateDoc(appointmentRef, dataToUpdate);

      // Return the updated appointment
      return {
        id: appointmentId,
        ...currentData,
        ...dataToUpdate,
        updatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error updating appointment in Firebase:', error);
      throw new Error('Failed to update appointment');
    }
  },

  /**
   * Delete an appointment
   * @param {string} appointmentId - The appointment ID to delete
   * @returns {Promise<boolean>} - Success status
   */
  deleteAppointment: async (appointmentId) => {
    try {
      if (!appointmentId) {
        throw new Error('Appointment ID is required');
      }

      const appointmentRef = doc(db, 'appointments', appointmentId);
      await deleteDoc(appointmentRef);
      return true;
    } catch (error) {
      console.error('Error deleting appointment from Firebase:', error);
      return false;
    }
  },

  /**
   * Get appointment by ID
   * @param {string} appointmentId - The appointment ID
   * @returns {Promise<Object|null>} - The appointment or null if not found
   */
  getAppointmentById: async (appointmentId) => {
    try {
      if (!appointmentId) {
        throw new Error('Appointment ID is required');
      }

      const appointmentRef = doc(db, 'appointments', appointmentId);
      const appointmentSnap = await getDoc(appointmentRef);

      if (!appointmentSnap.exists()) {
        return null;
      }

      const data = appointmentSnap.data();
      const appointment = {
        id: appointmentId,
        ...data
      };

      // Convert timestamps to ISO strings
      if (data.createdAt && typeof data.createdAt.toDate === 'function') {
        appointment.createdAt = data.createdAt.toDate().toISOString();
      }
      if (data.updatedAt && typeof data.updatedAt.toDate === 'function') {
        appointment.updatedAt = data.updatedAt.toDate().toISOString();
      }

      return appointment;
    } catch (error) {
      console.error('Error getting appointment by ID from Firebase:', error);
      return null;
    }
  },

  /**
   * Get appointments by status
   * @param {string} userId - The user ID
   * @param {string} userRole - The user's role
   * @param {string} status - The appointment status
   * @returns {Promise<Array>} - Array of appointments with the specified status
   */
  getAppointmentsByStatus: async (userId, userRole, status) => {
    try {
      const appointments = await firebaseAppointmentsService.getUserAppointments(userId, userRole);
      return appointments.filter(appointment => appointment.status === status);
    } catch (error) {
      console.error('Error getting appointments by status:', error);
      return [];
    }
  },

  /**
   * Cancel an appointment
   * @param {string} appointmentId - The appointment ID to cancel
   * @param {string} reason - The cancellation reason
   * @param {string} cancelledBy - The user ID who cancelled the appointment
   * @returns {Promise<Object>} - The updated appointment
   */
  cancelAppointment: async (appointmentId, reason, cancelledBy) => {
    try {
      const updateData = {
        status: 'cancelled',
        cancellationReason: reason || 'Cancelled by user',
        cancelledBy: cancelledBy,
        cancelledAt: serverTimestamp()
      };

      return await firebaseAppointmentsService.updateAppointment(appointmentId, updateData);
    } catch (error) {
      console.error('Error cancelling appointment:', error);
      throw new Error('Failed to cancel appointment');
    }
  },

  /**
   * Reschedule an appointment
   * @param {string} appointmentId - The appointment ID to reschedule
   * @param {string} newDate - The new appointment date
   * @param {string} newTime - The new appointment time
   * @param {string} rescheduledBy - The user ID who rescheduled the appointment
   * @returns {Promise<Object>} - The updated appointment
   */
  rescheduleAppointment: async (appointmentId, newDate, newTime, rescheduledBy) => {
    try {
      // Get current appointment data to store previous date/time
      const currentAppointment = await firebaseAppointmentsService.getAppointmentById(appointmentId);
      
      if (!currentAppointment) {
        throw new Error('Appointment not found');
      }

      const updateData = {
        previousDate: currentAppointment.date,
        previousTime: currentAppointment.time,
        date: newDate,
        time: newTime,
        status: 'rescheduled',
        rescheduledBy: rescheduledBy,
        rescheduledAt: serverTimestamp()
      };

      return await firebaseAppointmentsService.updateAppointment(appointmentId, updateData);
    } catch (error) {
      console.error('Error rescheduling appointment:', error);
      throw new Error('Failed to reschedule appointment');
    }
  },

  /**
   * Get appointments for a specific date range
   * @param {string} userId - The user ID
   * @param {string} userRole - The user's role
   * @param {string} startDate - The start date (YYYY-MM-DD)
   * @param {string} endDate - The end date (YYYY-MM-DD)
   * @returns {Promise<Array>} - Array of appointments in the date range
   */
  getAppointmentsByDateRange: async (userId, userRole, startDate, endDate) => {
    try {
      const appointments = await firebaseAppointmentsService.getUserAppointments(userId, userRole);
      
      return appointments.filter(appointment => {
        const appointmentDate = appointment.date;
        return appointmentDate >= startDate && appointmentDate <= endDate;
      }).sort((a, b) => {
        const dateA = new Date(`${a.date}T${a.time}`);
        const dateB = new Date(`${b.date}T${b.time}`);
        return dateA - dateB;
      });
    } catch (error) {
      console.error('Error getting appointments by date range:', error);
      return [];
    }
  }
};

export default firebaseAppointmentsService;
