import { 
  collection, 
  query, 
  where, 
  getDocs, 
  addDoc, 
  updateDoc, 
  doc, 
  getDoc, 
  deleteDoc, 
  serverTimestamp, 
  arrayUnion,
  arrayRemove 
} from 'firebase/firestore';
import { db } from '../config/firebase';

/**
 * Service for managing caregiver relationships and activities for web application
 */
export const firebaseCaregiverService = {
  /**
   * Assign a caregiver to a patient (supervisor only)
   * @param {string} patientId - The ID of the patient
   * @param {string} caregiverId - The ID of the caregiver
   * @param {string} supervisorId - The ID of the supervisor making the assignment
   * @returns {Promise<Object>} - The result of the assignment
   */
  assignCaregiverToPatient: async (patientId, caregiverId, supervisorId) => {
    try {
      if (!patientId || !caregiverId || !supervisorId) {
        throw new Error('Patient ID, caregiver ID, and supervisor ID are required');
      }

      // Update patient document to add caregiver
      const patientRef = doc(db, 'users', patientId);
      await updateDoc(patientRef, {
        assignedCaregivers: arrayUnion(caregiverId),
        updatedAt: serverTimestamp()
      });

      // Update caregiver document to add patient
      const caregiverRef = doc(db, 'users', caregiverId);
      await updateDoc(caregiverRef, {
        assignedPatients: arrayUnion(patientId),
        updatedAt: serverTimestamp()
      });

      // Create assignment record
      const assignmentRecord = {
        patientId,
        caregiverId,
        supervisorId,
        assignedAt: serverTimestamp(),
        status: 'active'
      };

      const assignmentsCollection = collection(db, 'caregiverAssignments');
      const docRef = await addDoc(assignmentsCollection, assignmentRecord);

      return {
        id: docRef.id,
        ...assignmentRecord,
        assignedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error assigning caregiver to patient:', error);
      throw new Error('Failed to assign caregiver to patient');
    }
  },

  /**
   * Get all available caregivers
   * @returns {Promise<Array>} - List of available caregivers
   */
  getAvailableCaregivers: async () => {
    try {
      const usersCollection = collection(db, 'users');
      const q = query(usersCollection, where('role', '==', 'caregiver'));
      
      const querySnapshot = await getDocs(q);
      const caregivers = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        caregivers.push({
          id: doc.id,
          ...data,
          // Don't expose sensitive information
          password: undefined,
          email: data.email || 'No email provided'
        });
      });

      return caregivers;
    } catch (error) {
      console.error('Error getting available caregivers:', error);
      throw new Error('Failed to get available caregivers');
    }
  },

  /**
   * Get caregivers assigned to a patient
   * @param {string} patientId - The ID of the patient
   * @returns {Promise<Array>} - List of assigned caregivers
   */
  getPatientCaregivers: async (patientId) => {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      // Get patient document to find assigned caregivers
      const patientRef = doc(db, 'users', patientId);
      const patientSnap = await getDoc(patientRef);

      if (!patientSnap.exists()) {
        throw new Error('Patient not found');
      }

      const patientData = patientSnap.data();
      const assignedCaregiverIds = patientData.assignedCaregivers || [];

      if (assignedCaregiverIds.length === 0) {
        return [];
      }

      // Get caregiver details
      const caregivers = [];
      for (const caregiverId of assignedCaregiverIds) {
        const caregiverRef = doc(db, 'users', caregiverId);
        const caregiverSnap = await getDoc(caregiverRef);
        
        if (caregiverSnap.exists()) {
          const caregiverData = caregiverSnap.data();
          caregivers.push({
            id: caregiverId,
            ...caregiverData,
            password: undefined // Don't expose password
          });
        }
      }

      return caregivers;
    } catch (error) {
      console.error('Error getting patient caregivers:', error);
      throw new Error('Failed to get patient caregivers');
    }
  },

  /**
   * Get patients assigned to a caregiver
   * @param {string} caregiverId - The ID of the caregiver
   * @returns {Promise<Array>} - List of assigned patients
   */
  getCaregiverPatients: async (caregiverId) => {
    try {
      if (!caregiverId) {
        throw new Error('Caregiver ID is required');
      }

      // Get caregiver document to find assigned patients
      const caregiverRef = doc(db, 'users', caregiverId);
      const caregiverSnap = await getDoc(caregiverRef);

      if (!caregiverSnap.exists()) {
        throw new Error('Caregiver not found');
      }

      const caregiverData = caregiverSnap.data();
      const assignedPatientIds = caregiverData.assignedPatients || [];

      if (assignedPatientIds.length === 0) {
        return [];
      }

      // Get patient details
      const patients = [];
      for (const patientId of assignedPatientIds) {
        const patientRef = doc(db, 'users', patientId);
        const patientSnap = await getDoc(patientRef);
        
        if (patientSnap.exists()) {
          const patientData = patientSnap.data();
          patients.push({
            id: patientId,
            ...patientData,
            password: undefined // Don't expose password
          });
        }
      }

      return patients;
    } catch (error) {
      console.error('Error getting caregiver patients:', error);
      throw new Error('Failed to get caregiver patients');
    }
  },

  /**
   * Remove a caregiver assignment
   * @param {string} patientId - The ID of the patient
   * @param {string} caregiverId - The ID of the caregiver
   * @returns {Promise<boolean>} - Success status
   */
  removeCaregiverAssignment: async (patientId, caregiverId) => {
    try {
      if (!patientId || !caregiverId) {
        throw new Error('Patient ID and caregiver ID are required');
      }

      // Update patient document to remove caregiver
      const patientRef = doc(db, 'users', patientId);
      await updateDoc(patientRef, {
        assignedCaregivers: arrayRemove(caregiverId),
        updatedAt: serverTimestamp()
      });

      // Update caregiver document to remove patient
      const caregiverRef = doc(db, 'users', caregiverId);
      await updateDoc(caregiverRef, {
        assignedPatients: arrayRemove(patientId),
        updatedAt: serverTimestamp()
      });

      // Update assignment record status
      const assignmentsCollection = collection(db, 'caregiverAssignments');
      const q = query(
        assignmentsCollection,
        where('patientId', '==', patientId),
        where('caregiverId', '==', caregiverId),
        where('status', '==', 'active')
      );

      const querySnapshot = await getDocs(q);
      querySnapshot.forEach(async (docSnapshot) => {
        await updateDoc(docSnapshot.ref, {
          status: 'removed',
          removedAt: serverTimestamp()
        });
      });

      return true;
    } catch (error) {
      console.error('Error removing caregiver assignment:', error);
      throw new Error('Failed to remove caregiver assignment');
    }
  },

  /**
   * Get patient vitals from Firebase
   * @param {string} patientId - The patient ID
   * @param {string} vitalType - Optional vital type to filter by
   * @returns {Promise<Array>} - Array of vital records
   */
  getPatientVitals: async (patientId, vitalType = null) => {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      // Query Firestore for vitals
      const vitalsCollection = collection(db, 'vitals');
      let q;

      if (vitalType) {
        q = query(
          vitalsCollection,
          where('userId', '==', patientId),
          where('vitalType', '==', vitalType)
        );
      } else {
        q = query(
          vitalsCollection,
          where('userId', '==', patientId)
        );
      }

      const querySnapshot = await getDocs(q);
      const vitals = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        vitals.push({
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate?.().toISOString() || new Date().toISOString()
        });
      });

      // Sort by timestamp, newest first
      return vitals.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    } catch (error) {
      console.error('Error getting patient vitals:', error);
      throw new Error('Failed to get patient vitals');
    }
  },

  /**
   * Record a new vital sign for a patient
   * @param {string} patientId - The patient ID
   * @param {string} vitalType - The type of vital (heartRate, bloodPressure, etc.)
   * @param {Object} values - The vital values
   * @param {string} notes - Optional notes about the vital
   * @param {string} recordedBy - The ID of the caregiver recording the vital
   * @returns {Promise<Object>} - The newly created vital record
   */
  recordPatientVital: async (patientId, vitalType, values, notes = '', recordedBy) => {
    try {
      if (!patientId || !vitalType || !values || !recordedBy) {
        throw new Error('Patient ID, vital type, values, and recorded by are required');
      }

      // Create the vital record
      const vitalRecord = {
        userId: patientId,
        vitalType,
        values,
        notes,
        recordMethod: 'manual',
        recordType: 'caregiver',
        recordedBy,
        timestamp: serverTimestamp()
      };

      // Add to Firestore
      const vitalsCollection = collection(db, 'vitals');
      const docRef = await addDoc(vitalsCollection, vitalRecord);

      // Return the newly created record
      return {
        id: docRef.id,
        ...vitalRecord,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error recording patient vital:', error);
      throw new Error('Failed to record patient vital');
    }
  },

  /**
   * Record a new activity for a patient
   * @param {string} patientId - The patient ID
   * @param {string} activityType - The type of activity
   * @param {string} description - Description of the activity
   * @param {number} duration - Duration of the activity in minutes
   * @param {boolean} completed - Whether the activity was completed
   * @param {string} notes - Optional notes about the activity
   * @param {string} recordedBy - The ID of the caregiver recording the activity
   * @returns {Promise<Object>} - The newly created activity record
   */
  recordPatientActivity: async (patientId, activityType, description, duration = 0, completed = true, notes = '', recordedBy) => {
    try {
      if (!patientId || !activityType || !description || !recordedBy) {
        throw new Error('Patient ID, activity type, description, and recorded by are required');
      }

      // Create the activity record
      const activityRecord = {
        userId: patientId,
        type: activityType,
        description,
        duration,
        completed,
        notes,
        recordedBy,
        timestamp: serverTimestamp()
      };

      // Add to Firestore
      const activitiesCollection = collection(db, 'activities');
      const docRef = await addDoc(activitiesCollection, activityRecord);

      // Return the newly created record
      return {
        id: docRef.id,
        ...activityRecord,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error recording patient activity:', error);
      throw new Error('Failed to record patient activity');
    }
  },

  /**
   * Get patient activities from Firebase
   * @param {string} patientId - The patient ID
   * @param {string} activityType - Optional activity type to filter by
   * @param {number} limit - Optional limit for the number of records to return
   * @returns {Promise<Array>} - Array of activity records
   */
  getPatientActivities: async (patientId, activityType = null, limit = 50) => {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      // Query Firestore for activities
      const activitiesCollection = collection(db, 'activities');
      let q;

      if (activityType) {
        q = query(
          activitiesCollection,
          where('userId', '==', patientId),
          where('type', '==', activityType)
        );
      } else {
        q = query(
          activitiesCollection,
          where('userId', '==', patientId)
        );
      }

      const querySnapshot = await getDocs(q);
      const activities = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        activities.push({
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate?.().toISOString() || new Date().toISOString()
        });
      });

      // Sort by timestamp, newest first
      return activities
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, limit);
    } catch (error) {
      console.error('Error getting patient activities:', error);
      throw new Error('Failed to get patient activities');
    }
  }
};

export default firebaseCaregiverService;
