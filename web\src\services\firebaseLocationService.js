import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc,
  query, 
  where, 
  orderBy, 
  limit,
  onSnapshot,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { GEO_CONSTANTS } from '../config/constants';

/**
 * Service for managing location data in Firebase for web application
 */
export class FirebaseLocationService {
  constructor() {
    this.watchId = null;
    this.activeListeners = new Map();
  }

  /**
   * Request location permissions from the browser
   * @returns {Promise<boolean>} Whether permission was granted
   */
  async requestLocationPermissions() {
    try {
      if (!navigator.geolocation) {
        console.error('Geolocation is not supported by this browser');
        return false;
      }

      // Check current permission state
      if (navigator.permissions) {
        const permission = await navigator.permissions.query({ name: 'geolocation' });
        return permission.state === 'granted';
      }

      // Fallback: try to get position to trigger permission request
      return new Promise((resolve) => {
        navigator.geolocation.getCurrentPosition(
          () => resolve(true),
          () => resolve(false),
          { timeout: 5000 }
        );
      });
    } catch (error) {
      console.error('Error requesting location permissions:', error);
      return false;
    }
  }

  /**
   * Get the current location of the device
   * @param {Object} options - Geolocation options
   * @returns {Promise<Object|null>} The current location or null if unavailable
   */
  async getCurrentLocation(options = {}) {
    try {
      const hasPermission = await this.requestLocationPermissions();
      if (!hasPermission) {
        throw new Error('Location permission denied');
      }

      const defaultOptions = {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000,
        ...options
      };

      return new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            resolve({
              coords: {
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
                accuracy: position.coords.accuracy,
                altitude: position.coords.altitude,
                altitudeAccuracy: position.coords.altitudeAccuracy,
                heading: position.coords.heading,
                speed: position.coords.speed
              },
              timestamp: position.timestamp
            });
          },
          (error) => {
            console.error('Error getting current location:', error);
            reject(error);
          },
          defaultOptions
        );
      });
    } catch (error) {
      console.error('Error getting current location:', error);
      return null;
    }
  }

  /**
   * Save a patient's location to Firebase
   * @param {Object} locationData - The location data to save
   * @param {string} userId - User ID (optional, uses current user if not provided)
   * @returns {Promise<Object>} The saved location data with ID
   */
  async savePatientLocation(locationData, userId = null) {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }

      const locationToSave = {
        ...locationData,
        userId: userId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        type: 'manual', // manual, automatic, emergency
        accuracy: locationData.accuracy || null,
        source: 'web_browser'
      };

      const locationsCollection = collection(db, 'patientLocations');
      const docRef = await addDoc(locationsCollection, locationToSave);

      return {
        id: docRef.id,
        ...locationToSave
      };
    } catch (error) {
      console.error('Error saving location to Firebase:', error);
      throw new Error('Failed to save location: ' + error.message);
    }
  }

  /**
   * Start sharing location with a supervisor
   * @param {string} patientId - The patient's user ID
   * @param {string} supervisorId - The supervisor's user ID
   * @returns {Promise<Object>} The location sharing session
   */
  async startLocationSharing(patientId, supervisorId) {
    try {
      // Get current location
      const location = await this.getCurrentLocation();
      if (!location) {
        throw new Error('Could not get current location');
      }

      const sharingData = {
        patientId,
        supervisorId,
        status: 'active',
        startedAt: serverTimestamp(),
        lastUpdated: serverTimestamp(),
        lastLocation: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          accuracy: location.coords.accuracy,
          altitude: location.coords.altitude,
          heading: location.coords.heading,
          speed: location.coords.speed,
          timestamp: new Date(location.timestamp).toISOString(),
        },
        settings: {
          updateInterval: GEO_CONSTANTS.LOCATION_UPDATE_INTERVAL,
          highAccuracy: true,
          shareMovement: true,
          shareSpeed: true
        }
      };

      const sharingCollection = collection(db, 'locationSharing');
      const docRef = await addDoc(sharingCollection, sharingData);

      return {
        id: docRef.id,
        ...sharingData
      };
    } catch (error) {
      console.error('Error starting location sharing:', error);
      throw new Error('Failed to start location sharing: ' + error.message);
    }
  }

  /**
   * Update location sharing with new position
   * @param {string} sharingId - The location sharing session ID
   * @param {Object} location - New location data
   * @returns {Promise<boolean>} Whether the operation was successful
   */
  async updateLocationSharing(sharingId, location) {
    try {
      const sharingRef = doc(db, 'locationSharing', sharingId);
      
      await updateDoc(sharingRef, {
        lastLocation: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          accuracy: location.coords.accuracy,
          altitude: location.coords.altitude,
          heading: location.coords.heading,
          speed: location.coords.speed,
          timestamp: new Date(location.timestamp).toISOString(),
        },
        lastUpdated: serverTimestamp()
      });

      return true;
    } catch (error) {
      console.error('Error updating location sharing:', error);
      return false;
    }
  }

  /**
   * Stop sharing location with a supervisor
   * @param {string} sharingId - The location sharing session ID
   * @param {string} userId - User ID stopping the sharing
   * @returns {Promise<boolean>} Whether the operation was successful
   */
  async stopLocationSharing(sharingId, userId) {
    try {
      const sharingRef = doc(db, 'locationSharing', sharingId);
      const sharingDoc = await getDoc(sharingRef);

      if (!sharingDoc.exists()) {
        throw new Error('Location sharing session not found');
      }

      const sharingData = sharingDoc.data();
      if (sharingData.patientId !== userId && sharingData.supervisorId !== userId) {
        throw new Error('Not authorized to stop this location sharing session');
      }

      await updateDoc(sharingRef, {
        status: 'stopped',
        stoppedAt: serverTimestamp(),
        stoppedBy: userId
      });

      return true;
    } catch (error) {
      console.error('Error stopping location sharing:', error);
      return false;
    }
  }

  /**
   * Get active location sharing sessions for a supervisor
   * @param {string} supervisorId - Supervisor's user ID
   * @returns {Promise<Array>} Array of active location sharing sessions
   */
  async getSupervisorActiveSessions(supervisorId) {
    try {
      const sharingCollection = collection(db, 'locationSharing');
      const q = query(
        sharingCollection,
        where('supervisorId', '==', supervisorId),
        where('status', '==', 'active'),
        orderBy('startedAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const sessions = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        sessions.push({
          id: doc.id,
          ...data
        });
      });

      return sessions;
    } catch (error) {
      console.error('Error getting supervisor active sessions:', error);
      return [];
    }
  }

  /**
   * Get a patient's recent locations
   * @param {string} patientId - The patient's user ID
   * @param {number} maxResults - Maximum number of locations to retrieve
   * @returns {Promise<Array>} Array of location records
   */
  async getPatientRecentLocations(patientId, maxResults = 10) {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      const locationsCollection = collection(db, 'patientLocations');
      const q = query(
        locationsCollection,
        where('userId', '==', patientId),
        orderBy('createdAt', 'desc'),
        limit(maxResults)
      );

      const querySnapshot = await getDocs(q);
      const locations = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        locations.push({
          id: doc.id,
          ...data
        });
      });

      return locations;
    } catch (error) {
      console.error('Error getting patient locations:', error);
      return [];
    }
  }

  /**
   * Start watching position changes
   * @param {Function} callback - Callback function for position updates
   * @param {Object} options - Watch options
   * @returns {number} Watch ID
   */
  startWatchingPosition(callback, options = {}) {
    try {
      const defaultOptions = {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 30000,
        ...options
      };

      this.watchId = navigator.geolocation.watchPosition(
        (position) => {
          const locationData = {
            coords: {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
              accuracy: position.coords.accuracy,
              altitude: position.coords.altitude,
              altitudeAccuracy: position.coords.altitudeAccuracy,
              heading: position.coords.heading,
              speed: position.coords.speed
            },
            timestamp: position.timestamp
          };
          callback(locationData);
        },
        (error) => {
          console.error('Error watching position:', error);
          callback(null, error);
        },
        defaultOptions
      );

      return this.watchId;
    } catch (error) {
      console.error('Error starting position watch:', error);
      return null;
    }
  }

  /**
   * Stop watching position changes
   */
  stopWatchingPosition() {
    if (this.watchId !== null) {
      navigator.geolocation.clearWatch(this.watchId);
      this.watchId = null;
    }
  }

  /**
   * Listen to location sharing updates in real-time
   * @param {string} sharingId - Sharing session ID
   * @param {Function} callback - Callback function for updates
   * @returns {Function} Unsubscribe function
   */
  listenToLocationSharing(sharingId, callback) {
    try {
      const sharingRef = doc(db, 'locationSharing', sharingId);
      
      const unsubscribe = onSnapshot(sharingRef, (doc) => {
        if (doc.exists()) {
          callback({
            id: doc.id,
            ...doc.data()
          });
        } else {
          callback(null);
        }
      }, (error) => {
        console.error('Error listening to location sharing:', error);
        callback(null, error);
      });

      this.activeListeners.set(sharingId, unsubscribe);
      return unsubscribe;
    } catch (error) {
      console.error('Error setting up location sharing listener:', error);
      return () => {};
    }
  }

  /**
   * Stop listening to location sharing updates
   * @param {string} sharingId - Sharing session ID
   */
  stopListeningToLocationSharing(sharingId) {
    const unsubscribe = this.activeListeners.get(sharingId);
    if (unsubscribe) {
      unsubscribe();
      this.activeListeners.delete(sharingId);
    }
  }

  /**
   * Calculate distance between two coordinates
   * @param {number} lat1 - Latitude of first point
   * @param {number} lon1 - Longitude of first point
   * @param {number} lat2 - Latitude of second point
   * @param {number} lon2 - Longitude of second point
   * @returns {number} Distance in meters
   */
  calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  }

  /**
   * Clean up all active listeners and watchers
   */
  cleanup() {
    this.stopWatchingPosition();
    this.activeListeners.forEach((unsubscribe) => {
      unsubscribe();
    });
    this.activeListeners.clear();
  }
}

// Create and export a singleton instance
export const firebaseLocationService = new FirebaseLocationService();
export default firebaseLocationService;
