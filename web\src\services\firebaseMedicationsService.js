import {
  collection,
  doc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../config/firebase';

/**
 * Service for managing medications in Firebase for web application
 */
export const firebaseMedicationsService = {
  /**
   * Save a new medication to Firebase
   * @param {Object} medicationData - The medication data to save
   * @param {string} patientId - The patient's user ID
   * @returns {Promise<Object>} - The saved medication record with ID
   */
  saveMedication: async (medicationData, patientId) => {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      // Create medication data with patient ID
      const medicationToSave = {
        ...medicationData,
        patientId: patientId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      // Add to Firestore
      const medicationsCollection = collection(db, 'medications');
      const docRef = await addDoc(medicationsCollection, medicationToSave);

      // Get the newly created document to return with server timestamp
      const newDoc = await getDoc(docRef);
      const newDocData = newDoc.data();

      // Handle the case where timestamp might be a server timestamp object
      const result = {
        id: docRef.id,
        ...newDocData,
      };

      // Ensure timestamps are strings if they're not already
      if (newDocData.createdAt && typeof newDocData.createdAt.toDate === 'function') {
        result.createdAt = newDocData.createdAt.toDate().toISOString();
      }
      if (newDocData.updatedAt && typeof newDocData.updatedAt.toDate === 'function') {
        result.updatedAt = newDocData.updatedAt.toDate().toISOString();
      }

      return result;
    } catch (error) {
      console.error('Error saving medication to Firebase:', error);
      throw new Error('Failed to save medication');
    }
  },

  /**
   * Get all medications for a patient
   * @param {string} patientId - The patient ID
   * @returns {Promise<Array>} - Array of medication records
   */
  getPatientMedications: async (patientId) => {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      // Query Firestore for medications
      const medicationsCollection = collection(db, 'medications');

      try {
        // Try with ordering (requires index)
        const q = query(
          medicationsCollection,
          where('patientId', '==', patientId),
          orderBy('createdAt', 'desc')
        );

        const querySnapshot = await getDocs(q);

        // Convert to array of medications
        const medications = [];
        querySnapshot.forEach((doc) => {
          const data = doc.data();
          const medication = {
            id: doc.id,
            ...data
          };

          // Convert timestamps to ISO strings
          if (data.createdAt && typeof data.createdAt.toDate === 'function') {
            medication.createdAt = data.createdAt.toDate().toISOString();
          }
          if (data.updatedAt && typeof data.updatedAt.toDate === 'function') {
            medication.updatedAt = data.updatedAt.toDate().toISOString();
          }

          medications.push(medication);
        });

        return medications;
      } catch (indexError) {
        // If index error, fall back to simpler query without ordering
        if (indexError.toString().includes('requires an index')) {
          console.warn('Index not found, falling back to simple query');

          const simpleQuery = query(
            medicationsCollection,
            where('patientId', '==', patientId)
          );

          const simpleSnapshot = await getDocs(simpleQuery);

          // Convert to array of medications and sort manually
          const medications = [];
          simpleSnapshot.forEach((doc) => {
            const data = doc.data();
            const medication = {
              id: doc.id,
              ...data
            };

            // Convert timestamps to ISO strings
            if (data.createdAt && typeof data.createdAt.toDate === 'function') {
              medication.createdAt = data.createdAt.toDate().toISOString();
            }
            if (data.updatedAt && typeof data.updatedAt.toDate === 'function') {
              medication.updatedAt = data.updatedAt.toDate().toISOString();
            }

            medications.push(medication);
          });

          // Sort manually by createdAt if available
          return medications.sort((a, b) => {
            if (!a.createdAt || !b.createdAt) return 0;
            const dateA = new Date(a.createdAt);
            const dateB = new Date(b.createdAt);
            return dateB - dateA; // descending order
          });
        } else {
          // If not an index error, rethrow
          throw indexError;
        }
      }
    } catch (error) {
      console.error('Error getting medications from Firebase:', error);
      return [];
    }
  },

  /**
   * Get a specific medication by ID
   * @param {string} medicationId - The medication ID
   * @returns {Promise<Object|null>} - The medication or null if not found
   */
  getMedicationById: async (medicationId) => {
    try {
      if (!medicationId) {
        throw new Error('Medication ID is required');
      }

      const medicationRef = doc(db, 'medications', medicationId);
      const medicationDoc = await getDoc(medicationRef);

      if (medicationDoc.exists()) {
        const data = medicationDoc.data();
        const medication = {
          id: medicationDoc.id,
          ...data
        };

        // Convert timestamps to ISO strings
        if (data.createdAt && typeof data.createdAt.toDate === 'function') {
          medication.createdAt = data.createdAt.toDate().toISOString();
        }
        if (data.updatedAt && typeof data.updatedAt.toDate === 'function') {
          medication.updatedAt = data.updatedAt.toDate().toISOString();
        }

        return medication;
      }

      return null;
    } catch (error) {
      console.error('Error getting medication by ID from Firebase:', error);
      return null;
    }
  },

  /**
   * Update a medication
   * @param {string} medicationId - The medication ID to update
   * @param {Object} updateData - The data to update
   * @returns {Promise<Object|null>} - The updated medication or null if not found
   */
  updateMedication: async (medicationId, updateData) => {
    try {
      if (!medicationId) {
        throw new Error('Medication ID is required');
      }

      const medicationRef = doc(db, 'medications', medicationId);
      const medicationDoc = await getDoc(medicationRef);

      if (!medicationDoc.exists()) {
        return null;
      }

      // Add updated timestamp
      const dataToUpdate = {
        ...updateData,
        updatedAt: serverTimestamp()
      };

      await updateDoc(medicationRef, dataToUpdate);

      // Get the updated document
      const updatedDoc = await getDoc(medicationRef);
      const updatedData = updatedDoc.data();

      // Handle the case where timestamp might be a server timestamp object
      const result = {
        id: medicationId,
        ...updatedData,
      };

      // Ensure updatedAt is a string if it's not already
      if (updatedData.updatedAt && typeof updatedData.updatedAt.toDate === 'function') {
        result.updatedAt = updatedData.updatedAt.toDate().toISOString();
      }
      if (updatedData.createdAt && typeof updatedData.createdAt.toDate === 'function') {
        result.createdAt = updatedData.createdAt.toDate().toISOString();
      }

      return result;
    } catch (error) {
      console.error('Error updating medication in Firebase:', error);
      return null;
    }
  },

  /**
   * Delete a medication
   * @param {string} medicationId - The medication ID to delete
   * @returns {Promise<boolean>} - True if deleted, false if not found
   */
  deleteMedication: async (medicationId) => {
    try {
      if (!medicationId) {
        throw new Error('Medication ID is required');
      }

      const medicationRef = doc(db, 'medications', medicationId);
      const medicationDoc = await getDoc(medicationRef);

      if (!medicationDoc.exists()) {
        return false;
      }

      await deleteDoc(medicationRef);
      return true;
    } catch (error) {
      console.error('Error deleting medication from Firebase:', error);
      return false;
    }
  },

  /**
   * Save a medication reminder
   * @param {Object} reminderData - The reminder data to save
   * @param {string} patientId - The patient's user ID
   * @returns {Promise<Object>} - The saved reminder with ID
   */
  saveReminder: async (reminderData, patientId) => {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      // Create reminder data
      const reminderToSave = {
        ...reminderData,
        patientId: patientId,
        status: reminderData.status || 'scheduled', // scheduled, completed, missed
        createdAt: serverTimestamp()
      };

      // Add to Firestore
      const remindersCollection = collection(db, 'medicationReminders');
      const docRef = await addDoc(remindersCollection, reminderToSave);

      // Get the newly created document to return with server timestamp
      const newDoc = await getDoc(docRef);
      const newDocData = newDoc.data();

      // Handle the case where timestamp might be a server timestamp object
      const result = {
        id: docRef.id,
        ...newDocData,
      };

      // Ensure createdAt is a string if it's not already
      if (newDocData.createdAt && typeof newDocData.createdAt.toDate === 'function') {
        result.createdAt = newDocData.createdAt.toDate().toISOString();
      }

      return result;
    } catch (error) {
      console.error('Error saving medication reminder to Firebase:', error);
      throw new Error('Failed to save medication reminder');
    }
  },

  /**
   * Get all medication reminders for a patient
   * @param {string} patientId - The patient ID
   * @returns {Promise<Array>} - Array of medication reminders
   */
  getPatientReminders: async (patientId) => {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      // Query Firestore for reminders
      const remindersCollection = collection(db, 'medicationReminders');

      try {
        // Try with ordering (requires index)
        const q = query(
          remindersCollection,
          where('patientId', '==', patientId),
          orderBy('scheduledTime', 'asc')
        );

        const querySnapshot = await getDocs(q);

        // Convert to array of reminders
        const reminders = [];
        querySnapshot.forEach((doc) => {
          const data = doc.data();
          const reminder = {
            id: doc.id,
            ...data
          };

          // Convert timestamps to ISO strings
          if (data.createdAt && typeof data.createdAt.toDate === 'function') {
            reminder.createdAt = data.createdAt.toDate().toISOString();
          }
          if (data.scheduledTime && typeof data.scheduledTime.toDate === 'function') {
            reminder.scheduledTime = data.scheduledTime.toDate().toISOString();
          }

          reminders.push(reminder);
        });

        return reminders;
      } catch (indexError) {
        // If index error, fall back to simpler query without ordering
        if (indexError.toString().includes('requires an index')) {
          console.warn('Index not found, falling back to simple query');

          const simpleQuery = query(
            remindersCollection,
            where('patientId', '==', patientId)
          );

          const simpleSnapshot = await getDocs(simpleQuery);

          // Convert to array of reminders and sort manually
          const reminders = [];
          simpleSnapshot.forEach((doc) => {
            const data = doc.data();
            const reminder = {
              id: doc.id,
              ...data
            };

            // Convert timestamps to ISO strings
            if (data.createdAt && typeof data.createdAt.toDate === 'function') {
              reminder.createdAt = data.createdAt.toDate().toISOString();
            }
            if (data.scheduledTime && typeof data.scheduledTime.toDate === 'function') {
              reminder.scheduledTime = data.scheduledTime.toDate().toISOString();
            }

            reminders.push(reminder);
          });

          // Sort manually by scheduledTime if available
          return reminders.sort((a, b) => {
            if (!a.scheduledTime || !b.scheduledTime) return 0;
            const dateA = new Date(a.scheduledTime);
            const dateB = new Date(b.scheduledTime);
            return dateA - dateB; // ascending order
          });
        } else {
          // If not an index error, rethrow
          throw indexError;
        }
      }
    } catch (error) {
      console.error('Error getting medication reminders from Firebase:', error);
      return [];
    }
  },

  /**
   * Update a medication reminder status
   * @param {string} reminderId - The reminder ID to update
   * @param {string} status - The new status (scheduled, completed, missed)
   * @returns {Promise<Object|null>} - The updated reminder or null if not found
   */
  updateReminderStatus: async (reminderId, status) => {
    try {
      if (!reminderId) {
        throw new Error('Reminder ID is required');
      }

      const reminderRef = doc(db, 'medicationReminders', reminderId);
      const reminderDoc = await getDoc(reminderRef);

      if (!reminderDoc.exists()) {
        return null;
      }

      // Update the status
      const updateData = {
        status,
        updatedAt: serverTimestamp()
      };

      await updateDoc(reminderRef, updateData);

      // Get the updated document
      const updatedDoc = await getDoc(reminderRef);
      const updatedData = updatedDoc.data();

      // Handle the case where timestamp might be a server timestamp object
      const result = {
        id: reminderId,
        ...updatedData,
      };

      // Ensure updatedAt is a string if it's not already
      if (updatedData.updatedAt && typeof updatedData.updatedAt.toDate === 'function') {
        result.updatedAt = updatedData.updatedAt.toDate().toISOString();
      }
      if (updatedData.createdAt && typeof updatedData.createdAt.toDate === 'function') {
        result.createdAt = updatedData.createdAt.toDate().toISOString();
      }

      return result;
    } catch (error) {
      console.error('Error updating medication reminder status in Firebase:', error);
      return null;
    }
  }
};

export default firebaseMedicationsService;
