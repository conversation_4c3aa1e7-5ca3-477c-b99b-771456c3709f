import { 
  collection,
  doc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '../config/firebase';

/**
 * Service for managing prescriptions in Firebase for web application
 */
export const firebasePrescriptionsService = {
  /**
   * Save a new prescription to Firebase
   * @param {Object} prescriptionData - The prescription data to save
   * @param {string} doctorId - The doctor's user ID
   * @returns {Promise<Object>} - The saved prescription record with ID
   */
  savePrescription: async (prescriptionData, doctorId) => {
    try {
      if (!doctorId) {
        throw new Error('Doctor ID is required');
      }

      // Create prescription data with doctor ID
      const prescriptionToSave = {
        ...prescriptionData,
        doctorId: doctorId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        status: prescriptionData.status || 'pending' // pending, sent, filled
      };

      // Add to Firestore
      const prescriptionsCollection = collection(db, 'prescriptions');

      try {
        const docRef = await addDoc(prescriptionsCollection, prescriptionToSave);

        // Get the newly created document to return with server timestamp
        const newDoc = await getDoc(docRef);
        const newDocData = newDoc.data();

        // Handle the case where timestamp might be a server timestamp object
        const result = {
          id: docRef.id,
          ...newDocData,
        };

        // Ensure timestamps are strings if they're not already
        if (newDocData.createdAt && typeof newDocData.createdAt.toDate === 'function') {
          result.createdAt = newDocData.createdAt.toDate().toISOString();
        }
        if (newDocData.updatedAt && typeof newDocData.updatedAt.toDate === 'function') {
          result.updatedAt = newDocData.updatedAt.toDate().toISOString();
        }

        return result;
      } catch (firestoreError) {
        // Check if it's a permission error
        if (firestoreError.code === 'permission-denied') {
          console.error('Firebase permission denied. Please update Firestore rules to allow access to prescriptions collection.');

          // Create a local ID for the prescription
          const localId = `local-${Date.now()}`;

          // Return a local version of the prescription with the warning
          return {
            id: localId,
            ...prescriptionToSave,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            _isLocalOnly: true,
            _permissionError: true
          };
        }

        // Re-throw other errors
        throw firestoreError;
      }
    } catch (error) {
      console.error('Error saving prescription to Firebase:', error);
      throw new Error(`Failed to save prescription: ${error.message}`);
    }
  },

  /**
   * Get all prescriptions created by a doctor
   * @param {string} doctorId - The doctor ID
   * @returns {Promise<Array>} - Array of prescription records
   */
  getDoctorPrescriptions: async (doctorId) => {
    try {
      if (!doctorId) {
        throw new Error('Doctor ID is required');
      }

      try {
        // Query prescriptions collection
        const prescriptionsCollection = collection(db, 'prescriptions');

        try {
          // Try with ordering (requires index)
          const prescriptionsQuery = query(
            prescriptionsCollection,
            where('doctorId', '==', doctorId),
            orderBy('createdAt', 'desc')
          );

          const querySnapshot = await getDocs(prescriptionsQuery);

          // Format prescription data
          const prescriptions = [];
          querySnapshot.forEach((doc) => {
            const data = doc.data();
            const prescription = {
              id: doc.id,
              ...data,
            };

            // Convert timestamps to ISO strings
            if (data.createdAt && typeof data.createdAt.toDate === 'function') {
              prescription.createdAt = data.createdAt.toDate().toISOString();
            }
            if (data.updatedAt && typeof data.updatedAt.toDate === 'function') {
              prescription.updatedAt = data.updatedAt.toDate().toISOString();
            }

            prescriptions.push(prescription);
          });

          return prescriptions;
        } catch (indexError) {
          // If index error, fall back to simpler query without ordering
          if (indexError.toString().includes('requires an index')) {
            console.warn('Index not found for prescriptions, falling back to simple query');

            // Simple query without ordering
            const simpleQuery = query(
              prescriptionsCollection,
              where('doctorId', '==', doctorId)
            );

            const simpleSnapshot = await getDocs(simpleQuery);

            // Format prescription data
            const prescriptions = [];
            simpleSnapshot.forEach((doc) => {
              const data = doc.data();
              const prescription = {
                id: doc.id,
                ...data,
              };

              // Convert timestamps to ISO strings
              if (data.createdAt && typeof data.createdAt.toDate === 'function') {
                prescription.createdAt = data.createdAt.toDate().toISOString();
              }
              if (data.updatedAt && typeof data.updatedAt.toDate === 'function') {
                prescription.updatedAt = data.updatedAt.toDate().toISOString();
              }

              prescriptions.push(prescription);
            });

            // Sort manually by createdAt in descending order
            prescriptions.sort((a, b) => {
              const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
              const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
              return dateB - dateA; // Descending order
            });

            return prescriptions;
          } else {
            // Re-throw if it's not an index error
            throw indexError;
          }
        }
      } catch (firestoreError) {
        // Check if it's a permission error
        if (firestoreError.code === 'permission-denied') {
          console.error('Firebase permission denied. Please update Firestore rules to allow access to prescriptions collection.');

          // Return an empty array with a special flag to indicate permission error
          const emptyResult = [];
          emptyResult._permissionError = true;
          return emptyResult;
        }

        // Re-throw other errors
        throw firestoreError;
      }
    } catch (error) {
      console.error('Error getting doctor prescriptions from Firebase:', error);
      return [];
    }
  },

  /**
   * Get all prescriptions for a patient
   * @param {string} patientId - The patient ID
   * @param {string} status - Optional status filter (e.g., 'sent', 'pending', 'filled')
   * @returns {Promise<Array>} - Array of prescription records
   */
  getPatientPrescriptions: async (patientId, status = 'sent') => {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      // Query prescriptions collection
      const prescriptionsCollection = collection(db, 'prescriptions');

      try {
        // Try with ordering and status filter (requires composite index)
        const prescriptionsQuery = query(
          prescriptionsCollection,
          where('patientId', '==', patientId),
          where('status', '==', status),
          orderBy('createdAt', 'desc')
        );

        const querySnapshot = await getDocs(prescriptionsQuery);

        // Format prescription data
        const prescriptions = [];
        querySnapshot.forEach((doc) => {
          const data = doc.data();
          const prescription = {
            id: doc.id,
            ...data,
          };

          // Convert timestamps to ISO strings
          if (data.createdAt && typeof data.createdAt.toDate === 'function') {
            prescription.createdAt = data.createdAt.toDate().toISOString();
          }
          if (data.updatedAt && typeof data.updatedAt.toDate === 'function') {
            prescription.updatedAt = data.updatedAt.toDate().toISOString();
          }

          prescriptions.push(prescription);
        });

        return prescriptions;
      } catch (indexError) {
        // If index error, fall back to simpler query without ordering
        if (indexError.toString().includes('requires an index')) {
          console.warn('Index not found for patient prescriptions, falling back to simple query');

          // Simple query without ordering but with status filter
          const simpleQuery = query(
            prescriptionsCollection,
            where('patientId', '==', patientId),
            where('status', '==', status)
          );

          const simpleSnapshot = await getDocs(simpleQuery);

          // Format prescription data
          const prescriptions = [];
          simpleSnapshot.forEach((doc) => {
            const data = doc.data();
            const prescription = {
              id: doc.id,
              ...data,
            };

            // Convert timestamps to ISO strings
            if (data.createdAt && typeof data.createdAt.toDate === 'function') {
              prescription.createdAt = data.createdAt.toDate().toISOString();
            }
            if (data.updatedAt && typeof data.updatedAt.toDate === 'function') {
              prescription.updatedAt = data.updatedAt.toDate().toISOString();
            }

            prescriptions.push(prescription);
          });

          // Sort manually by createdAt in descending order
          prescriptions.sort((a, b) => {
            const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
            const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
            return dateB - dateA; // Descending order
          });

          return prescriptions;
        } else {
          // Re-throw if it's not an index error
          throw indexError;
        }
      }
    } catch (error) {
      console.error('Error getting patient prescriptions from Firebase:', error);
      return [];
    }
  },

  /**
   * Get a specific prescription by ID
   * @param {string} prescriptionId - The prescription ID
   * @returns {Promise<Object|null>} - The prescription or null if not found
   */
  getPrescriptionById: async (prescriptionId) => {
    try {
      if (!prescriptionId) {
        throw new Error('Prescription ID is required');
      }

      const prescriptionRef = doc(db, 'prescriptions', prescriptionId);
      const prescriptionSnap = await getDoc(prescriptionRef);

      if (!prescriptionSnap.exists()) {
        return null;
      }

      const data = prescriptionSnap.data();
      const prescription = {
        id: prescriptionSnap.id,
        ...data,
      };

      // Convert timestamps to ISO strings
      if (data.createdAt && typeof data.createdAt.toDate === 'function') {
        prescription.createdAt = data.createdAt.toDate().toISOString();
      }
      if (data.updatedAt && typeof data.updatedAt.toDate === 'function') {
        prescription.updatedAt = data.updatedAt.toDate().toISOString();
      }

      return prescription;
    } catch (error) {
      console.error('Error getting prescription by ID from Firebase:', error);
      return null;
    }
  },

  /**
   * Update a prescription's status
   * @param {string} prescriptionId - The prescription ID to update
   * @param {string} status - The new status (pending, sent, filled)
   * @returns {Promise<Object|null>} - The updated prescription or null if not found
   */
  updatePrescriptionStatus: async (prescriptionId, status) => {
    try {
      if (!prescriptionId) {
        throw new Error('Prescription ID is required');
      }

      const prescriptionRef = doc(db, 'prescriptions', prescriptionId);
      const prescriptionSnap = await getDoc(prescriptionRef);

      if (!prescriptionSnap.exists()) {
        return null;
      }

      // Update the status
      await updateDoc(prescriptionRef, {
        status,
        updatedAt: serverTimestamp()
      });

      // Get the updated document
      const updatedSnap = await getDoc(prescriptionRef);
      const data = updatedSnap.data();

      const updatedPrescription = {
        id: updatedSnap.id,
        ...data,
      };

      // Convert timestamps to ISO strings
      if (data.createdAt && typeof data.createdAt.toDate === 'function') {
        updatedPrescription.createdAt = data.createdAt.toDate().toISOString();
      }
      if (data.updatedAt && typeof data.updatedAt.toDate === 'function') {
        updatedPrescription.updatedAt = data.updatedAt.toDate().toISOString();
      }

      return updatedPrescription;
    } catch (error) {
      console.error('Error updating prescription status in Firebase:', error);
      return null;
    }
  },

  /**
   * Delete a prescription
   * @param {string} prescriptionId - The prescription ID to delete
   * @returns {Promise<boolean>} - True if successful, false otherwise
   */
  deletePrescription: async (prescriptionId) => {
    try {
      if (!prescriptionId) {
        throw new Error('Prescription ID is required');
      }

      const prescriptionRef = doc(db, 'prescriptions', prescriptionId);
      await deleteDoc(prescriptionRef);

      return true;
    } catch (error) {
      console.error('Error deleting prescription from Firebase:', error);
      return false;
    }
  }
};

export default firebasePrescriptionsService;
