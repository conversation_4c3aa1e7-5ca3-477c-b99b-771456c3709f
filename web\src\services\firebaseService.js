// Firebase service for API calls and data management
import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  startAfter,
  onSnapshot,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '../config/firebase';

/**
 * Generic Firestore service class
 */
class FirebaseService {
  constructor(collectionName) {
    this.collectionName = collectionName;
    this.collectionRef = collection(db, collectionName);
  }

  /**
   * Get all documents from collection
   * @param {object} options - Query options
   * @returns {Promise<Array>} Array of documents
   */
  async getAll(options = {}) {
    try {
      const {
        where: whereClause,
        orderBy: orderByClause,
        limit: limitClause,
        startAfter: startAfterDoc
      } = options;

      let q = this.collectionRef;

      // Apply where clauses
      if (whereClause && Array.isArray(whereClause)) {
        whereClause.forEach(([field, operator, value]) => {
          q = query(q, where(field, operator, value));
        });
      }

      // Apply order by
      if (orderByClause) {
        if (Array.isArray(orderByClause)) {
          orderByClause.forEach(([field, direction = 'asc']) => {
            q = query(q, orderBy(field, direction));
          });
        } else {
          q = query(q, orderBy(orderByClause));
        }
      }

      // Apply limit
      if (limitClause) {
        q = query(q, limit(limitClause));
      }

      // Apply pagination
      if (startAfterDoc) {
        q = query(q, startAfter(startAfterDoc));
      }

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error(`Error getting documents from ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Get a single document by ID
   * @param {string} id - Document ID
   * @returns {Promise<object|null>} Document data or null
   */
  async getById(id) {
    try {
      const docRef = doc(db, this.collectionName, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        };
      } else {
        return null;
      }
    } catch (error) {
      console.error(`Error getting document ${id} from ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Create a new document
   * @param {object} data - Document data
   * @returns {Promise<string>} Created document ID
   */
  async create(data) {
    try {
      const docData = {
        ...data,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      const docRef = await addDoc(this.collectionRef, docData);
      return docRef.id;
    } catch (error) {
      console.error(`Error creating document in ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Update a document
   * @param {string} id - Document ID
   * @param {object} data - Updated data
   * @returns {Promise<void>}
   */
  async update(id, data) {
    try {
      const docRef = doc(db, this.collectionName, id);
      const updateData = {
        ...data,
        updatedAt: serverTimestamp()
      };

      await updateDoc(docRef, updateData);
    } catch (error) {
      console.error(`Error updating document ${id} in ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Delete a document
   * @param {string} id - Document ID
   * @returns {Promise<void>}
   */
  async delete(id) {
    try {
      const docRef = doc(db, this.collectionName, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error(`Error deleting document ${id} from ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Listen to real-time updates
   * @param {function} callback - Callback function for updates
   * @param {object} options - Query options
   * @returns {function} Unsubscribe function
   */
  subscribe(callback, options = {}) {
    try {
      const {
        where: whereClause,
        orderBy: orderByClause,
        limit: limitClause
      } = options;

      let q = this.collectionRef;

      // Apply where clauses
      if (whereClause && Array.isArray(whereClause)) {
        whereClause.forEach(([field, operator, value]) => {
          q = query(q, where(field, operator, value));
        });
      }

      // Apply order by
      if (orderByClause) {
        if (Array.isArray(orderByClause)) {
          orderByClause.forEach(([field, direction = 'asc']) => {
            q = query(q, orderBy(field, direction));
          });
        } else {
          q = query(q, orderBy(orderByClause));
        }
      }

      // Apply limit
      if (limitClause) {
        q = query(q, limit(limitClause));
      }

      return onSnapshot(q, (snapshot) => {
        const documents = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        callback(documents);
      });
    } catch (error) {
      console.error(`Error subscribing to ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Get documents with pagination
   * @param {number} page - Page number (1-based)
   * @param {number} pageSize - Number of documents per page
   * @param {object} options - Additional query options
   * @returns {Promise<object>} Paginated results
   */
  async getPaginated(page = 1, pageSize = 10, options = {}) {
    try {
      const {
        where: whereClause,
        orderBy: orderByClause = ['createdAt', 'desc']
      } = options;

      let q = this.collectionRef;

      // Apply where clauses
      if (whereClause && Array.isArray(whereClause)) {
        whereClause.forEach(([field, operator, value]) => {
          q = query(q, where(field, operator, value));
        });
      }

      // Apply order by
      if (Array.isArray(orderByClause)) {
        orderByClause.forEach(([field, direction = 'asc']) => {
          q = query(q, orderBy(field, direction));
        });
      } else {
        q = query(q, orderBy(orderByClause));
      }

      // Get total count (for pagination info)
      const totalSnapshot = await getDocs(q);
      const totalItems = totalSnapshot.size;
      const totalPages = Math.ceil(totalItems / pageSize);

      // Apply pagination
      const offset = (page - 1) * pageSize;
      if (offset > 0) {
        const offsetSnapshot = await getDocs(query(q, limit(offset)));
        const lastDoc = offsetSnapshot.docs[offsetSnapshot.docs.length - 1];
        if (lastDoc) {
          q = query(q, startAfter(lastDoc));
        }
      }

      q = query(q, limit(pageSize));

      const snapshot = await getDocs(q);
      const items = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      return {
        items,
        page,
        pageSize,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      };
    } catch (error) {
      console.error(`Error getting paginated documents from ${this.collectionName}:`, error);
      throw error;
    }
  }
}

// Create service instances for different collections
export const usersService = new FirebaseService('users');
export const appointmentsService = new FirebaseService('appointments');
export const medicationsService = new FirebaseService('medications');
export const prescriptionsService = new FirebaseService('prescriptions');
export const vitalsService = new FirebaseService('vitals');
export const notificationsService = new FirebaseService('notifications');
export const messagesService = new FirebaseService('messages');

// Specialized service methods

/**
 * User-specific service methods
 */
export const userService = {
  ...usersService,

  /**
   * Get user by email
   * @param {string} email - User email
   * @returns {Promise<object|null>} User data or null
   */
  async getByEmail(email) {
    const users = await this.getAll({
      where: [['email', '==', email]],
      limit: 1
    });
    return users.length > 0 ? users[0] : null;
  },

  /**
   * Get user by user code
   * @param {string} userCode - User code
   * @returns {Promise<object|null>} User data or null
   */
  async getByUserCode(userCode) {
    const users = await this.getAll({
      where: [['userCode', '==', userCode]],
      limit: 1
    });
    return users.length > 0 ? users[0] : null;
  },

  /**
   * Update user profile
   * @param {string} userId - User ID
   * @param {object} profileData - Profile data
   * @returns {Promise<void>}
   */
  async updateProfile(userId, profileData) {
    return this.update(userId, profileData);
  }
};

/**
 * Appointment-specific service methods
 */
export const appointmentService = {
  ...appointmentsService,

  /**
   * Get appointments for a specific user
   * @param {string} userId - User ID
   * @param {string} role - User role ('patient' or 'doctor')
   * @returns {Promise<Array>} User appointments
   */
  async getByUser(userId, role = 'patient') {
    const field = role === 'patient' ? 'patientId' : 'doctorId';
    return this.getAll({
      where: [[field, '==', userId]],
      orderBy: ['appointmentDate', 'asc']
    });
  },

  /**
   * Get upcoming appointments
   * @param {string} userId - User ID
   * @param {string} role - User role
   * @returns {Promise<Array>} Upcoming appointments
   */
  async getUpcoming(userId, role = 'patient') {
    const field = role === 'patient' ? 'patientId' : 'doctorId';
    const now = new Date();
    
    return this.getAll({
      where: [
        [field, '==', userId],
        ['appointmentDate', '>=', now]
      ],
      orderBy: ['appointmentDate', 'asc']
    });
  }
};

/**
 * Medication-specific service methods
 */
export const medicationService = {
  ...medicationsService,

  /**
   * Get active medications for a patient
   * @param {string} patientId - Patient ID
   * @returns {Promise<Array>} Active medications
   */
  async getActiveByPatient(patientId) {
    return this.getAll({
      where: [
        ['patientId', '==', patientId],
        ['status', '==', 'active']
      ],
      orderBy: ['createdAt', 'desc']
    });
  }
};

export default FirebaseService;
