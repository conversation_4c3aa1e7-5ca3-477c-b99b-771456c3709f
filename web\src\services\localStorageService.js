/**
 * Local Storage Service for Web
 * Provides a consistent interface for storing and retrieving data from localStorage
 */

// Storage keys
const STORAGE_KEYS = {
  USER_PREFERENCES: 'neurocare_user_preferences',
  THEME_SETTINGS: 'neurocare_theme_settings',
  OFFLINE_DATA: 'neurocare_offline_data',
  CACHE: 'neurocare_cache',
  VITALS_CACHE: 'neurocare_vitals_cache',
  MEDICATIONS_CACHE: 'neurocare_medications_cache',
  APPOINTMENTS_CACHE: 'neurocare_appointments_cache',
  LAST_SYNC: 'neurocare_last_sync',
  USER_SETTINGS: 'neurocare_user_settings',
  NAVIGATION_HISTORY: 'neurocare_navigation_history',
  FORM_DRAFTS: 'neurocare_form_drafts',
};

/**
 * Check if localStorage is available
 */
const isLocalStorageAvailable = () => {
  try {
    const test = '__localStorage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch (error) {
    console.warn('localStorage is not available:', error);
    return false;
  }
};

/**
 * Store data in localStorage
 */
const setItem = async (key, value) => {
  try {
    if (!isLocalStorageAvailable()) {
      throw new Error('localStorage is not available');
    }

    const serializedValue = JSON.stringify({
      data: value,
      timestamp: new Date().toISOString(),
      version: '1.0'
    });

    localStorage.setItem(key, serializedValue);
    return true;
  } catch (error) {
    console.error('Error storing data in localStorage:', error);
    return false;
  }
};

/**
 * Retrieve data from localStorage
 */
const getItem = async (key, defaultValue = null) => {
  try {
    if (!isLocalStorageAvailable()) {
      return defaultValue;
    }

    const serializedValue = localStorage.getItem(key);
    
    if (serializedValue === null) {
      return defaultValue;
    }

    const parsedValue = JSON.parse(serializedValue);
    
    // Return the data, handling both old and new formats
    if (parsedValue && typeof parsedValue === 'object' && 'data' in parsedValue) {
      return parsedValue.data;
    }
    
    // Fallback for old format
    return parsedValue;
  } catch (error) {
    console.error('Error retrieving data from localStorage:', error);
    return defaultValue;
  }
};

/**
 * Remove data from localStorage
 */
const removeItem = async (key) => {
  try {
    if (!isLocalStorageAvailable()) {
      return false;
    }

    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error('Error removing data from localStorage:', error);
    return false;
  }
};

/**
 * Clear all app data from localStorage
 */
const clear = async () => {
  try {
    if (!isLocalStorageAvailable()) {
      return false;
    }

    // Remove only our app's keys
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });

    return true;
  } catch (error) {
    console.error('Error clearing localStorage:', error);
    return false;
  }
};

/**
 * Get all stored keys for the app
 */
const getAllKeys = async () => {
  try {
    if (!isLocalStorageAvailable()) {
      return [];
    }

    const keys = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('neurocare_')) {
        keys.push(key);
      }
    }

    return keys;
  } catch (error) {
    console.error('Error getting all keys from localStorage:', error);
    return [];
  }
};

/**
 * Store user preferences
 */
const setUserPreferences = async (preferences) => {
  return await setItem(STORAGE_KEYS.USER_PREFERENCES, preferences);
};

/**
 * Get user preferences
 */
const getUserPreferences = async () => {
  return await getItem(STORAGE_KEYS.USER_PREFERENCES, {});
};

/**
 * Store theme settings
 */
const setThemeSettings = async (themeSettings) => {
  return await setItem(STORAGE_KEYS.THEME_SETTINGS, themeSettings);
};

/**
 * Get theme settings
 */
const getThemeSettings = async () => {
  return await getItem(STORAGE_KEYS.THEME_SETTINGS, {});
};

/**
 * Store offline data
 */
const setOfflineData = async (data) => {
  return await setItem(STORAGE_KEYS.OFFLINE_DATA, data);
};

/**
 * Get offline data
 */
const getOfflineData = async () => {
  return await getItem(STORAGE_KEYS.OFFLINE_DATA, {});
};

/**
 * Store cache data with expiration
 */
const setCacheData = async (key, data, expirationMinutes = 60) => {
  const cacheData = {
    data,
    expiration: new Date(Date.now() + expirationMinutes * 60 * 1000).toISOString()
  };
  
  return await setItem(`${STORAGE_KEYS.CACHE}_${key}`, cacheData);
};

/**
 * Get cache data (returns null if expired)
 */
const getCacheData = async (key) => {
  const cacheData = await getItem(`${STORAGE_KEYS.CACHE}_${key}`);
  
  if (!cacheData || !cacheData.expiration) {
    return null;
  }
  
  const now = new Date();
  const expiration = new Date(cacheData.expiration);
  
  if (now > expiration) {
    // Cache expired, remove it
    await removeItem(`${STORAGE_KEYS.CACHE}_${key}`);
    return null;
  }
  
  return cacheData.data;
};

/**
 * Store last sync timestamp
 */
const setLastSync = async (timestamp = new Date().toISOString()) => {
  return await setItem(STORAGE_KEYS.LAST_SYNC, timestamp);
};

/**
 * Get last sync timestamp
 */
const getLastSync = async () => {
  return await getItem(STORAGE_KEYS.LAST_SYNC);
};

/**
 * Store form draft
 */
const setFormDraft = async (formId, draftData) => {
  const drafts = await getItem(STORAGE_KEYS.FORM_DRAFTS, {});
  drafts[formId] = {
    data: draftData,
    timestamp: new Date().toISOString()
  };
  return await setItem(STORAGE_KEYS.FORM_DRAFTS, drafts);
};

/**
 * Get form draft
 */
const getFormDraft = async (formId) => {
  const drafts = await getItem(STORAGE_KEYS.FORM_DRAFTS, {});
  return drafts[formId]?.data || null;
};

/**
 * Remove form draft
 */
const removeFormDraft = async (formId) => {
  const drafts = await getItem(STORAGE_KEYS.FORM_DRAFTS, {});
  delete drafts[formId];
  return await setItem(STORAGE_KEYS.FORM_DRAFTS, drafts);
};

/**
 * Get storage usage information
 */
const getStorageInfo = async () => {
  try {
    if (!isLocalStorageAvailable()) {
      return { available: false };
    }

    let totalSize = 0;
    const keys = await getAllKeys();
    
    keys.forEach(key => {
      const value = localStorage.getItem(key);
      if (value) {
        totalSize += value.length;
      }
    });

    return {
      available: true,
      totalKeys: keys.length,
      totalSize: totalSize,
      totalSizeKB: Math.round(totalSize / 1024 * 100) / 100
    };
  } catch (error) {
    console.error('Error getting storage info:', error);
    return { available: false, error: error.message };
  }
};

export default {
  // Core methods
  setItem,
  getItem,
  removeItem,
  clear,
  getAllKeys,
  
  // Specific data methods
  setUserPreferences,
  getUserPreferences,
  setThemeSettings,
  getThemeSettings,
  setOfflineData,
  getOfflineData,
  setCacheData,
  getCacheData,
  setLastSync,
  getLastSync,
  setFormDraft,
  getFormDraft,
  removeFormDraft,
  
  // Utility methods
  getStorageInfo,
  isLocalStorageAvailable,
  
  // Storage keys
  STORAGE_KEYS
};
