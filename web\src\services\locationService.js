// Location service for GPS tracking and geolocation features

import { toast } from 'react-toastify';

class LocationService {
  constructor() {
    this.watchId = null;
    this.currentPosition = null;
    this.isTracking = false;
    this.subscribers = new Set();
  }

  /**
   * Check if geolocation is supported
   * @returns {boolean}
   */
  isSupported() {
    return 'geolocation' in navigator;
  }

  /**
   * Get current position
   * @param {object} options - Geolocation options
   * @returns {Promise<Position>}
   */
  async getCurrentPosition(options = {}) {
    if (!this.isSupported()) {
      throw new Error('Geolocation is not supported by this browser');
    }

    const defaultOptions = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 60000, // 1 minute
      ...options
    };

    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          this.currentPosition = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: new Date(position.timestamp),
            heading: position.coords.heading,
            speed: position.coords.speed
          };
          resolve(this.currentPosition);
        },
        (error) => {
          this.handleLocationError(error);
          reject(error);
        },
        defaultOptions
      );
    });
  }

  /**
   * Start watching position changes
   * @param {function} callback - Callback function for position updates
   * @param {object} options - Geolocation options
   * @returns {number} Watch ID
   */
  startTracking(callback, options = {}) {
    if (!this.isSupported()) {
      throw new Error('Geolocation is not supported by this browser');
    }

    if (this.isTracking) {
      this.stopTracking();
    }

    const defaultOptions = {
      enableHighAccuracy: true,
      timeout: 5000,
      maximumAge: 30000, // 30 seconds
      ...options
    };

    this.watchId = navigator.geolocation.watchPosition(
      (position) => {
        this.currentPosition = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: new Date(position.timestamp),
          heading: position.coords.heading,
          speed: position.coords.speed
        };

        this.isTracking = true;
        
        // Notify all subscribers
        this.subscribers.forEach(subscriber => {
          try {
            subscriber(this.currentPosition);
          } catch (error) {
            console.error('Error in location subscriber:', error);
          }
        });

        // Call the provided callback
        if (callback) {
          callback(this.currentPosition);
        }
      },
      (error) => {
        this.handleLocationError(error);
        this.isTracking = false;
      },
      defaultOptions
    );

    return this.watchId;
  }

  /**
   * Stop watching position changes
   */
  stopTracking() {
    if (this.watchId !== null) {
      navigator.geolocation.clearWatch(this.watchId);
      this.watchId = null;
      this.isTracking = false;
    }
  }

  /**
   * Subscribe to location updates
   * @param {function} callback - Callback function
   * @returns {function} Unsubscribe function
   */
  subscribe(callback) {
    this.subscribers.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.subscribers.delete(callback);
    };
  }

  /**
   * Calculate distance between two points using Haversine formula
   * @param {object} point1 - {latitude, longitude}
   * @param {object} point2 - {latitude, longitude}
   * @returns {number} Distance in meters
   */
  calculateDistance(point1, point2) {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = point1.latitude * Math.PI / 180;
    const φ2 = point2.latitude * Math.PI / 180;
    const Δφ = (point2.latitude - point1.latitude) * Math.PI / 180;
    const Δλ = (point2.longitude - point1.longitude) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c; // Distance in meters
  }

  /**
   * Check if a point is within a geofence
   * @param {object} point - {latitude, longitude}
   * @param {object} center - {latitude, longitude}
   * @param {number} radius - Radius in meters
   * @returns {boolean}
   */
  isWithinGeofence(point, center, radius) {
    const distance = this.calculateDistance(point, center);
    return distance <= radius;
  }

  /**
   * Get address from coordinates (reverse geocoding)
   * @param {number} latitude
   * @param {number} longitude
   * @returns {Promise<string>}
   */
  async getAddressFromCoordinates(latitude, longitude) {
    try {
      // Using a free geocoding service (you might want to use Google Maps API in production)
      const response = await fetch(
        `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=en`
      );
      
      if (!response.ok) {
        throw new Error('Geocoding request failed');
      }
      
      const data = await response.json();
      return data.display_name || `${latitude}, ${longitude}`;
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    }
  }

  /**
   * Get coordinates from address (forward geocoding)
   * @param {string} address
   * @returns {Promise<object>}
   */
  async getCoordinatesFromAddress(address) {
    try {
      // Using a free geocoding service
      const response = await fetch(
        `https://api.opencagedata.com/geocode/v1/json?q=${encodeURIComponent(address)}&key=YOUR_API_KEY&limit=1`
      );
      
      if (!response.ok) {
        throw new Error('Geocoding request failed');
      }
      
      const data = await response.json();
      
      if (data.results && data.results.length > 0) {
        const result = data.results[0];
        return {
          latitude: result.geometry.lat,
          longitude: result.geometry.lng,
          formatted: result.formatted
        };
      } else {
        throw new Error('Address not found');
      }
    } catch (error) {
      console.error('Forward geocoding error:', error);
      throw error;
    }
  }

  /**
   * Handle geolocation errors
   * @param {GeolocationPositionError} error
   */
  handleLocationError(error) {
    let message = 'Location error occurred';
    
    switch (error.code) {
      case error.PERMISSION_DENIED:
        message = 'Location access denied by user';
        break;
      case error.POSITION_UNAVAILABLE:
        message = 'Location information unavailable';
        break;
      case error.TIMEOUT:
        message = 'Location request timed out';
        break;
      default:
        message = 'Unknown location error';
        break;
    }
    
    console.error('Geolocation error:', message, error);
    toast.error(message);
  }

  /**
   * Request location permission
   * @returns {Promise<string>}
   */
  async requestPermission() {
    if (!('permissions' in navigator)) {
      // Fallback: try to get location directly
      try {
        await this.getCurrentPosition();
        return 'granted';
      } catch (error) {
        return 'denied';
      }
    }

    try {
      const permission = await navigator.permissions.query({ name: 'geolocation' });
      return permission.state;
    } catch (error) {
      console.error('Permission query error:', error);
      return 'prompt';
    }
  }

  /**
   * Format coordinates for display
   * @param {number} latitude
   * @param {number} longitude
   * @param {number} precision
   * @returns {string}
   */
  formatCoordinates(latitude, longitude, precision = 6) {
    return `${latitude.toFixed(precision)}, ${longitude.toFixed(precision)}`;
  }

  /**
   * Get current position with error handling
   * @returns {Promise<object|null>}
   */
  async getSafeCurrentPosition() {
    try {
      return await this.getCurrentPosition();
    } catch (error) {
      console.error('Failed to get current position:', error);
      return null;
    }
  }

  /**
   * Create a geofence monitor
   * @param {object} center - {latitude, longitude}
   * @param {number} radius - Radius in meters
   * @param {function} onEnter - Callback when entering geofence
   * @param {function} onExit - Callback when exiting geofence
   * @returns {function} Stop monitoring function
   */
  createGeofenceMonitor(center, radius, onEnter, onExit) {
    let wasInside = false;
    
    const unsubscribe = this.subscribe((position) => {
      const isInside = this.isWithinGeofence(position, center, radius);
      
      if (isInside && !wasInside) {
        // Entered geofence
        if (onEnter) onEnter(position);
      } else if (!isInside && wasInside) {
        // Exited geofence
        if (onExit) onExit(position);
      }
      
      wasInside = isInside;
    });
    
    return unsubscribe;
  }

  /**
   * Get current location status
   * @returns {object}
   */
  getStatus() {
    return {
      isSupported: this.isSupported(),
      isTracking: this.isTracking,
      currentPosition: this.currentPosition,
      subscriberCount: this.subscribers.size
    };
  }
}

// Create and export a singleton instance
const locationService = new LocationService();

export default locationService;
