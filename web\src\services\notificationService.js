// Notification service for managing app notifications
import { toast } from 'react-toastify';
import { notificationsService } from './firebaseService';
import { NOTIFICATION_TYPES, NOTIFICATION_PRIORITIES } from '../utils/constants';

/**
 * Notification service for handling in-app and push notifications
 */
class NotificationService {
  constructor() {
    this.subscribers = new Map();
    this.unsubscribeFunctions = new Map();
  }

  /**
   * Show a toast notification
   * @param {string} message - Notification message
   * @param {string} type - Notification type ('success', 'error', 'warning', 'info')
   * @param {object} options - Additional options
   */
  showToast(message, type = 'info', options = {}) {
    const defaultOptions = {
      position: 'top-right',
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    };

    const toastOptions = { ...defaultOptions, ...options };

    switch (type) {
      case 'success':
        toast.success(message, toastOptions);
        break;
      case 'error':
        toast.error(message, toastOptions);
        break;
      case 'warning':
        toast.warning(message, toastOptions);
        break;
      case 'info':
      default:
        toast.info(message, toastOptions);
        break;
    }
  }

  /**
   * Show success notification
   * @param {string} message - Success message
   * @param {object} options - Additional options
   */
  success(message, options = {}) {
    this.showToast(message, 'success', options);
  }

  /**
   * Show error notification
   * @param {string} message - Error message
   * @param {object} options - Additional options
   */
  error(message, options = {}) {
    this.showToast(message, 'error', options);
  }

  /**
   * Show warning notification
   * @param {string} message - Warning message
   * @param {object} options - Additional options
   */
  warning(message, options = {}) {
    this.showToast(message, 'warning', options);
  }

  /**
   * Show info notification
   * @param {string} message - Info message
   * @param {object} options - Additional options
   */
  info(message, options = {}) {
    this.showToast(message, 'info', options);
  }

  /**
   * Create a notification in the database
   * @param {object} notificationData - Notification data
   * @returns {Promise<string>} Created notification ID
   */
  async createNotification(notificationData) {
    try {
      const notification = {
        title: notificationData.title,
        message: notificationData.message,
        type: notificationData.type || NOTIFICATION_TYPES.SYSTEM,
        priority: notificationData.priority || NOTIFICATION_PRIORITIES.MEDIUM,
        userId: notificationData.userId,
        read: false,
        data: notificationData.data || {},
        ...notificationData
      };

      const notificationId = await notificationsService.create(notification);
      
      // Show toast notification if requested
      if (notificationData.showToast !== false) {
        this.showToast(notification.message, this.getToastType(notification.type));
      }

      return notificationId;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Get notifications for a user
   * @param {string} userId - User ID
   * @param {object} options - Query options
   * @returns {Promise<Array>} User notifications
   */
  async getUserNotifications(userId, options = {}) {
    try {
      const {
        limit: limitCount = 50,
        unreadOnly = false,
        type = null
      } = options;

      const whereClause = [['userId', '==', userId]];
      
      if (unreadOnly) {
        whereClause.push(['read', '==', false]);
      }
      
      if (type) {
        whereClause.push(['type', '==', type]);
      }

      return await notificationsService.getAll({
        where: whereClause,
        orderBy: ['createdAt', 'desc'],
        limit: limitCount
      });
    } catch (error) {
      console.error('Error getting user notifications:', error);
      throw error;
    }
  }

  /**
   * Mark notification as read
   * @param {string} notificationId - Notification ID
   * @returns {Promise<void>}
   */
  async markAsRead(notificationId) {
    try {
      await notificationsService.update(notificationId, { read: true });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read for a user
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async markAllAsRead(userId) {
    try {
      const unreadNotifications = await this.getUserNotifications(userId, { unreadOnly: true });
      
      const updatePromises = unreadNotifications.map(notification =>
        notificationsService.update(notification.id, { read: true })
      );
      
      await Promise.all(updatePromises);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  /**
   * Delete a notification
   * @param {string} notificationId - Notification ID
   * @returns {Promise<void>}
   */
  async deleteNotification(notificationId) {
    try {
      await notificationsService.delete(notificationId);
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  /**
   * Subscribe to real-time notifications for a user
   * @param {string} userId - User ID
   * @param {function} callback - Callback function for new notifications
   * @returns {function} Unsubscribe function
   */
  subscribeToUserNotifications(userId, callback) {
    try {
      // Unsubscribe from previous subscription if exists
      if (this.unsubscribeFunctions.has(userId)) {
        this.unsubscribeFunctions.get(userId)();
      }

      const unsubscribe = notificationsService.subscribe(
        (notifications) => {
          // Sort by creation date (newest first)
          const sortedNotifications = notifications.sort((a, b) => {
            const aTime = a.createdAt?.toDate?.() || new Date(a.createdAt);
            const bTime = b.createdAt?.toDate?.() || new Date(b.createdAt);
            return bTime - aTime;
          });

          callback(sortedNotifications);
        },
        {
          where: [['userId', '==', userId]],
          orderBy: ['createdAt', 'desc']
        }
      );

      this.unsubscribeFunctions.set(userId, unsubscribe);
      return unsubscribe;
    } catch (error) {
      console.error('Error subscribing to user notifications:', error);
      throw error;
    }
  }

  /**
   * Unsubscribe from notifications for a user
   * @param {string} userId - User ID
   */
  unsubscribeFromUserNotifications(userId) {
    if (this.unsubscribeFunctions.has(userId)) {
      this.unsubscribeFunctions.get(userId)();
      this.unsubscribeFunctions.delete(userId);
    }
  }

  /**
   * Get unread notification count for a user
   * @param {string} userId - User ID
   * @returns {Promise<number>} Unread count
   */
  async getUnreadCount(userId) {
    try {
      const unreadNotifications = await this.getUserNotifications(userId, { unreadOnly: true });
      return unreadNotifications.length;
    } catch (error) {
      console.error('Error getting unread notification count:', error);
      return 0;
    }
  }

  /**
   * Send appointment reminder notification
   * @param {object} appointment - Appointment data
   * @returns {Promise<string>} Notification ID
   */
  async sendAppointmentReminder(appointment) {
    const notification = {
      title: 'Appointment Reminder',
      message: `You have an appointment with Dr. ${appointment.doctorName} at ${new Date(appointment.appointmentDate).toLocaleTimeString()}`,
      type: NOTIFICATION_TYPES.APPOINTMENT,
      priority: NOTIFICATION_PRIORITIES.HIGH,
      userId: appointment.patientId,
      data: {
        appointmentId: appointment.id,
        appointmentDate: appointment.appointmentDate
      }
    };

    return this.createNotification(notification);
  }

  /**
   * Send medication reminder notification
   * @param {object} medication - Medication data
   * @param {string} userId - User ID
   * @returns {Promise<string>} Notification ID
   */
  async sendMedicationReminder(medication, userId) {
    const notification = {
      title: 'Medication Reminder',
      message: `Time to take your medication: ${medication.name} (${medication.dosage})`,
      type: NOTIFICATION_TYPES.MEDICATION,
      priority: NOTIFICATION_PRIORITIES.HIGH,
      userId: userId,
      data: {
        medicationId: medication.id,
        medicationName: medication.name,
        dosage: medication.dosage
      }
    };

    return this.createNotification(notification);
  }

  /**
   * Send system alert notification
   * @param {string} message - Alert message
   * @param {string} userId - User ID
   * @param {string} priority - Alert priority
   * @returns {Promise<string>} Notification ID
   */
  async sendAlert(message, userId, priority = NOTIFICATION_PRIORITIES.MEDIUM) {
    const notification = {
      title: 'System Alert',
      message: message,
      type: NOTIFICATION_TYPES.ALERT,
      priority: priority,
      userId: userId
    };

    return this.createNotification(notification);
  }

  /**
   * Convert notification type to toast type
   * @param {string} notificationType - Notification type
   * @returns {string} Toast type
   */
  getToastType(notificationType) {
    switch (notificationType) {
      case NOTIFICATION_TYPES.ALERT:
        return 'warning';
      case NOTIFICATION_TYPES.APPOINTMENT:
      case NOTIFICATION_TYPES.MEDICATION:
        return 'info';
      case NOTIFICATION_TYPES.SYSTEM:
        return 'info';
      default:
        return 'info';
    }
  }

  /**
   * Request notification permission (for browser notifications)
   * @returns {Promise<boolean>} Permission granted
   */
  async requestPermission() {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'denied') {
      return false;
    }

    const permission = await Notification.requestPermission();
    return permission === 'granted';
  }

  /**
   * Show browser notification
   * @param {string} title - Notification title
   * @param {object} options - Notification options
   */
  showBrowserNotification(title, options = {}) {
    if (Notification.permission === 'granted') {
      const notification = new Notification(title, {
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        ...options
      });

      // Auto close after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);

      return notification;
    }
  }
}

// Create and export singleton instance
const notificationService = new NotificationService();
export default notificationService;
