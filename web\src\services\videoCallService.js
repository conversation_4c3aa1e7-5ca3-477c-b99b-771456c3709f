import { 
  collection, 
  addDoc, 
  doc, 
  getDoc, 
  updateDoc, 
  deleteDoc,
  query, 
  where, 
  getDocs, 
  onSnapshot,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { VIDEO_CALL_STATUS } from '../config/constants';

/**
 * Service for managing video calls in the web application
 */
export class VideoCallService {
  constructor() {
    this.activeListeners = new Map();
  }

  /**
   * Create a new video call session
   * @param {Object} callData - Call configuration
   * @returns {Promise<Object>} - Created call information
   */
  async createCall(callData) {
    try {
      const {
        patientId,
        doctorId,
        appointmentId = null,
        roomName = null,
        callType = 'consultation',
        scheduledTime = null
      } = callData;

      // Generate room name if not provided
      const generatedRoomName = roomName || `neurocare-${Date.now()}-${Math.random().toString(36).substring(7)}`;

      const callDocument = {
        patientId,
        doctorId,
        appointmentId,
        roomName: generatedRoomName,
        callType,
        status: VIDEO_CALL_STATUS.CONNECTING,
        scheduledTime: scheduledTime || null,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        startedAt: null,
        endedAt: null,
        duration: 0,
        participants: [
          {
            userId: patientId,
            role: 'patient',
            joinedAt: null,
            leftAt: null,
            status: 'invited'
          },
          {
            userId: doctorId,
            role: 'doctor',
            joinedAt: null,
            leftAt: null,
            status: 'invited'
          }
        ],
        settings: {
          audioEnabled: true,
          videoEnabled: true,
          screenShareEnabled: false,
          recordingEnabled: false,
          chatEnabled: true
        },
        metadata: {
          platform: 'web',
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        }
      };

      const docRef = await addDoc(collection(db, 'videoCalls'), callDocument);
      
      return {
        id: docRef.id,
        ...callDocument,
        roomUrl: this.generateRoomUrl(generatedRoomName)
      };
    } catch (error) {
      console.error('Error creating video call:', error);
      throw new Error('Failed to create video call: ' + error.message);
    }
  }

  /**
   * Join an existing video call
   * @param {string} callId - Call ID to join
   * @param {string} userId - User ID joining the call
   * @param {string} userRole - Role of the user joining
   * @returns {Promise<Object>} - Updated call information
   */
  async joinCall(callId, userId, userRole) {
    try {
      const callRef = doc(db, 'videoCalls', callId);
      const callDoc = await getDoc(callRef);

      if (!callDoc.exists()) {
        throw new Error('Call not found');
      }

      const callData = callDoc.data();
      const now = new Date();

      // Update participant status
      const updatedParticipants = callData.participants.map(participant => {
        if (participant.userId === userId) {
          return {
            ...participant,
            joinedAt: now.toISOString(),
            status: 'joined'
          };
        }
        return participant;
      });

      // Update call status if this is the first participant
      let newStatus = callData.status;
      let startedAt = callData.startedAt;

      if (callData.status === VIDEO_CALL_STATUS.CONNECTING) {
        newStatus = VIDEO_CALL_STATUS.CONNECTED;
        startedAt = now.toISOString();
      }

      await updateDoc(callRef, {
        participants: updatedParticipants,
        status: newStatus,
        startedAt: startedAt,
        updatedAt: serverTimestamp()
      });

      return {
        id: callId,
        ...callData,
        participants: updatedParticipants,
        status: newStatus,
        startedAt: startedAt,
        roomUrl: this.generateRoomUrl(callData.roomName)
      };
    } catch (error) {
      console.error('Error joining video call:', error);
      throw new Error('Failed to join video call: ' + error.message);
    }
  }

  /**
   * Leave a video call
   * @param {string} callId - Call ID to leave
   * @param {string} userId - User ID leaving the call
   * @returns {Promise<void>}
   */
  async leaveCall(callId, userId) {
    try {
      const callRef = doc(db, 'videoCalls', callId);
      const callDoc = await getDoc(callRef);

      if (!callDoc.exists()) {
        throw new Error('Call not found');
      }

      const callData = callDoc.data();
      const now = new Date();

      // Update participant status
      const updatedParticipants = callData.participants.map(participant => {
        if (participant.userId === userId) {
          return {
            ...participant,
            leftAt: now.toISOString(),
            status: 'left'
          };
        }
        return participant;
      });

      // Check if all participants have left
      const activeParticipants = updatedParticipants.filter(p => p.status === 'joined');
      let newStatus = callData.status;
      let endedAt = callData.endedAt;
      let duration = callData.duration;

      if (activeParticipants.length === 0 && callData.status === VIDEO_CALL_STATUS.CONNECTED) {
        newStatus = VIDEO_CALL_STATUS.ENDED;
        endedAt = now.toISOString();
        
        // Calculate duration if call was started
        if (callData.startedAt) {
          const startTime = new Date(callData.startedAt);
          duration = Math.floor((now - startTime) / 1000); // Duration in seconds
        }
      }

      await updateDoc(callRef, {
        participants: updatedParticipants,
        status: newStatus,
        endedAt: endedAt,
        duration: duration,
        updatedAt: serverTimestamp()
      });

    } catch (error) {
      console.error('Error leaving video call:', error);
      throw new Error('Failed to leave video call: ' + error.message);
    }
  }

  /**
   * End a video call (force end by host)
   * @param {string} callId - Call ID to end
   * @param {string} endedBy - User ID who ended the call
   * @returns {Promise<void>}
   */
  async endCall(callId, endedBy) {
    try {
      const callRef = doc(db, 'videoCalls', callId);
      const callDoc = await getDoc(callRef);

      if (!callDoc.exists()) {
        throw new Error('Call not found');
      }

      const callData = callDoc.data();
      const now = new Date();

      // Calculate duration if call was started
      let duration = callData.duration;
      if (callData.startedAt && callData.status === VIDEO_CALL_STATUS.CONNECTED) {
        const startTime = new Date(callData.startedAt);
        duration = Math.floor((now - startTime) / 1000);
      }

      // Update all participants to left status
      const updatedParticipants = callData.participants.map(participant => ({
        ...participant,
        leftAt: participant.leftAt || now.toISOString(),
        status: participant.status === 'joined' ? 'left' : participant.status
      }));

      await updateDoc(callRef, {
        status: VIDEO_CALL_STATUS.ENDED,
        endedAt: now.toISOString(),
        endedBy: endedBy,
        duration: duration,
        participants: updatedParticipants,
        updatedAt: serverTimestamp()
      });

    } catch (error) {
      console.error('Error ending video call:', error);
      throw new Error('Failed to end video call: ' + error.message);
    }
  }

  /**
   * Get call details by ID
   * @param {string} callId - Call ID to retrieve
   * @returns {Promise<Object>} - Call information
   */
  async getCallDetails(callId) {
    try {
      const callRef = doc(db, 'videoCalls', callId);
      const callDoc = await getDoc(callRef);

      if (!callDoc.exists()) {
        throw new Error('Call not found');
      }

      const callData = callDoc.data();
      return {
        id: callId,
        ...callData,
        roomUrl: this.generateRoomUrl(callData.roomName)
      };
    } catch (error) {
      console.error('Error getting call details:', error);
      throw new Error('Failed to get call details: ' + error.message);
    }
  }

  /**
   * Get active calls for a user
   * @param {string} userId - User ID
   * @param {string} userRole - User role
   * @returns {Promise<Array>} - List of active calls
   */
  async getActiveCalls(userId, userRole) {
    try {
      const callsCollection = collection(db, 'videoCalls');
      
      // Query based on user role
      const fieldToQuery = userRole === 'doctor' ? 'doctorId' : 'patientId';
      
      const callsQuery = query(
        callsCollection,
        where(fieldToQuery, '==', userId),
        where('status', 'in', [VIDEO_CALL_STATUS.CONNECTING, VIDEO_CALL_STATUS.CONNECTED])
      );

      const callsSnapshot = await getDocs(callsQuery);
      
      return callsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        roomUrl: this.generateRoomUrl(doc.data().roomName)
      }));
    } catch (error) {
      console.error('Error getting active calls:', error);
      return [];
    }
  }

  /**
   * Get call history for a user
   * @param {string} userId - User ID
   * @param {string} userRole - User role
   * @param {number} limit - Number of calls to retrieve
   * @returns {Promise<Array>} - List of past calls
   */
  async getCallHistory(userId, userRole, limit = 20) {
    try {
      const callsCollection = collection(db, 'videoCalls');
      
      const fieldToQuery = userRole === 'doctor' ? 'doctorId' : 'patientId';
      
      const callsQuery = query(
        callsCollection,
        where(fieldToQuery, '==', userId),
        where('status', '==', VIDEO_CALL_STATUS.ENDED)
      );

      const callsSnapshot = await getDocs(callsQuery);
      
      const calls = callsSnapshot.docs
        .map(doc => ({
          id: doc.id,
          ...doc.data()
        }))
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, limit);

      return calls;
    } catch (error) {
      console.error('Error getting call history:', error);
      return [];
    }
  }

  /**
   * Listen to call updates in real-time
   * @param {string} callId - Call ID to listen to
   * @param {Function} callback - Callback function for updates
   * @returns {Function} - Unsubscribe function
   */
  listenToCallUpdates(callId, callback) {
    try {
      const callRef = doc(db, 'videoCalls', callId);
      
      const unsubscribe = onSnapshot(callRef, (doc) => {
        if (doc.exists()) {
          const callData = {
            id: doc.id,
            ...doc.data(),
            roomUrl: this.generateRoomUrl(doc.data().roomName)
          };
          callback(callData);
        } else {
          callback(null);
        }
      }, (error) => {
        console.error('Error listening to call updates:', error);
        callback(null, error);
      });

      // Store the unsubscribe function
      this.activeListeners.set(callId, unsubscribe);
      
      return unsubscribe;
    } catch (error) {
      console.error('Error setting up call listener:', error);
      return () => {};
    }
  }

  /**
   * Stop listening to call updates
   * @param {string} callId - Call ID to stop listening to
   */
  stopListeningToCall(callId) {
    const unsubscribe = this.activeListeners.get(callId);
    if (unsubscribe) {
      unsubscribe();
      this.activeListeners.delete(callId);
    }
  }

  /**
   * Update call settings
   * @param {string} callId - Call ID
   * @param {Object} settings - Settings to update
   * @returns {Promise<void>}
   */
  async updateCallSettings(callId, settings) {
    try {
      const callRef = doc(db, 'videoCalls', callId);
      
      await updateDoc(callRef, {
        settings: settings,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating call settings:', error);
      throw new Error('Failed to update call settings: ' + error.message);
    }
  }

  /**
   * Generate room URL for video call
   * @param {string} roomName - Room name
   * @returns {string} - Complete room URL
   */
  generateRoomUrl(roomName) {
    // In a real implementation, you would use your video service provider's URL
    // For example, with Jitsi Meet:
    return `https://meet.jit.si/${roomName}`;
    
    // Or with a custom domain:
    // return `https://video.neurocare.app/${roomName}`;
  }

  /**
   * Clean up all active listeners
   */
  cleanup() {
    this.activeListeners.forEach((unsubscribe) => {
      unsubscribe();
    });
    this.activeListeners.clear();
  }
}

// Create and export a singleton instance
export const videoCallService = new VideoCallService();
export default videoCallService;
