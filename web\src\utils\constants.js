// Constants for the NeuroCare web application

// User Roles
export const USER_ROLES = {
  PATIENT: 'patient',
  DOCTOR: 'doctor',
  ADMIN: 'admin',
  CAREGIVER: 'caregiver',
  SUPERVISOR: 'supervisor'
};

// Role Display Names
export const ROLE_DISPLAY_NAMES = {
  [USER_ROLES.PATIENT]: 'Patient',
  [USER_ROLES.DOCTOR]: 'Doctor',
  [USER_ROLES.ADMIN]: 'Administrator',
  [USER_ROLES.CAREGIVER]: 'Caregiver',
  [USER_ROLES.SUPERVISOR]: 'Supervisor'
};

// Role Icons
export const ROLE_ICONS = {
  [USER_ROLES.PATIENT]: '👤',
  [USER_ROLES.DOCTOR]: '👨‍⚕️',
  [USER_ROLES.ADMIN]: '🛡️',
  [USER_ROLES.CAREGIVER]: '🤝',
  [USER_ROLES.SUPERVISOR]: '👁️'
};

// Appointment Status
export const APPOINTMENT_STATUS = {
  SCHEDULED: 'scheduled',
  CONFIRMED: 'confirmed',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  NO_SHOW: 'no_show'
};

// Appointment Status Display Names
export const APPOINTMENT_STATUS_DISPLAY = {
  [APPOINTMENT_STATUS.SCHEDULED]: 'Scheduled',
  [APPOINTMENT_STATUS.CONFIRMED]: 'Confirmed',
  [APPOINTMENT_STATUS.IN_PROGRESS]: 'In Progress',
  [APPOINTMENT_STATUS.COMPLETED]: 'Completed',
  [APPOINTMENT_STATUS.CANCELLED]: 'Cancelled',
  [APPOINTMENT_STATUS.NO_SHOW]: 'No Show'
};

// Appointment Types
export const APPOINTMENT_TYPES = {
  CONSULTATION: 'consultation',
  FOLLOW_UP: 'follow_up',
  EMERGENCY: 'emergency',
  ROUTINE_CHECKUP: 'routine_checkup',
  SPECIALIST: 'specialist',
  THERAPY: 'therapy',
  VACCINATION: 'vaccination'
};

// Medication Status
export const MEDICATION_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  COMPLETED: 'completed',
  DISCONTINUED: 'discontinued'
};

// Prescription Status
export const PRESCRIPTION_STATUS = {
  DRAFT: 'draft',
  SENT: 'sent',
  FILLED: 'filled',
  EXPIRED: 'expired',
  CANCELLED: 'cancelled'
};

// Vital Sign Types
export const VITAL_SIGN_TYPES = {
  BLOOD_PRESSURE_SYSTOLIC: 'blood_pressure_systolic',
  BLOOD_PRESSURE_DIASTOLIC: 'blood_pressure_diastolic',
  HEART_RATE: 'heart_rate',
  TEMPERATURE: 'temperature',
  OXYGEN_SATURATION: 'oxygen_saturation',
  BLOOD_SUGAR: 'blood_sugar',
  WEIGHT: 'weight',
  HEIGHT: 'height'
};

// Vital Sign Display Names
export const VITAL_SIGN_DISPLAY_NAMES = {
  [VITAL_SIGN_TYPES.BLOOD_PRESSURE_SYSTOLIC]: 'Blood Pressure (Systolic)',
  [VITAL_SIGN_TYPES.BLOOD_PRESSURE_DIASTOLIC]: 'Blood Pressure (Diastolic)',
  [VITAL_SIGN_TYPES.HEART_RATE]: 'Heart Rate',
  [VITAL_SIGN_TYPES.TEMPERATURE]: 'Temperature',
  [VITAL_SIGN_TYPES.OXYGEN_SATURATION]: 'Oxygen Saturation',
  [VITAL_SIGN_TYPES.BLOOD_SUGAR]: 'Blood Sugar',
  [VITAL_SIGN_TYPES.WEIGHT]: 'Weight',
  [VITAL_SIGN_TYPES.HEIGHT]: 'Height'
};

// Vital Sign Units
export const VITAL_SIGN_UNITS = {
  [VITAL_SIGN_TYPES.BLOOD_PRESSURE_SYSTOLIC]: 'mmHg',
  [VITAL_SIGN_TYPES.BLOOD_PRESSURE_DIASTOLIC]: 'mmHg',
  [VITAL_SIGN_TYPES.HEART_RATE]: 'bpm',
  [VITAL_SIGN_TYPES.TEMPERATURE]: '°C',
  [VITAL_SIGN_TYPES.OXYGEN_SATURATION]: '%',
  [VITAL_SIGN_TYPES.BLOOD_SUGAR]: 'mg/dL',
  [VITAL_SIGN_TYPES.WEIGHT]: 'kg',
  [VITAL_SIGN_TYPES.HEIGHT]: 'cm'
};

// Normal Ranges for Vital Signs
export const VITAL_SIGN_NORMAL_RANGES = {
  [VITAL_SIGN_TYPES.BLOOD_PRESSURE_SYSTOLIC]: { min: 90, max: 140 },
  [VITAL_SIGN_TYPES.BLOOD_PRESSURE_DIASTOLIC]: { min: 60, max: 90 },
  [VITAL_SIGN_TYPES.HEART_RATE]: { min: 60, max: 100 },
  [VITAL_SIGN_TYPES.TEMPERATURE]: { min: 36.1, max: 37.2 },
  [VITAL_SIGN_TYPES.OXYGEN_SATURATION]: { min: 95, max: 100 },
  [VITAL_SIGN_TYPES.BLOOD_SUGAR]: { min: 70, max: 140 }
};

// Notification Types
export const NOTIFICATION_TYPES = {
  APPOINTMENT: 'appointment',
  MEDICATION: 'medication',
  ALERT: 'alert',
  SYSTEM: 'system',
  MESSAGE: 'message'
};

// Notification Priorities
export const NOTIFICATION_PRIORITIES = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent'
};

// Gender Options
export const GENDER_OPTIONS = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
  { value: 'prefer-not-to-say', label: 'Prefer not to say' }
];

// Frequency Options for Medications
export const MEDICATION_FREQUENCIES = [
  { value: 'once_daily', label: 'Once daily' },
  { value: 'twice_daily', label: 'Twice daily' },
  { value: 'three_times_daily', label: 'Three times daily' },
  { value: 'four_times_daily', label: 'Four times daily' },
  { value: 'every_other_day', label: 'Every other day' },
  { value: 'weekly', label: 'Weekly' },
  { value: 'as_needed', label: 'As needed' }
];

// Time Slots for Appointments
export const DEFAULT_TIME_SLOTS = [
  '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
  '12:00', '12:30', '13:00', '13:30', '14:00', '14:30',
  '15:00', '15:30', '16:00', '16:30', '17:00'
];

// Dashboard Card Types
export const DASHBOARD_CARD_TYPES = {
  STAT: 'stat',
  CHART: 'chart',
  LIST: 'list',
  ACTION: 'action',
  PROGRESS: 'progress'
};

// File Upload Types
export const ALLOWED_FILE_TYPES = {
  IMAGES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  DOCUMENTS: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  ALL: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
};

// Maximum File Sizes (in bytes)
export const MAX_FILE_SIZES = {
  PROFILE_IMAGE: 5 * 1024 * 1024, // 5MB
  DOCUMENT: 10 * 1024 * 1024, // 10MB
  MEDICAL_RECORD: 20 * 1024 * 1024 // 20MB
};

// API Endpoints (for future use)
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password'
  },
  USERS: {
    PROFILE: '/users/profile',
    UPDATE_PROFILE: '/users/profile',
    UPLOAD_AVATAR: '/users/avatar'
  },
  APPOINTMENTS: {
    LIST: '/appointments',
    CREATE: '/appointments',
    UPDATE: '/appointments/:id',
    DELETE: '/appointments/:id',
    AVAILABLE_SLOTS: '/appointments/available-slots'
  },
  MEDICATIONS: {
    LIST: '/medications',
    CREATE: '/medications',
    UPDATE: '/medications/:id',
    DELETE: '/medications/:id'
  },
  VITALS: {
    LIST: '/vitals',
    CREATE: '/vitals',
    UPDATE: '/vitals/:id',
    DELETE: '/vitals/:id'
  }
};

// Local Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'neurocare_auth_token',
  USER_PREFERENCES: 'neurocare_user_preferences',
  THEME_MODE: 'neurocare_theme_mode',
  LANGUAGE: 'neurocare_language',
  NOTIFICATION_SETTINGS: 'neurocare_notification_settings'
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your internet connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  UNKNOWN_ERROR: 'An unexpected error occurred.'
};

// Success Messages
export const SUCCESS_MESSAGES = {
  PROFILE_UPDATED: 'Profile updated successfully!',
  APPOINTMENT_CREATED: 'Appointment scheduled successfully!',
  APPOINTMENT_UPDATED: 'Appointment updated successfully!',
  APPOINTMENT_CANCELLED: 'Appointment cancelled successfully!',
  MEDICATION_ADDED: 'Medication added successfully!',
  VITAL_RECORDED: 'Vital signs recorded successfully!',
  PASSWORD_CHANGED: 'Password changed successfully!',
  EMAIL_SENT: 'Email sent successfully!'
};

// Breakpoints for Responsive Design
export const BREAKPOINTS = {
  MOBILE: 480,
  TABLET: 768,
  DESKTOP: 1024,
  LARGE: 1200
};

// Animation Durations
export const ANIMATION_DURATIONS = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500
};

// Z-Index Values
export const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070
};

// Default Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [5, 10, 20, 50, 100]
};

// Date Formats
export const DATE_FORMATS = {
  SHORT: 'MM/DD/YYYY',
  LONG: 'MMMM DD, YYYY',
  TIME: 'HH:mm',
  DATETIME: 'MM/DD/YYYY HH:mm',
  ISO: 'YYYY-MM-DDTHH:mm:ss.sssZ'
};
