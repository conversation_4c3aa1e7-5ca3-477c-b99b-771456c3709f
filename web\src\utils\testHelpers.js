// Test helpers and utilities for NeuroCare web application

import { render } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '../contexts/ThemeContext';
import { AuthProvider } from '../contexts/AuthContext';

/**
 * Custom render function with providers
 * @param {React.Component} ui - Component to render
 * @param {object} options - Render options
 * @returns {object} Render result
 */
export const renderWithProviders = (ui, options = {}) => {
  const {
    initialEntries = ['/'],
    user = null,
    ...renderOptions
  } = options;

  const Wrapper = ({ children }) => (
    <BrowserRouter>
      <ThemeProvider>
        <AuthProvider initialUser={user}>
          {children}
        </AuthProvider>
      </ThemeProvider>
    </BrowserRouter>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

/**
 * Mock user data for different roles
 */
export const mockUsers = {
  patient: {
    uid: 'patient-123',
    email: '<EMAIL>',
    displayName: '<PERSON>',
    role: 'patient',
    userCode: 'PAT12345',
    profileComplete: true
  },
  doctor: {
    uid: 'doctor-123',
    email: '<EMAIL>',
    displayName: 'Dr. Sarah Doctor',
    role: 'doctor',
    userCode: 'DOC12345',
    profileComplete: true
  },
  admin: {
    uid: 'admin-123',
    email: '<EMAIL>',
    displayName: 'Admin User',
    role: 'admin',
    userCode: 'ADM12345',
    profileComplete: true
  },
  caregiver: {
    uid: 'caregiver-123',
    email: '<EMAIL>',
    displayName: 'Care Giver',
    role: 'caregiver',
    userCode: 'CAR12345',
    profileComplete: true
  },
  supervisor: {
    uid: 'supervisor-123',
    email: '<EMAIL>',
    displayName: 'Super Visor',
    role: 'supervisor',
    userCode: 'SUP12345',
    profileComplete: true
  }
};

/**
 * Mock appointment data
 */
export const mockAppointments = [
  {
    id: 'apt-1',
    patientId: 'patient-123',
    patientName: 'John Patient',
    doctorId: 'doctor-123',
    doctorName: 'Dr. Sarah Doctor',
    appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
    type: 'consultation',
    status: 'scheduled',
    reason: 'Regular checkup',
    location: 'NeuroCare Clinic',
    notes: 'Patient reports feeling well'
  },
  {
    id: 'apt-2',
    patientId: 'patient-123',
    patientName: 'John Patient',
    doctorId: 'doctor-123',
    doctorName: 'Dr. Sarah Doctor',
    appointmentDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // Last week
    type: 'follow_up',
    status: 'completed',
    reason: 'Follow-up consultation',
    location: 'NeuroCare Clinic'
  }
];

/**
 * Mock vital signs data
 */
export const mockVitals = [
  {
    id: 'vital-1',
    patientId: 'patient-123',
    values: {
      blood_pressure_systolic: 120,
      blood_pressure_diastolic: 80,
      heart_rate: 72,
      temperature: 36.5,
      oxygen_saturation: 98
    },
    recordedAt: new Date().toISOString(),
    recordedBy: 'patient-123',
    notes: 'Feeling good today'
  },
  {
    id: 'vital-2',
    patientId: 'patient-123',
    values: {
      blood_pressure_systolic: 125,
      blood_pressure_diastolic: 82,
      heart_rate: 75,
      temperature: 36.7
    },
    recordedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Yesterday
    recordedBy: 'patient-123'
  }
];

/**
 * Mock prescription data
 */
export const mockPrescriptions = [
  {
    id: 'presc-1',
    patientId: 'patient-123',
    doctorId: 'doctor-123',
    doctorName: 'Dr. Sarah Doctor',
    prescribedDate: new Date().toISOString(),
    status: 'sent',
    medications: [
      {
        name: 'Aspirin',
        dosage: '100mg',
        frequency: 'Once daily',
        duration: '30 days',
        instructions: 'Take with food'
      },
      {
        name: 'Vitamin D',
        dosage: '1000 IU',
        frequency: 'Once daily',
        duration: '90 days'
      }
    ],
    notes: 'Continue current medication regimen'
  }
];

/**
 * Mock medication data
 */
export const mockMedications = [
  {
    id: 'med-1',
    patientId: 'patient-123',
    name: 'Aspirin',
    dosage: '100mg',
    frequency: 'Once daily',
    status: 'active',
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    prescribedBy: 'Dr. Sarah Doctor',
    instructions: 'Take with food'
  }
];

/**
 * Mock notification data
 */
export const mockNotifications = [
  {
    id: 'notif-1',
    userId: 'patient-123',
    title: 'Appointment Reminder',
    message: 'You have an appointment tomorrow at 2:00 PM',
    type: 'appointment',
    priority: 'high',
    read: false,
    createdAt: new Date().toISOString(),
    data: {
      appointmentId: 'apt-1'
    }
  },
  {
    id: 'notif-2',
    userId: 'patient-123',
    title: 'Medication Reminder',
    message: 'Time to take your Aspirin',
    type: 'medication',
    priority: 'medium',
    read: true,
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    data: {
      medicationId: 'med-1'
    }
  }
];

/**
 * Mock Firebase functions
 */
export const mockFirebase = {
  auth: {
    currentUser: null,
    signInWithEmailAndPassword: jest.fn(),
    createUserWithEmailAndPassword: jest.fn(),
    signOut: jest.fn(),
    sendPasswordResetEmail: jest.fn()
  },
  firestore: {
    collection: jest.fn(() => ({
      doc: jest.fn(() => ({
        get: jest.fn(),
        set: jest.fn(),
        update: jest.fn(),
        delete: jest.fn()
      })),
      add: jest.fn(),
      where: jest.fn(),
      orderBy: jest.fn(),
      limit: jest.fn(),
      get: jest.fn()
    }))
  }
};

/**
 * Wait for async operations to complete
 * @param {number} ms - Milliseconds to wait
 * @returns {Promise} Promise that resolves after the specified time
 */
export const waitFor = (ms = 0) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Create mock form event
 * @param {string} name - Input name
 * @param {any} value - Input value
 * @returns {object} Mock event object
 */
export const createMockEvent = (name, value) => ({
  target: { name, value },
  preventDefault: jest.fn(),
  stopPropagation: jest.fn()
});

/**
 * Mock local storage
 */
export const mockLocalStorage = (() => {
  let store = {};

  return {
    getItem: jest.fn((key) => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn((key) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    })
  };
})();

/**
 * Mock window.matchMedia for responsive tests
 */
export const mockMatchMedia = (matches = false) => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches,
      media: query,
      onchange: null,
      addListener: jest.fn(), // deprecated
      removeListener: jest.fn(), // deprecated
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });
};

/**
 * Mock IntersectionObserver for infinite scroll tests
 */
export const mockIntersectionObserver = () => {
  const mockIntersectionObserver = jest.fn();
  mockIntersectionObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null
  });
  window.IntersectionObserver = mockIntersectionObserver;
  window.IntersectionObserverEntry = jest.fn();
};

/**
 * Test data generators
 */
export const generateTestData = {
  user: (overrides = {}) => ({
    uid: `user-${Date.now()}`,
    email: '<EMAIL>',
    displayName: 'Test User',
    role: 'patient',
    userCode: 'TEST123',
    profileComplete: true,
    ...overrides
  }),

  appointment: (overrides = {}) => ({
    id: `apt-${Date.now()}`,
    patientId: 'patient-123',
    doctorId: 'doctor-123',
    appointmentDate: new Date().toISOString(),
    type: 'consultation',
    status: 'scheduled',
    reason: 'Test appointment',
    ...overrides
  }),

  vitalSigns: (overrides = {}) => ({
    id: `vital-${Date.now()}`,
    patientId: 'patient-123',
    values: {
      blood_pressure_systolic: 120,
      blood_pressure_diastolic: 80,
      heart_rate: 72
    },
    recordedAt: new Date().toISOString(),
    ...overrides
  })
};

/**
 * Common test assertions
 */
export const commonAssertions = {
  expectElementToBeVisible: (element) => {
    expect(element).toBeInTheDocument();
    expect(element).toBeVisible();
  },

  expectElementToHaveText: (element, text) => {
    expect(element).toBeInTheDocument();
    expect(element).toHaveTextContent(text);
  },

  expectFormToBeValid: (form) => {
    expect(form).toBeInTheDocument();
    expect(form).toBeValid();
  },

  expectButtonToBeEnabled: (button) => {
    expect(button).toBeInTheDocument();
    expect(button).toBeEnabled();
  },

  expectButtonToBeDisabled: (button) => {
    expect(button).toBeInTheDocument();
    expect(button).toBeDisabled();
  }
};

export default {
  renderWithProviders,
  mockUsers,
  mockAppointments,
  mockVitals,
  mockPrescriptions,
  mockMedications,
  mockNotifications,
  mockFirebase,
  waitFor,
  createMockEvent,
  mockLocalStorage,
  mockMatchMedia,
  mockIntersectionObserver,
  generateTestData,
  commonAssertions
};
