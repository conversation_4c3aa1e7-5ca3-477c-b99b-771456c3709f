// Validation utility functions for the NeuroCare web application

/**
 * Validate email address
 * @param {string} email - Email to validate
 * @returns {object} Validation result with isValid and message
 */
export const validateEmail = (email) => {
  if (!email) {
    return { isValid: false, message: 'Em<PERSON> is required' };
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (!emailRegex.test(email)) {
    return { isValid: false, message: 'Please enter a valid email address' };
  }

  return { isValid: true, message: '' };
};

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {object} Validation result with isValid, message, and strength
 */
export const validatePassword = (password) => {
  if (!password) {
    return { isValid: false, message: 'Password is required', strength: 'none' };
  }

  if (password.length < 6) {
    return { isValid: false, message: 'Password must be at least 6 characters long', strength: 'weak' };
  }

  let strength = 'weak';
  let score = 0;

  // Check for different character types
  if (password.length >= 8) score++;
  if (/[a-z]/.test(password)) score++;
  if (/[A-Z]/.test(password)) score++;
  if (/[0-9]/.test(password)) score++;
  if (/[^A-Za-z0-9]/.test(password)) score++;

  if (score >= 4) strength = 'strong';
  else if (score >= 3) strength = 'medium';

  return {
    isValid: true,
    message: '',
    strength,
    score
  };
};

/**
 * Validate password confirmation
 * @param {string} password - Original password
 * @param {string} confirmPassword - Password confirmation
 * @returns {object} Validation result
 */
export const validatePasswordConfirmation = (password, confirmPassword) => {
  if (!confirmPassword) {
    return { isValid: false, message: 'Please confirm your password' };
  }

  if (password !== confirmPassword) {
    return { isValid: false, message: 'Passwords do not match' };
  }

  return { isValid: true, message: '' };
};

/**
 * Validate phone number
 * @param {string} phone - Phone number to validate
 * @returns {object} Validation result
 */
export const validatePhone = (phone) => {
  if (!phone) {
    return { isValid: false, message: 'Phone number is required' };
  }

  // Remove all non-digit characters
  const cleanPhone = phone.replace(/\D/g, '');

  if (cleanPhone.length < 10) {
    return { isValid: false, message: 'Phone number must be at least 10 digits' };
  }

  if (cleanPhone.length > 15) {
    return { isValid: false, message: 'Phone number is too long' };
  }

  return { isValid: true, message: '' };
};

/**
 * Validate date of birth
 * @param {string} dateOfBirth - Date of birth to validate
 * @returns {object} Validation result
 */
export const validateDateOfBirth = (dateOfBirth) => {
  if (!dateOfBirth) {
    return { isValid: false, message: 'Date of birth is required' };
  }

  const birthDate = new Date(dateOfBirth);
  const today = new Date();

  if (isNaN(birthDate.getTime())) {
    return { isValid: false, message: 'Please enter a valid date' };
  }

  if (birthDate > today) {
    return { isValid: false, message: 'Date of birth cannot be in the future' };
  }

  const age = today.getFullYear() - birthDate.getFullYear();
  if (age > 150) {
    return { isValid: false, message: 'Please enter a valid date of birth' };
  }

  return { isValid: true, message: '' };
};

/**
 * Validate required field
 * @param {string} value - Value to validate
 * @param {string} fieldName - Name of the field for error message
 * @returns {object} Validation result
 */
export const validateRequired = (value, fieldName = 'This field') => {
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return { isValid: false, message: `${fieldName} is required` };
  }

  return { isValid: true, message: '' };
};

/**
 * Validate name (first name, last name, etc.)
 * @param {string} name - Name to validate
 * @param {string} fieldName - Name of the field for error message
 * @returns {object} Validation result
 */
export const validateName = (name, fieldName = 'Name') => {
  const requiredCheck = validateRequired(name, fieldName);
  if (!requiredCheck.isValid) {
    return requiredCheck;
  }

  if (name.length < 2) {
    return { isValid: false, message: `${fieldName} must be at least 2 characters long` };
  }

  if (name.length > 50) {
    return { isValid: false, message: `${fieldName} must be less than 50 characters` };
  }

  // Check for valid characters (letters, spaces, hyphens, apostrophes)
  const nameRegex = /^[a-zA-Z\s\-']+$/;
  if (!nameRegex.test(name)) {
    return { isValid: false, message: `${fieldName} can only contain letters, spaces, hyphens, and apostrophes` };
  }

  return { isValid: true, message: '' };
};

/**
 * Validate medical record number
 * @param {string} recordNumber - Medical record number to validate
 * @returns {object} Validation result
 */
export const validateMedicalRecordNumber = (recordNumber) => {
  if (!recordNumber) {
    return { isValid: false, message: 'Medical record number is required' };
  }

  // Remove spaces and hyphens
  const cleanNumber = recordNumber.replace(/[\s\-]/g, '');

  if (cleanNumber.length < 6) {
    return { isValid: false, message: 'Medical record number must be at least 6 characters' };
  }

  if (cleanNumber.length > 20) {
    return { isValid: false, message: 'Medical record number is too long' };
  }

  // Check for alphanumeric characters only
  const recordRegex = /^[a-zA-Z0-9]+$/;
  if (!recordRegex.test(cleanNumber)) {
    return { isValid: false, message: 'Medical record number can only contain letters and numbers' };
  }

  return { isValid: true, message: '' };
};

/**
 * Validate vital signs values
 * @param {string} type - Type of vital sign
 * @param {string|number} value - Value to validate
 * @returns {object} Validation result
 */
export const validateVitalSign = (type, value) => {
  if (!value && value !== 0) {
    return { isValid: false, message: 'Value is required' };
  }

  const numValue = parseFloat(value);

  if (isNaN(numValue)) {
    return { isValid: false, message: 'Please enter a valid number' };
  }

  const ranges = {
    'blood_pressure_systolic': { min: 50, max: 300, unit: 'mmHg' },
    'blood_pressure_diastolic': { min: 30, max: 200, unit: 'mmHg' },
    'heart_rate': { min: 30, max: 250, unit: 'bpm' },
    'temperature': { min: 30, max: 45, unit: '°C' },
    'oxygen_saturation': { min: 50, max: 100, unit: '%' },
    'blood_sugar': { min: 20, max: 600, unit: 'mg/dL' },
    'weight': { min: 1, max: 1000, unit: 'kg' },
    'height': { min: 30, max: 300, unit: 'cm' }
  };

  const range = ranges[type];
  if (!range) {
    return { isValid: true, message: '' }; // Unknown type, assume valid
  }

  if (numValue < range.min || numValue > range.max) {
    return {
      isValid: false,
      message: `Value must be between ${range.min} and ${range.max} ${range.unit}`
    };
  }

  return { isValid: true, message: '' };
};

/**
 * Validate medication dosage
 * @param {string} dosage - Dosage to validate
 * @returns {object} Validation result
 */
export const validateMedicationDosage = (dosage) => {
  if (!dosage) {
    return { isValid: false, message: 'Dosage is required' };
  }

  // Basic dosage format validation (number + unit)
  const dosageRegex = /^\d+(\.\d+)?\s*(mg|g|ml|l|units?|iu|mcg|µg)\s*$/i;

  if (!dosageRegex.test(dosage.trim())) {
    return {
      isValid: false,
      message: 'Please enter a valid dosage (e.g., "500mg", "2.5ml", "1 unit")'
    };
  }

  return { isValid: true, message: '' };
};

/**
 * Validate form data using multiple validators
 * @param {object} data - Form data to validate
 * @param {object} validators - Object with field names as keys and validator functions as values
 * @returns {object} Validation results with errors object and isValid boolean
 */
export const validateForm = (data, validators) => {
  const errors = {};
  let isValid = true;

  Object.keys(validators).forEach(field => {
    const validator = validators[field];
    const value = data[field];

    let result;
    if (typeof validator === 'function') {
      result = validator(value);
    } else if (Array.isArray(validator)) {
      // Multiple validators for one field
      for (const v of validator) {
        result = v(value);
        if (!result.isValid) break;
      }
    }

    if (result && !result.isValid) {
      errors[field] = result.message;
      isValid = false;
    }
  });

  return { errors, isValid };
};

/**
 * Format phone number for display
 * @param {string} phone - Phone number to format
 * @returns {string} Formatted phone number
 */
export const formatPhoneNumber = (phone) => {
  if (!phone) return '';

  const cleanPhone = phone.replace(/\D/g, '');

  if (cleanPhone.length === 10) {
    return `(${cleanPhone.slice(0, 3)}) ${cleanPhone.slice(3, 6)}-${cleanPhone.slice(6)}`;
  }

  if (cleanPhone.length === 11 && cleanPhone[0] === '1') {
    return `+1 (${cleanPhone.slice(1, 4)}) ${cleanPhone.slice(4, 7)}-${cleanPhone.slice(7)}`;
  }

  return phone; // Return original if can't format
};

/**
 * Sanitize input to prevent XSS
 * @param {string} input - Input to sanitize
 * @returns {string} Sanitized input
 */
export const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;

  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};
