// Map of voice commands to navigation destinations
const PATIENT_NAVIGATION_COMMANDS = {
  // Basic navigation
  'dashboard': '/dashboard',
  'home': '/dashboard',
  'go home': '/dashboard',
  'main screen': '/dashboard',

  // Health records
  'health records': '/vitals',
  'my records': '/vitals',
  'medical records': '/vitals',
  'show records': '/vitals',
  'open records': '/vitals',

  // Appointments
  'appointments': '/appointments',
  'my appointments': '/appointments',
  'show appointments': '/appointments',
  'view appointments': '/appointments',
  'schedule': '/appointments',

  // Maps and Locations
  'map': '/map',
  'maps': '/map',
  'my locations': '/map',
  'locations': '/map',
  'saved locations': '/map',
  'show map': '/map',
  'open map': '/map',

  // Medications
  'medications': '/prescriptions',
  'my medications': '/prescriptions',
  'show medications': '/prescriptions',
  'view medications': '/prescriptions',
  'medicine': '/prescriptions',
  'pills': '/prescriptions',

  // Record vitals
  'record vitals': '/vitals/record',
  'vitals': '/vitals',
  'check vitals': '/vitals',
  'log vitals': '/vitals/record',

  // Profile
  'profile': '/profile',
  'my profile': '/profile',
  'account': '/profile',
  'settings': '/settings',

  // QR Code
  'qr code': '/profile/qr',
  'my qr code': '/profile/qr',
  'show qr code': '/profile/qr',

  // Messages
  'messages': '/messages',
  'chat': '/messages',
  'conversations': '/messages',

  // Notifications
  'notifications': '/notifications',
  'alerts': '/notifications',
};

// Check if browser supports speech recognition
const isSpeechRecognitionSupported = () => {
  return 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
};

// Request microphone permissions
const requestMicrophonePermission = async () => {
  try {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      console.error('getUserMedia not supported');
      return false;
    }

    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    // Stop the stream immediately as we just wanted to check permissions
    stream.getTracks().forEach(track => track.stop());
    return true;
  } catch (error) {
    console.error('Error requesting microphone permission:', error);
    return false;
  }
};

// Text-to-speech function
const speak = (text, options = {}) => {
  if (!('speechSynthesis' in window)) {
    console.warn('Speech synthesis not supported');
    return;
  }

  const utterance = new SpeechSynthesisUtterance(text);
  utterance.rate = options.rate || 1.0;
  utterance.pitch = options.pitch || 1.0;
  utterance.volume = options.volume || 1.0;
  
  if (options.onDone) {
    utterance.onend = options.onDone;
  }
  
  if (options.onError) {
    utterance.onerror = options.onError;
  }

  speechSynthesis.speak(utterance);
};

// Start voice recording using Web Speech API
const startVoiceRecording = async () => {
  try {
    if (!isSpeechRecognitionSupported()) {
      throw new Error('Speech recognition not supported');
    }

    // Check microphone permissions
    const hasPermission = await requestMicrophonePermission();
    if (!hasPermission) {
      throw new Error('Microphone permission denied');
    }

    // Create speech recognition instance
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();
    
    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.lang = 'en-US';

    // Provide audio feedback that we're listening
    speak("Listening for navigation command", {
      rate: 1.0,
      onDone: () => console.log("Voice prompt completed"),
      onError: (error) => console.error("Voice prompt error:", error)
    });

    return recognition;
  } catch (error) {
    console.error('Failed to start voice recognition:', error);
    return null;
  }
};

// Stop voice recording and process the command
const stopVoiceRecording = async (recognition, onResult) => {
  if (!recognition) {
    onResult({
      success: false,
      error: 'No recognition instance available'
    });
    return;
  }

  try {
    recognition.onresult = (event) => {
      const transcript = event.results[0][0].transcript.toLowerCase().trim();
      console.log('Voice command received:', transcript);
      
      const destination = processVoiceCommand(transcript);
      
      if (destination) {
        onResult({
          success: true,
          command: transcript,
          destination: destination
        });
        
        speak(`Navigating to ${transcript}`, {
          onDone: () => console.log("Navigation confirmation completed")
        });
      } else {
        onResult({
          success: false,
          error: 'Command not recognized'
        });
        
        speak("Sorry, I didn't understand that command. Please try again.", {
          onError: (error) => console.error("Error feedback error:", error)
        });
      }
    };

    recognition.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
      onResult({
        success: false,
        error: `Speech recognition error: ${event.error}`
      });
    };

    recognition.onend = () => {
      console.log('Speech recognition ended');
    };

    // Start listening
    recognition.start();
  } catch (error) {
    console.error('Failed to process voice recording:', error);
    onResult({
      success: false,
      error: 'Failed to process voice command'
    });
  }
};

// Process a voice command string and return the navigation destination
const processVoiceCommand = (commandText) => {
  if (!commandText) return null;

  // Convert to lowercase for case-insensitive matching
  const normalizedCommand = commandText.toLowerCase().trim();

  // Check if the command matches any of our navigation commands
  for (const [command, destination] of Object.entries(PATIENT_NAVIGATION_COMMANDS)) {
    if (normalizedCommand.includes(command.toLowerCase())) {
      return destination;
    }
  }

  // Check for partial matches
  for (const [command, destination] of Object.entries(PATIENT_NAVIGATION_COMMANDS)) {
    const commandWords = command.toLowerCase().split(' ');
    const inputWords = normalizedCommand.split(' ');
    
    // Check if at least half of the command words are present
    const matchingWords = commandWords.filter(word => 
      inputWords.some(inputWord => inputWord.includes(word) || word.includes(inputWord))
    );
    
    if (matchingWords.length >= Math.ceil(commandWords.length / 2)) {
      return destination;
    }
  }

  // No matching command found
  return null;
};

// Get available voice commands for help
const getAvailableCommands = () => {
  return Object.keys(PATIENT_NAVIGATION_COMMANDS);
};

// Check if voice navigation is available
const isVoiceNavigationAvailable = () => {
  return isSpeechRecognitionSupported() && 'speechSynthesis' in window;
};

export {
  PATIENT_NAVIGATION_COMMANDS,
  requestMicrophonePermission,
  startVoiceRecording,
  stopVoiceRecording,
  processVoiceCommand,
  speak,
  getAvailableCommands,
  isVoiceNavigationAvailable,
  isSpeechRecognitionSupported
};
