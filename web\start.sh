#!/bin/bash

echo "========================================"
echo "   NeuroCare Web Application Setup"
echo "========================================"
echo

echo "Checking if Node.js is installed..."
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo "Node.js is installed: $(node --version)"

echo
echo "Checking if npm is available..."
if ! command -v npm &> /dev/null; then
    echo "ERROR: npm is not available"
    exit 1
fi

echo "npm is available: $(npm --version)"

echo
echo "Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install dependencies"
    exit 1
fi

echo
echo "========================================"
echo "  Dependencies installed successfully!"
echo "========================================"
echo

echo "Starting NeuroCare Web Application..."
echo
echo "The application will open in your default browser at:"
echo "http://localhost:3000"
echo
echo "Press Ctrl+C to stop the development server"
echo

npm start
